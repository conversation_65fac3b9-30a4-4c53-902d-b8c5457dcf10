<?php

namespace App\Livewire\Admin\Contract;

use App\Enums\ContractStatusEnum;
use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\CustomerRepository;
use App\Services\ApplicationService;
use App\Services\ApplicationCourseService;
use App\Services\ToastService;
use Gate;

class Show extends BaseAdminPageComponent
{
    public $id;
    public $isCanDelete;

    protected $contract = null;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CONTRACT;
        parent::__construct();
        $subTitle = trans2('screens.contract.detail.page_title');
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle = $this->pageTitle . $subTitle . $suffixTitle;
    }

    public function mount($id)
    {
        $this->id = $id;
        $this->contract = app()->make(ApplicationService::class)->getContractByApplication($id);

        if (empty($this->contract)) {
            app(ToastService::class)->error(__('messages.no_data'));
            return $this->redirect(route('admin.contract.index'));
        }

        $this->brandIds = collect($this->contract->brand_id);
        $this->shopIds = collect($this->contract->shop_id);
        Gate::authorize('allowed-update', [SidebarMenuEnum::CONTRACT, $this->brandIds, $this->shopIds]);

        $this->isCanDelete = $this->isCanDelete();
    }

    protected function isCanDelete()
    {
        if ($this->contract->contract_status) {
            return in_array($this->contract->contract_status->value, ContractStatusEnum::canDeleteStatus());
        }
        return true;
    }

    public function render()
    {
        return $this->viewLivewireAdmin('contract.detail.show', [
            'contract' => $this->contract,
        ]);
    }
}
