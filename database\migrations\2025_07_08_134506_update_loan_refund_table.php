<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('loan_refunds', function (Blueprint $table) {
            $table->integer('loan_withdrawal_id')->comment('ローン出金ID')->index()->after('loan_payment_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('loan_refunds', function (Blueprint $table) {
            $table->dropColumn('loan_withdrawal_id');
        });
    }
};
