<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->decimal('fee_total_amount', 12, 2)->nullable()->comment('分割支払金合計（残金＋分割払手数料）')->change();
            $table->decimal('total_amount', 12, 2)->nullable()->comment('支払総額（頭金＋分割支払金合計）')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->decimal('fee_total_amount', 10, 2)->nullable()->comment('分割支払金合計（残金＋分割払手数料）')->change();
            $table->decimal('total_amount', 10, 2)->nullable()->comment('支払総額（頭金＋分割支払金合計）')->change();
        });
    }
};
