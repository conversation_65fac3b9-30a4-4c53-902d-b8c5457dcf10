<div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
    <table class="table table-datalist w-100">
        <thead>
            <tr>
                <th scope="col" class="empty"></th>
                <th scope="col">{{ trans2('screens.dashboard.total_contracts') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.new_contracts') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.new_contract_amount') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.completed_contracts') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.cancelled_contracts') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.cancelled_amount') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.forced_cancelled_contracts') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.forced_contract_cancel_amount') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.uncollected_contracts') }}</th>
                <th scope="col">{{ trans2('screens.dashboard.uncollected_amount') }}</th>
            </tr>
        </thead>
        <tbody>
            @if($dataList->isNotEmpty())
                @foreach ($dataList as $data)
                    <tr>
                        <th>{{ formatDottedYearMonth(data_get($data,'target_month')) }}</th>
                        <td>{{ number_format(data_get($data,'total_contracts')) }}</td>
                        <td>{{ number_format(data_get($data,'new_contracts')) }}</td>
                        <td><span class="me-1">&yen;</span>{{ number_format(data_get($data,'new_contract_amount')) }}</td>
                        <td>{{ number_format(data_get($data,'completed_contracts')) }}</td>
                        <td>{{ number_format(data_get($data,'cancelled_contracts')) }}</td>
                        <td><span class="me-1">&yen;</span>{{ number_format(data_get($data,'cancelled_amount')) }}</td>
                        <td>{{ number_format(data_get($data,'forced_cancelled_contracts')) }}</td>
                        <td><span class="me-1">&yen;</span>{{ number_format(data_get($data,'forced_contract_cancel_amount')) }}</td>
                        <td class="bg-caution">{{ number_format(data_get($data,'uncollected_contracts')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($data,'uncollected_amount')) }}</td>
                    </tr>
                @endforeach
            @endif    
        </tbody>
    </table>
    @if ($dataList->isEmpty())
        @include('components.no-result-found')
    @endif
</div>
