<div x-data="inputPayment">
    <h1 class="application-page-title">1.{{ trans2('screens.application.brand.title') }}</h1>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">{{ trans2('screens.application.brand.brand_name') }}<span class="required-icon">{{
                        trans2('required') }}</span></th>
                <td>
                    <div wire:ignore style="max-width: 767px !important;">
                        <select name="" wire:model="saveForm.shop_brand_id"
                            class="form-select2 form-required shop-brand-id-selector"
                            data-placeholder="{{ trans2('select_default') }}" style="width:100%;">
                            <option value="" selected></option>
                            @foreach($brandShopList as $item)
                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    @error('saveForm.shop_brand_id')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="required">{{ trans2('screens.application.brand.staff_name') }}<span class="required-icon">{{
                        trans2('required') }}</span></th>
                <td>
                    <input type="text" wire:model="saveForm.staff_name" name="" value=""
                        class="form-control form-required w-px-300">
                    @error('saveForm.staff_name')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
        </tbody>
    </table>
    <h2 class="heading-2">{{ trans2('screens.application.brand.type_and_product_format') }}</h2>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">{{ trans2('screens.application.brand.fee_type') }}<span class="required-icon">{{
                        trans2('required') }}</span></th>
                <td>
                    <div class="d-flex">
                        <div class="form-radio me-5">
                            <label>
                                <input wire:model="saveForm.fee_type" wire:change="clearProductSelections" type="radio"
                                    name="fee_type" value="1" class="form-radio-input">
                                <span class="form-radio-text">{{ trans2('screens.application.brand.fee_type_self_paid')
                                    }}</span>
                            </label>
                        </div>
                        <div class="form-radio">
                            <label>
                                <input wire:model="saveForm.fee_type" wire:change="clearProductSelections" type="radio"
                                    name="fee_type" value="2" class="form-radio-input">
                                <span class="form-radio-text">{{ trans2('screens.application.brand.fee_type_standard')
                                    }}</span>
                            </label>
                        </div>
                    </div>
                    @error('saveForm.fee_type')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="align-top required">
                    {{ trans2('screens.application.brand.general_product') }}<span class="required-icon">{{
                        trans2('required') }}</span>
                </th>
                <td>
                    <div class="form-parts-group" id="brandItemsGroup">
                        @foreach($productGeneral as $index => $item)
                        <div wire:key="product_general_{{ $item['application_course_id'] ?? $index }}"
                            class="form-parts d-flex pt-2 pb-2 align-items-end" data-parts-id="{{ $index }}">
                            <div class="row align-items-start flex-grow-1">
                                <div class="col-auto flex-grow-1">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.product_name') }}
                                    </div>
                                    <div>
                                        <select wire:model="saveForm.product_general.{{ $index }}.course_id"
                                            class="form-select2 flex-grow-1 course-general-selector limit-select-w454"
                                            style="width:100%;" data-placeholder="{{ trans2('select_default') }}">
                                            <option value="" selected></option>
                                            @foreach($courseGeneralList as $course)
                                            <option value="{{ $course['id'] }}" wire:key="course_general_{{ $course['id'] }}"
                                                data-unit-price="{{ $course['unit_price'] }}">{{
                                                $course['name_application']
                                                ?? $course['name_management'] }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.quantity') }}</div>
                                    <div class="js-qty">
                                        <button type="button" class="js-qty__adjust js-qty__adjust--minus"
                                            wire:click="decrementQtyGeneral({{ $index }})"
                                            @disabled($item['quantity']==0)>
                                            <span aria-hidden="true">−</span>
                                            <span class="u-hidden-visually">{{
                                                trans2('screens.application.brand.sub_one') }}</span>
                                        </button>
                                        <input type="number" class="js-qty__num form-control"
                                            wire:model="saveForm.product_general.{{ $index }}.quantity" min="0"
                                            aria-label="quantity" pattern="[0-9]*" name="" step="1">
                                        <button type="button" class="js-qty__adjust js-qty__adjust--plus"
                                            wire:click="incrementQtyGeneral({{ $index }})">
                                            <span aria-hidden="true">+</span>
                                            <span class="u-hidden-visually">{{
                                                trans2('screens.application.brand.plus_one') }}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.amount') }}</div>
                                    <div class="form-parts-unit">
                                        <div class="form-unit">&yen;</div>
                                        <input type="text" name=""
                                            wire:model="saveForm.product_general.{{ $index }}.amount"
                                            class="form-control w-px-120" pattern="[0-9]*" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="ms-4">
                                @if ($loop->last)
                                <button type="button" class="btn btn-add" wire:click.prevent="addGeneral()"></button>
                                @else
                                <button type="button" class="btn btn-add form-delete-button delete-form-parts"
                                    wire:click.prevent="removeGeneral({{$index}})"></button>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div>
                                @error("saveForm.product_general")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_general.$index.course_id")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_general.$index.quantity")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_general.$index.amount")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        @endforeach
                    </div>
                </td>
            </tr>
            <tr>
                <th class="align-top">{{ trans2('screens.application.brand.optional_product') }}</th>
                <td>
                    <div class="form-parts-group" id="brandOptionGroup">
                        @foreach($productOptional as $index => $item)
                        <div wire:key="product_optional_{{ $item['application_course_id'] ?? $index }}"
                            class="form-parts d-flex pt-2 pb-2 align-items-end" data-parts-id="101">
                            <div class="row align-items-start flex-grow-1">
                                <div class="col-auto flex-grow-1">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.product_name') }}</div>
                                    <div>
                                        <select wire:model="saveForm.product_optional.{{ $index }}.course_id"
                                            class="form-select2 flex-grow-1 course-optional-selector limit-select-w454"
                                            style="width:100%;" data-placeholder="{{ trans2('select_default') }}">
                                            <option value="" selected></option>
                                            @foreach($courseOptionalList as $course)
                                            <option value="{{ $course['id'] }}" wire:key="course_optional_{{ $course['id'] }}"
                                                data-unit-price="{{ $course['unit_price'] }}">{{
                                                $course['name_application']
                                                ?? $course['name_management'] }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.quantity') }}</div>
                                    <div class="js-qty">
                                        <button type="button" class="js-qty__adjust js-qty__adjust--minus"
                                            wire:click="decrementQtyOptional({{ $index }})"
                                            @disabled($item['quantity']==0)>
                                            <span aria-hidden="true">−</span>
                                            <span class="u-hidden-visually">{{
                                                trans2('screens.application.brand.sub_one') }}</span>
                                        </button>
                                        <input type="number" class="js-qty__num form-control"
                                            wire:model="saveForm.product_optional.{{ $index }}.quantity" min="0"
                                            aria-label="quantity" pattern="[0-9]*" name="" step="1">
                                        <button type="button" class="js-qty__adjust js-qty__adjust--plus"
                                            wire:click="incrementQtyOptional({{ $index }})">
                                            <span aria-hidden="true">+</span>
                                            <span class="u-hidden-visually">{{
                                                trans2('screens.application.brand.plus_one') }}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.amount') }}</div>
                                    <div class="form-parts-unit">
                                        <div class="form-unit">&yen;</div>
                                        <input type="text" max="99999999.99"
                                            wire:model="saveForm.product_optional.{{ $index }}.amount" name="deposit"
                                            class="form-control w-px-120" x-data x-ref="incomeInput"
                                            x-on:keydown="handleDepositKeyDown" x-on:input="handleDepositInput"
                                            id="inputIncome" />
                                    </div>
                                </div>
                            </div>
                            <div class="ms-4">
                                @if ($loop->last)
                                <button type="button" class="btn btn-add" wire:click.prevent="addOptional()"></button>
                                @else
                                <button type="button" class="btn btn-add form-delete-button delete-form-parts"
                                    wire:click.prevent="removeOptional({{$index}})"></button>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div>
                                @error("saveForm.product_optional.$index.course_id")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_optional.$index.quantity")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_optional.$index.amount")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        @endforeach
                    </div>
                </td>
            </tr>
            <tr>
                <th>{{ trans2('screens.application.brand.total') }}</th>
                <td>
                    <div class="d-flex">
                        <div class="me-4">{{ trans2('screens.application.brand.total_payment') }}</div>
                        <div class="ms-auto"><span class="me-1">&yen;</span><span id="total_amount">0</span></div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button wire:click.prevent="validateSave" class="btn btn-dark btn-large" id="btn-submit">{{
            trans2('button.next') }}</button>
    </div>

    <style>
        .form-select2 {
            border-color: var(--bs-border-color);
            padding-left: 6px;
            min-height: 40px;
            overflow: auto;
            display: flex;
            align-items: center;
            max-width: 100%;
            width: 100%;
            -ms-overflow-style: none;
            scrollbar-width: none;
            border-radius: 3px;
            -webkit-appearance: none;
            /* For Safari and Chrome */
            -moz-appearance: none;
            /* For Firefox */
            appearance: none;
            /* Standard property */
            background: #fff url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNSIgaGVpZ2h0PSI3IiB2aWV3Qm94PSIwIDAgMTUgNyI+IDxwYXRoIGQ9Ik02LjgxOC42MzdhMSwxLDAsMCwxLDEuMzY1LDBsNC45NjMsNC42MzJBMSwxLDAsMCwxLDEyLjQ2Myw3SDIuNTM3YTEsMSwwLDAsMS0uNjgyLTEuNzMxWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTUgNykgcm90YXRlKDE4MCkiIGZpbGw9IiM4ZDhkOGQiLz48L3N2Zz4=");
            background-repeat: no-repeat;
            background-position: right 10px top 50%;
            background-size: 14px 8px;
        }
    </style>
</div>
@script
<script>
    Alpine.data('inputPayment', () => ({
        handleDepositKeyDown(event) {
            if (event.key === ',') {
                event.preventDefault();
            }
        },
        handleDepositInput(event) {
            let v = event.target.value;
            v = v.replace(/,/g, '');
            if (v.includes('.')) {
                const parts = v.split('.');
                const decimalPart = parts[1].slice(0, 2);
                v = parts[0] + '.' + decimalPart;
            }

            event.target.value = v;
        }
    }))
</script>
@endscript
@push('scripts')
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('brandManager', () => ({
            selectedOptions: {
                'course-general': [],
                'course-optional': [],
                'shop-brand-id': []
            },
            initializedSelects: new Set(),
            isUpdating: false,
            updateTimeouts: {},

            init() {
                this.$nextTick(() => {
                    this.initSelect2Bindings();
                    this.setupLivewireHooks();
                });

                // Listen for custom init-select2 event
                window.addEventListener('init-select2', () => {
                    if (!this.isUpdating) {
                        clearTimeout(this.updateTimeouts.init);
                        this.updateTimeouts.init = setTimeout(() => {
                            this.initSelect2Bindings();
                        }, 100);
                    }
                });
            },

            initSelect2Bindings() {
                if (this.isUpdating) return;

                this.isUpdating = true;

                try {
                    this.updateSelectOptions('course-general');
                    this.updateSelectOptions('course-optional');
                    this.updateSelectOptions('shop-brand-id');
                    this.initSelectAuthTypeBinding();
                } finally {
                    setTimeout(() => {
                        this.isUpdating = false;
                    }, 50);
                }
            },

            updateSelectOptions(target) {
                const selectEls = document.querySelectorAll(`.${target}-selector`);
                if (selectEls.length === 0) return;

                let selectedOptions = [];

                // Collect all selected values
                selectEls.forEach(el => {
                    const selectedValue = el.value;
                    if (selectedValue) {
                        selectedOptions.push(selectedValue);
                    }
                });

                // Only update if selections have changed
                const currentSelections = JSON.stringify(selectedOptions.sort());
                const previousSelections = JSON.stringify((this.selectedOptions[target] || []).sort());

                if (currentSelections === previousSelections && this.initializedSelects.has(target)) {
                    return; // No changes needed
                }

                this.selectedOptions[target] = selectedOptions;

                // Process each select element
                selectEls.forEach(el => {
                    this.updateSingleSelect(el, selectedOptions, target);
                });

                this.initializedSelects.add(target);
            },

            updateSingleSelect(el, selectedOptions, target) {
                // Update option visibility without destroying Select2
                const curOptions = Array.from(el.options);

                curOptions.forEach(option => {
                    option.classList.remove('option-invisible');

                    if (option.value && selectedOptions.includes(option.value) && !option.selected) {
                        option.classList.add('option-invisible');
                    }
                });

                // Only initialize Select2 if not already initialized
                if (!el.classList.contains('select2-hidden-accessible')) {
                    this.initializeSelect2(el, target);
                } else {
                    // Just refresh the Select2 display without reinitializing
                    $(el).trigger('change.select2');
                }
            },

            initializeSelect2(el, target) {
                const $el = $(el);

                // Remove any existing event handlers to prevent duplicates
                $el.off('change.brandManager');

                $el.select2({
                    templateResult: this.resultState,
                    width: '100%',
                    dropdownAutoWidth: true
                }).on('change.brandManager', (e) => {
                    // Debounce the change handler to prevent rapid firing
                    clearTimeout(el._changeTimeout);
                    el._changeTimeout = setTimeout(() => {
                        this.handleSelectChange(e.target, target);
                    }, 100);
                });
            },

            handleSelectChange(selectEl, target) {
                if (this.isUpdating) return;

                const value = selectEl.value;
                const model = selectEl.getAttribute('wire:model');

                if (model) {
                    const componentId = selectEl.closest('[wire\\:id]').getAttribute('wire:id');
                    Livewire.find(componentId).set(model, value);

                    // Handle course amount update
                    if (model.includes('course_id')) {
                        const isGeneral = model.includes('product_general');
                        const courseList = this.getCourseListFromDOM(isGeneral);

                        if (value && courseList) {
                            const course = courseList.find(c => c.id == value);
                            if (course) {
                                const amountModel = model.replace('course_id', 'amount');
                                Livewire.find(componentId).set(amountModel, course.unit_price ?? 0);
                            }
                        }
                    }
                }

                // Update options for this target type only with debouncing
                clearTimeout(this.updateTimeouts[target]);
                this.updateTimeouts[target] = setTimeout(() => {
                    this.updateSelectOptions(target);
                }, 200);
            },

            initSelectAuthTypeBinding() {
                document.querySelectorAll('select.form-select2:not(.select2-hidden-accessible)').forEach(el => {
                    $(el).off('change.brandManager').select2({
                        width: '100%'
                    }).on('change.brandManager', (e) => {
                        const value = e.target.value;
                        const model = el.getAttribute('wire:model');
                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                        }
                    });
                });
            },

            resultState(data, container) {
                if (data.element) {
                    $(container).addClass($(data.element).attr("class"));
                }
                return data.text;
            },

            setupLivewireHooks() {
                let processingTimeout;

                Livewire.hook('message.processed', () => {
                    clearTimeout(processingTimeout);
                    processingTimeout = setTimeout(() => {
                        if (!this.isUpdating) {
                            this.initSelect2Bindings();
                        }
                    }, 200);
                });
            },

            getCourseListFromDOM(isGeneral) {
                const selector = isGeneral ? '.course-general-selector' : '.course-optional-selector';
                const select = document.querySelector(selector);
                if (!select) return [];

                const options = Array.from(select.options).filter(opt => opt.value);

                return options.map(opt => ({
                    id: opt.value,
                    name: opt.text,
                    unit_price: opt.getAttribute('data-unit-price') ? Number(opt.getAttribute('data-unit-price')) : null
                }));
            }
        }));
    });
</script>
<script>
    // logic calc total amount show ui when change
    function calculateTotalAmount() {
        let total = 0;
        const maxValueDecimal = 99999999.99;

        // Calculate total from general products
        $('#brandItemsGroup .form-parts').each(function() {
            const quantity = parseFloat($(this).find('input[wire\\:model*="quantity"]').val()) || 0;
            const amount = parseFloat($(this).find('input[wire\\:model*="amount"]').val()) || 0;
            total += quantity * amount;
        });

        // Calculate total from optional products
        $('#brandOptionGroup .form-parts').each(function() {
            const quantity = parseFloat($(this).find('input[wire\\:model*="quantity"]').val()) || 0;
            const amount = parseFloat($(this).find('input[wire\\:model*="amount"]').val()) || 0;
            total += quantity * amount;
        });

        // Update total amount display
        $('#total_amount').text(parseInt(total).toLocaleString('en-US'));

        // validate max value total price
        if (total > maxValueDecimal) {
            $('#btn-submit').prop('disabled', true);

            // show toast message error only if total changed
            if (window.notyf && window.lastTotalAmountForError !== total) {
                window.notyf.error('合計金額は99,999,999.99を超えてはなりません。');
                window.lastTotalAmountForError = total;
            }
        } else {
            $('#btn-submit').prop('disabled', false);
            // reset error flag when value is valid
            window.lastTotalAmountForError = null;
        }
    }

    // Listen for changes in quantity inputs
    $(document).on('input', '#brandItemsGroup input[wire\\:model*="quantity"], #brandOptionGroup input[wire\\:model*="quantity"]', function() {
        calculateTotalAmount();
    });

    // Listen for changes in amount inputs
    $(document).on('input', '#brandItemsGroup input[wire\\:model*="amount"], #brandOptionGroup input[wire\\:model*="amount"]', function() {
        calculateTotalAmount();
    });

    // Listen for Livewire updates
    document.addEventListener('livewire:updated', function() {
        setTimeout(() => {
            calculateTotalAmount();
        }, 100);
    });

    // Listen for Livewire DOM updates
    document.addEventListener('livewire:navigated', function() {
        setTimeout(() => {
            calculateTotalAmount();
        }, 100);
    });

    // Listen for Livewire component updates
    document.addEventListener('livewire:load', function() {
        setTimeout(() => {
            calculateTotalAmount();
        }, 100);
    });

    // Initial calculation
    $(document).ready(function() {
        calculateTotalAmount();
    });

    // Use MutationObserver for DOM changes (modern approach)
    function setupMutationObserver() {
        const brandItemsGroup = document.querySelector('#brandItemsGroup');
        const brandOptionGroup = document.querySelector('#brandOptionGroup');

        if (brandItemsGroup) {
            const observer1 = new MutationObserver(function(mutations) {
                calculateTotalAmount();
            });

            observer1.observe(brandItemsGroup, {
                childList: true,
                subtree: true
            });
        }

        if (brandOptionGroup) {
            const observer2 = new MutationObserver(function(mutations) {
                calculateTotalAmount();
            });

            observer2.observe(brandOptionGroup, {
                childList: true,
                subtree: true
            });
        }
    }

    // Setup mutation observers when document is ready
    $(document).ready(function() {
        setupMutationObserver();
    });

    // Setup mutation observers when Livewire loads
    document.addEventListener('livewire:load', function() {
        setTimeout(() => {
            setupMutationObserver();
        }, 100);
    });

    function getCourseListFromDOM(isGeneral) {
        const selector = isGeneral ? '.course-general-selector' : '.course-optional-selector';
        const select = document.querySelector(selector);
        if (!select) return [];

        const options = Array.from(select.options).filter(opt => opt.value);

        // return array object
        return options.map(opt => ({
            id: opt.value,
            name: opt.text,
            unit_price: opt.getAttribute('data-unit-price') ? Number(opt.getAttribute('data-unit-price')) : null
        }));
    }
</script>
@endpush