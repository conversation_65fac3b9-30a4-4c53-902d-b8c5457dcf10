<div x-data="inputPayment">
    <h1 class="application-page-title">1.{{ trans2('screens.application.brand.title') }}</h1>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">{{ trans2('screens.application.brand.brand_name') }}<span class="required-icon">{{
                        trans2('required') }}</span></th>
                <td>
                    <div wire:ignore style="max-width: 767px !important;">
                        <select name="" wire:model="saveForm.shop_brand_id"
                            class="form-select2 form-required shop-brand-id-selector"
                            data-placeholder="{{ trans2('select_default') }}" style="width:100%;">
                            <option value="" selected></option>
                            @foreach($brandShopList as $item)
                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    @error('saveForm.shop_brand_id')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="required">{{ trans2('screens.application.brand.staff_name') }}<span class="required-icon">{{
                        trans2('required') }}</span></th>
                <td>
                    <input type="text" wire:model="saveForm.staff_name" name="" value=""
                        class="form-control form-required w-px-300">
                    @error('saveForm.staff_name')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
        </tbody>
    </table>
    <h2 class="heading-2">{{ trans2('screens.application.brand.type_and_product_format') }}</h2>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">{{ trans2('screens.application.brand.fee_type') }}<span class="required-icon">{{
                        trans2('required') }}</span></th>
                <td>
                    <div class="d-flex">
                        <div class="form-radio me-5">
                            <label>
                                <input wire:model="saveForm.fee_type" wire:change="clearProductSelections" type="radio"
                                    name="fee_type" value="1" class="form-radio-input">
                                <span class="form-radio-text">{{ trans2('screens.application.brand.fee_type_self_paid')
                                    }}</span>
                            </label>
                        </div>
                        <div class="form-radio">
                            <label>
                                <input wire:model="saveForm.fee_type" wire:change="clearProductSelections" type="radio"
                                    name="fee_type" value="2" class="form-radio-input">
                                <span class="form-radio-text">{{ trans2('screens.application.brand.fee_type_standard')
                                    }}</span>
                            </label>
                        </div>
                    </div>
                    @error('saveForm.fee_type')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="align-top required">
                    {{ trans2('screens.application.brand.general_product') }}<span class="required-icon">{{
                        trans2('required') }}</span>
                </th>
                <td>
                    <div class="form-parts-group" id="brandItemsGroup">
                        @foreach($productGeneral as $index => $item)
                        <div wire:key="product_general_{{ $item['application_course_id'] ?? $index }}"
                            class="form-parts d-flex pt-2 pb-2 align-items-end" data-parts-id="{{ $index }}">
                            <div class="row align-items-start flex-grow-1">
                                <div class="col-auto flex-grow-1">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.product_name') }}
                                    </div>
                                    <div>
                                        <select wire:model="saveForm.product_general.{{ $index }}.course_id"
                                            class="form-select2 flex-grow-1 course-general-selector limit-select-w454"
                                            style="width:100%;" data-placeholder="{{ trans2('select_default') }}">
                                            <option value="" selected></option>
                                            @foreach($courseGeneralList as $course)
                                            <option value="{{ $course['id'] }}" wire:key="course_general_{{ $course['id'] }}"
                                                data-unit-price="{{ $course['unit_price'] }}">{{
                                                $course['name_application']
                                                ?? $course['name_management'] }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.quantity') }}</div>
                                    <div class="js-qty">
                                        <button type="button" class="js-qty__adjust js-qty__adjust--minus"
                                            wire:click="decrementQtyGeneral({{ $index }})"
                                            @disabled($item['quantity']==0)>
                                            <span aria-hidden="true">−</span>
                                            <span class="u-hidden-visually">{{
                                                trans2('screens.application.brand.sub_one') }}</span>
                                        </button>
                                        <input type="number" class="js-qty__num form-control"
                                            wire:model="saveForm.product_general.{{ $index }}.quantity" min="0"
                                            aria-label="quantity" pattern="[0-9]*" name="" step="1">
                                        <button type="button" class="js-qty__adjust js-qty__adjust--plus"
                                            wire:click="incrementQtyGeneral({{ $index }})">
                                            <span aria-hidden="true">+</span>
                                            <span class="u-hidden-visually">{{
                                                trans2('screens.application.brand.plus_one') }}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.amount') }}</div>
                                    <div class="form-parts-unit">
                                        <div class="form-unit">&yen;</div>
                                        <input type="text" name=""
                                            wire:model="saveForm.product_general.{{ $index }}.amount"
                                            class="form-control w-px-120" pattern="[0-9]*" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="ms-4">
                                @if ($loop->last)
                                <button type="button" class="btn btn-add" wire:click.prevent="addGeneral()"></button>
                                @else
                                <button type="button" class="btn btn-add form-delete-button delete-form-parts"
                                    wire:click.prevent="removeGeneral({{$index}})"></button>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div>
                                @error("saveForm.product_general")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_general.$index.course_id")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_general.$index.quantity")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_general.$index.amount")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        @endforeach
                    </div>
                </td>
            </tr>
            <tr>
                <th class="align-top">{{ trans2('screens.application.brand.optional_product') }}</th>
                <td>
                    <div class="form-parts-group" id="brandOptionGroup">
                        @foreach($productOptional as $index => $item)
                        <div wire:key="product_optional_{{ $item['application_course_id'] ?? $index }}"
                            class="form-parts d-flex pt-2 pb-2 align-items-end" data-parts-id="101">
                            <div class="row align-items-start flex-grow-1">
                                <div class="col-auto flex-grow-1">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.product_name') }}</div>
                                    <div>
                                        <select wire:model="saveForm.product_optional.{{ $index }}.course_id"
                                            class="form-select2 flex-grow-1 course-optional-selector limit-select-w454"
                                            style="width:100%;" data-placeholder="{{ trans2('select_default') }}">
                                            <option value="" selected></option>
                                            @foreach($courseOptionalList as $course)
                                            <option value="{{ $course['id'] }}" wire:key="course_optional_{{ $course['id'] }}"
                                                data-unit-price="{{ $course['unit_price'] }}">{{
                                                $course['name_application']
                                                ?? $course['name_management'] }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.quantity') }}</div>
                                    <div class="js-qty">
                                        <button type="button" class="js-qty__adjust js-qty__adjust--minus"
                                            wire:click="decrementQtyOptional({{ $index }})"
                                            @disabled($item['quantity']==0)>
                                            <span aria-hidden="true">−</span>
                                            <span class="u-hidden-visually">{{
                                                trans2('screens.application.brand.sub_one') }}</span>
                                        </button>
                                        <input type="number" class="js-qty__num form-control"
                                            wire:model="saveForm.product_optional.{{ $index }}.quantity" min="0"
                                            aria-label="quantity" pattern="[0-9]*" name="" step="1">
                                        <button type="button" class="js-qty__adjust js-qty__adjust--plus"
                                            wire:click="incrementQtyOptional({{ $index }})">
                                            <span aria-hidden="true">+</span>
                                            <span class="u-hidden-visually">{{
                                                trans2('screens.application.brand.plus_one') }}</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-center fw-medium fs-13 mb-2">{{
                                        trans2('screens.application.brand.amount') }}</div>
                                    <div class="form-parts-unit">
                                        <div class="form-unit">&yen;</div>
                                        <input type="text" max="99999999.99"
                                            wire:model="saveForm.product_optional.{{ $index }}.amount" name="deposit"
                                            class="form-control w-px-120" x-data x-ref="incomeInput"
                                            x-on:keydown="handleDepositKeyDown" x-on:input="handleDepositInput"
                                            id="inputIncome" />
                                    </div>
                                </div>
                            </div>
                            <div class="ms-4">
                                @if ($loop->last)
                                <button type="button" class="btn btn-add" wire:click.prevent="addOptional()"></button>
                                @else
                                <button type="button" class="btn btn-add form-delete-button delete-form-parts"
                                    wire:click.prevent="removeOptional({{$index}})"></button>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div>
                                @error("saveForm.product_optional.$index.course_id")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_optional.$index.quantity")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error("saveForm.product_optional.$index.amount")
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        @endforeach
                    </div>
                </td>
            </tr>
            <tr>
                <th>{{ trans2('screens.application.brand.total') }}</th>
                <td>
                    <div class="d-flex">
                        <div class="me-4">{{ trans2('screens.application.brand.total_payment') }}</div>
                        <div class="ms-auto"><span class="me-1">&yen;</span><span id="total_amount">0</span></div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button wire:click.prevent="validateSave" class="btn btn-dark btn-large" id="btn-submit">{{
            trans2('button.next') }}</button>
    </div>

    <style>
        .form-select2 {
            border-color: var(--bs-border-color);
            padding-left: 6px;
            min-height: 40px;
            overflow: auto;
            display: flex;
            align-items: center;
            max-width: 100%;
            width: 100%;
            -ms-overflow-style: none;
            scrollbar-width: none;
            border-radius: 3px;
            -webkit-appearance: none;
            /* For Safari and Chrome */
            -moz-appearance: none;
            /* For Firefox */
            appearance: none;
            /* Standard property */
            background: #fff url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNSIgaGVpZ2h0PSI3IiB2aWV3Qm94PSIwIDAgMTUgNyI+IDxwYXRoIGQ9Ik02LjgxOC42MzdhMSwxLDAsMCwxLDEuMzY1LDBsNC45NjMsNC42MzJBMSwxLDAsMCwxLDEyLjQ2Myw3SDIuNTM3YTEsMSwwLDAsMS0uNjgyLTEuNzMxWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTUgNykgcm90YXRlKDE4MCkiIGZpbGw9IiM4ZDhkOGQiLz48L3N2Zz4=");
            background-repeat: no-repeat;
            background-position: right 10px top 50%;
            background-size: 14px 8px;
        }
    </style>
</div>
@script
<script>
    Alpine.data('inputPayment', () => ({
        handleDepositKeyDown(event) {
            if (event.key === ',') {
                event.preventDefault();
            }
        },
        handleDepositInput(event) {
            let value = event.target.value;

            value = value.replace(/[^0-9.]/g, '');

            const firstDotIndex = value.indexOf('.');
            if (firstDotIndex !== -1) {
                let beforeDot = value.slice(0, firstDotIndex);
                let afterDot = value.slice(firstDotIndex + 1).replace(/\./g, '');
                value = beforeDot + '.' + afterDot;
            }

            let parts = value.split('.');
            let integerPart = parts[0] ?? '';
            let decimalPart = parts[1] ?? '';

            if (integerPart.length > 8) {
                integerPart = integerPart.slice(0, 8);
            }

            if (decimalPart.length > 2) {
                decimalPart = decimalPart.slice(0, 2);
            }

            if (decimalPart.length > 0 || value.endsWith('.')) {
                value = integerPart + '.' + decimalPart;
            } else {
                value = integerPart;
            }

            event.target.value = value;
        }
    }))
</script>
@endscript
@push('scripts')
<script>
    function initSelect2Binding(target) {
        const selectEls = $(`.${target}-selector:not(.select2-hidden-accessible)`); 
        let selectedOptions = [];

        $(`.${target}-selector`).each((index, el) => {
            let selectedValue = $(el).val();
            if (selectedValue) selectedOptions.push(selectedValue);
        });

        selectEls.each((index, el) => {
            const curOptions = Array.from(el.options).map(option => option.value);

            $(el).find('option').removeClass('option-invisible');

            for (let option of curOptions) {
                if (!option || !selectedOptions.includes(option)) continue;
                $(el).find('option')
                    .filter(function() {
                        return $(this).val() === option && !$(this).is(':selected');
                    })
                    .addClass('option-invisible');
            }

            $(el).select2({
                templateResult: resultState
            }).on('change', function(e) {
                const value = $(this).val();
                const model = el.getAttribute('wire:model');

                if (model) {
                    const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                    Livewire.find(componentId).set(model, value);

                    // Update amount when course_id changes
                    if (model.includes('course_id')) {
                        const index = model.split('.')[1];
                        const isGeneral = model.includes('product_general');
                        const courseList = getCourseListFromDOM(isGeneral);

                        if (value && courseList) {
                            const course = courseList.find(c => c.id == value);
                            if (course) {
                                const amountModel = model.replace('course_id', 'amount');
                                Livewire.find(componentId).set(amountModel, course.unit_price ?? 0);
                            }
                        }
                    }
                }
            });
        });
    }

    // Format Select2 options
    function resultState(data, container) {
        if (data.element) {
            $(container).addClass($(data.element).attr('class'));
        }
        return data.text;
    }

    // Initialize Select2 for all relevant selects
    function initAllSelect2() {
        initSelect2Binding('course-general');
        initSelect2Binding('course-optional');
        initSelect2Binding('shop-brand-id');
    }

    // Initialize on page load
    $(document).ready(function() {
        initAllSelect2();
        calculateTotalAmount();
        setupMutationObserver();
    });

    // Handle select change and update Livewire model
    function handleSelectChange(target, el) {
        const value = $(el).val();
        const model = el.getAttribute('wire:model');

        if (model) {
            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
            Livewire.find(componentId).set(model, value);

            // Update amount when course_id changes
            if (model.includes('course_id')) {
                const index = model.split('.')[1];
                const isGeneral = model.includes('product_general');
                const courseList = getCourseListFromDOM(isGeneral);

                if (value && courseList) {
                    const course = courseList.find(c => c.id == value);
                    if (course) {
                        const amountModel = model.replace('course_id', 'amount');
                        Livewire.find(componentId).set(amountModel, course.unit_price ?? 0);
                    }
                }
            }

            initSelect2Binding(target);
            calculateTotalAmount();
        }
    }

    // Handle dynamic select changes
    $(document).on('change', 'select.course-optional-selector', function() {
        handleSelectChange('course-optional', this);
    });

    $(document).on('change', 'select.course-general-selector', function() {
        handleSelectChange('course-general', this);
    });

    $(document).on('change', 'select.shop-brand-id-selector', function() {
        handleSelectChange('shop-brand-id', this);
    });

    function calculateTotalAmount() {
        let total = 0;
        const maxValueDecimal = 99999999.99;

        // Calculate total from general products
        $('#brandItemsGroup .form-parts').each(function() {
            const quantity = parseFloat($(this).find('input[wire\\:model*="quantity"]').val()) || 0;
            const amount = parseFloat($(this).find('input[wire\\:model*="amount"]').val()) || 0;
            total += quantity * amount;
        });

        // Calculate total from optional products
        $('#brandOptionGroup .form-parts').each(function() {
            const quantity = parseFloat($(this).find('input[wire\\:model*="quantity"]').val()) || 0;
            const amount = parseFloat($(this).find('input[wire\\:model*="amount"]').val()) || 0;
            total += quantity * amount;
        });

        // Update total amount display
        $('#total_amount').text(parseInt(total).toLocaleString('en-US'));

        if (total > maxValueDecimal) {
            $('#btn-submit').prop('disabled', true);
            if (window.notyf && window.lastTotalAmountForError !== total) {
                window.notyf.error('合計金額は99,999,999.99を超えてはなりません。');
                window.lastTotalAmountForError = total;
            }
        } else {
            $('#btn-submit').prop('disabled', false);
            window.lastTotalAmountForError = null;
        }
    }

    // Listen for input changes
    $(document).on('input', '#brandItemsGroup input[wire\\:model*="quantity"], #brandOptionGroup input[wire\\:model*="quantity"]', function() {
        calculateTotalAmount();
    });

    $(document).on('input', '#brandItemsGroup input[wire\\:model*="amount"], #brandOptionGroup input[wire\\:model*="amount"]', function() {
        calculateTotalAmount();
    });

    // Listen for Livewire updates
    document.addEventListener('livewire:updated', function() {
        setTimeout(() => {
            calculateTotalAmount();
        }, 100);
    });

    document.addEventListener('livewire:navigated', function() {
        setTimeout(() => {
            calculateTotalAmount();
        }, 100);
    });

    document.addEventListener('livewire:load', function() {
        setTimeout(() => {
            calculateTotalAmount();
            initAllSelect2();
        }, 100);
    });

    // Setup MutationObserver for dynamic DOM changes
    function setupMutationObserver() {
        const brandItemsGroup = document.querySelector('#brandItemsGroup');
        const brandOptionGroup = document.querySelector('#brandOptionGroup');

        if (brandItemsGroup) {
            const observer1 = new MutationObserver(function(mutations) {
                calculateTotalAmount();
                initSelect2Binding('course-general'); // Initialize new selects only
            });
            observer1.observe(brandItemsGroup, {
                childList: true,
                subtree: true
            });
        }

        if (brandOptionGroup) {
            const observer2 = new MutationObserver(function(mutations) {
                calculateTotalAmount();
                initSelect2Binding('course-optional'); // Initialize new selects only
            });
            observer2.observe(brandOptionGroup, {
                childList: true,
                subtree: true
            });
        }
    }

    // Get course list from DOM
    function getCourseListFromDOM(isGeneral) {
        const selector = isGeneral ? '.course-general-selector' : '.course-optional-selector';
        const select = document.querySelector(selector);
        if (!select) return [];

        const options = Array.from(select.options).filter(opt => opt.value);
        return options.map(opt => ({
            id: opt.value,
            name: opt.text,
            unit_price: opt.getAttribute('data-unit-price') ? Number(opt.getAttribute('data-unit-price')) : null
        }));
    }
</script>
@endpush