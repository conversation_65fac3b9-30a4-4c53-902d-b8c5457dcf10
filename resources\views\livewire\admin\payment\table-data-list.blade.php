@php
    $paymentStatusText = \App\Enums\PaymentStatusEnum::texts();
    $paymentStatusColor = \App\Enums\PaymentStatusEnum::colors();
    $paymentCompanyFlagText = \App\Enums\PaymentCompanyFlagEnum::texts();
@endphp
<div>
    {{-- skeleton loading overlay --}}
    <div wire:loading.flex class="row">
        @include('components.loading-overlay')
    </div>
    @if ($tableData)
        <div class="overflow-auto pt-3" data-simplebar data-simplebar-auto-hide="false" wire:init="notifyVisible" id="section">
            <table class="table table-borderless table-thead-bordered table-align-middle table-database" id="tableDataTable"
                data-link="">
                <thead>
                    <tr>
                        <th scope="col">{{ transm('payment.attributes.id') }}</th>
                        <th scope="col">{{ transm('contract.attributes.payment_company_flag') }}</th>
                        <th scope="col">{{ transm('payment.attributes.payment_status') }}</th>
                        <th scope="col">{{ transm('customer.attributes.name') }}</th>
                        <th scope="col">{{ transm('payment.attributes.payment_plan_date') }}</th>
                        <th scope="col">{{ transm('contract.attributes.courses') }}</th>
                        <th scope="col">{{ transm('payment.attributes.total_paid_amount') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @if ($tableData->isNotEmpty())
                        @foreach ($tableData as $data)
                            <tr wire:navigate href="{{ getRoute('payment.details', ['id' => $data->id]) }}" data-link="">
                                <td>{{ data_get($data, 'id') }}</td>
                                <td>
                                    @if ($data->payment_company_flag)
                                        {{ $paymentCompanyFlagText[$data->payment_company_flag?->value] }}
                                    @endif
                                </td>
                                <td>
                                    @if ($data->payment_status)
                                        <span class="badge badge-status rounded-pill badge-lg badge-{{ $paymentStatusColor[$data->payment_status?->value] }}">{{ $paymentStatusText[$data->payment_status?->value] }}</span>
                                    @endif
                                </td>
                                <td>{{ data_get($data, 'last_name') }} {{ data_get($data, 'first_name') }}</td>
                                <td>{{ data_get($data, 'payment_plan_date') }}</td>
                                <td>
                                    @foreach ( data_get($data,'application.courses') as $course)
                                        <div>
                                            {{ joinSlash(
                                                data_get($data, 'application.shopBrand.name'),
                                                data_get($course, 'itemType.name'),
                                                data_get($course, 'name_application'),
                                            ) }}
                                        </div>
                                    @endforeach
                                </td>
                                <td><span class="me-1">&yen;</span>{{ formatNumber(data_get($data, 'total_paid_amount')) }}</td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
            @if ($tableData->isEmpty())
                @include('components.no-result-found')
            @endif
        </div>
        <div class="text-center mt-4">
            @include('components.pagination-range-text', ['items' => $tableData])
            {{ $tableData->links('components.pagination') }}
        </div>
    @endif
</div>
@script
    <script>
        Livewire.on('scroll-to', ({ id }) => {
            const el = document.getElementById(id);

            if (el) {
                el.scrollIntoView({ behavior: 'smooth' });
            }
            const observer = new MutationObserver(() => {
                const el = document.getElementById(id);
                if (el) {
                    el.scrollIntoView({ behavior: 'smooth' });
                    observer.disconnect();
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
            });
        });
    </script>
@endscript
