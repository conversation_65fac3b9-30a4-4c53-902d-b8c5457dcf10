<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\InformationInputFlagEnum;
use App\Enums\RelationshipFlagEnum;
use App\Enums\SexEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Models\Customer;
use App\Rules\TelValidationRule;
use App\Rules\ZipCodeValidationRule;
use Illuminate\Validation\Rule;

class GuarantorForm extends BaseAdminForm
{
    public $information_input_flag = InformationInputFlagEnum::DISABLED;
    public $gw_last_name = '';
    public $gw_first_name = '';
    public $gw_first_name_kana = '';
    public $gw_last_name_kana = '';
    public $gw_sex = SexEnum::FEMALE;
    public $gw_birthday = '';
    public $gw_tel1 = '';
    public $gw_tel2 = '';
    public $gw_tel3 = '';
    public $gw_zip1 = '';
    public $gw_zip2 = '';
    public $gw_pref_id = '';
    public $gw_city = '';
    public $gw_address = '';
    public $gw_building = '';
    public $gw_company_name = '';
    public $gw_company_name_kana = '';
    public $gw_company_city = '';
    public $gw_company_address = '';
    public $gw_company_building = '';
    public $gw_company_pref_id = '';
    public $gw_company_tel1 = '';
    public $gw_company_tel2 = '';
    public $gw_company_tel3 = '';
    public $gw_company_zip1 = '';
    public $gw_company_zip2 = '';
    public $gw_relationship_flag = RelationshipFlagEnum::SPOUSE;
    public $gw_relationship_other = '';

    public function rules()
    {
        if ($this->information_input_flag == InformationInputFlagEnum::DISABLED) {
            return [
                'information_input_flag' => ['required', Rule::in(InformationInputFlagEnum::getValues())],
            ];
        }

        return [
            'information_input_flag' => ['required', Rule::in(InformationInputFlagEnum::getValues())],
            'gw_last_name' => 'required|string|max:128',
            'gw_first_name' => 'required|string|max:128',
            'gw_first_name_kana' => 'required|katakana|max:128',
            'gw_last_name_kana' => 'required|katakana|max:128',
            'gw_sex' => ['required', Rule::in(SexEnum::getValues())],
            'gw_birthday' => 'required|date',
            'gw_tel1' => ['required', 'max:32', new TelValidationRule()],
            'gw_tel2' => ['required', 'max:32', new TelValidationRule()],
            'gw_tel3' => ['required', 'max:32', new TelValidationRule()],
            'gw_relationship_flag' => ['required', Rule::in(RelationshipFlagEnum::getValues())],
            'gw_relationship_other' => 'nullable|string|max:512',
            'gw_zip1' => ['required', 'max:4', new ZipCodeValidationRule()],
            'gw_zip2' => ['required', 'max:4', new ZipCodeValidationRule()],
            'gw_pref_id' => 'required|exists:prefs,id',
            'gw_city' => 'required|string|max:1000',
            'gw_address' => 'required|string|max:1000',
            'gw_building' => 'nullable|string|max:1000',
            'gw_company_name' => 'nullable|string|max:128',
            'gw_company_name_kana' => 'nullable|katakana|max:128',
            'gw_company_city' => 'nullable|string|max:1000',
            'gw_company_address' => 'nullable|string|max:1000',
            'gw_company_building' => 'nullable|string|max:1000',
            'gw_company_pref_id' => 'nullable|exists:prefs,id',
            'gw_company_tel1' => ['nullable', 'max:32', new TelValidationRule()],
            'gw_company_tel2' => ['nullable', 'max:32', new TelValidationRule()],
            'gw_company_tel3' => ['nullable', 'max:32', new TelValidationRule()],
            'gw_company_zip1' => ['nullable', 'max:4', new ZipCodeValidationRule()],
            'gw_company_zip2' => ['nullable', 'max:4', new ZipCodeValidationRule()],
        ];
    }

    public function getValidationAttributes()
    {
        return __('models.guarantor.attributes');
    }

    public function fillFromCustomer(Customer $customer): void
    {
        $this->information_input_flag = $customer->information_input_flag?->value ?? (string) InformationInputFlagEnum::DISABLED;
        $this->gw_last_name = $customer->gw_last_name ?? '';
        $this->gw_first_name = $customer->gw_first_name ?? '';
        $this->gw_last_name_kana = $customer->gw_last_name_kana ?? '';
        $this->gw_first_name_kana = $customer->gw_first_name_kana ?? '';
        $this->gw_sex = $customer->gw_sex ?? (string) SexEnum::FEMALE;
        $this->gw_birthday = $customer->gw_birthday ?? '';
        $this->gw_tel1 = $customer->gw_tel1 ?? '';
        $this->gw_tel2 = $customer->gw_tel2 ?? '';
        $this->gw_tel3 = $customer->gw_tel3 ?? '';
        $this->gw_zip1 = $customer->gw_zip1 ?? '';
        $this->gw_zip2 = $customer->gw_zip2 ?? '';
        $this->gw_pref_id = $customer->gw_pref_id ?? '';
        $this->gw_city = $customer->gw_city ?? '';
        $this->gw_address = $customer->gw_address ?? '';
        $this->gw_building = $customer->gw_building ?? '';
        $this->gw_company_name = $customer->gw_company_name ?? '';
        $this->gw_company_name_kana = $customer->gw_company_name_kana ?? '';
        $this->gw_company_city = $customer->gw_company_city ?? '';
        $this->gw_company_address = $customer->gw_company_address ?? '';
        $this->gw_company_building = $customer->gw_company_building ?? '';
        $this->gw_company_pref_id = $customer->gw_company_pref_id ?? '';
        $this->gw_company_tel1 = $customer->gw_company_tel1 ?? '';
        $this->gw_company_tel2 = $customer->gw_company_tel2 ?? '';
        $this->gw_company_tel3 = $customer->gw_company_tel3 ?? '';
        $this->gw_company_zip1 = $customer->gw_company_zip1 ?? '';
        $this->gw_company_zip2 = $customer->gw_company_zip2 ?? '';
        $this->gw_relationship_flag = $customer->gw_relationship_flag?->value ?? (string) RelationshipFlagEnum::SPOUSE;
        $this->gw_relationship_other = $customer->gw_relationship_other ?? '';
    }
}
