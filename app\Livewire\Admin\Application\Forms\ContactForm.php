<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\ContactFlagEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Models\Customer;
use Illuminate\Validation\Rule;

class ContactForm extends BaseAdminForm
{
    public $contact_flag = ContactFlagEnum::HOME;
    public $contact_hope_date1 = '';
    public $contact_hope_start_time1 = '';
    public $contact_hope_end_time1 = '';
    public $contact_hope_date2 = '';
    public $contact_hope_start_time2 = '';
    public $contact_hope_end_time2 = '';

    public function rules()
    {
        return [
            'contact_flag' => ['required', Rule::in(ContactFlagEnum::getValues())],
            'contact_hope_date1' => 'required|date',
            'contact_hope_start_time1' => 'required',
            'contact_hope_end_time1' => 'required|after:contact_hope_start_time1',
            'contact_hope_date2' => 'required|date',
            'contact_hope_start_time2' => 'required',
            'contact_hope_end_time2' => 'required|after:contact_hope_start_time2',
        ];
    }

    public function getValidationAttributes()
    {
        return __('models.contact.attributes');
    }

    public function fillFromCustomer(Customer $customer): void
    {
        $this->contact_flag = $customer->contact_flag?->value ?? (string) ContactFlagEnum::HOME;
        $this->contact_hope_date1 = $customer->contact_hope_date1 ?? '';
        $this->contact_hope_start_time1 = $customer->contact_hope_start_time1 ?? '';
        $this->contact_hope_end_time1 = $customer->contact_hope_end_time1 ?? '';
        $this->contact_hope_date2 = $customer->contact_hope_date2 ?? '';
        $this->contact_hope_start_time2 = $customer->contact_hope_start_time2 ?? '';
        $this->contact_hope_end_time2 = $customer->contact_hope_end_time2 ?? '';
    }
}
