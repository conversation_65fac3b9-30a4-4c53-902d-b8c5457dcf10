<?php

namespace App\Repositories;

use App\Models\LoanPayment;
use App\Models\LoanPaymentAllocation;
use App\Models\LoanSchedule;
use App\Models\LoanTransactionLog;
use Illuminate\Support\Facades\DB;

class LoanPaymentRepository extends CustomRepository
{
    protected $model = LoanPayment::class;

    public function getPaymentDatebyApplicationId($id)
    {
        return $this->model->select([
            $this->modelField('id'),
            $this->modelField('payment_date'),
        ])
            ->where('application_id', $id)
            ->get();
    }

    public function getLoanPaymentById($id)
    {
        return DB::table(LoanPayment::getTableName())
            ->where($this->modelField('id'), $id)
            ->first();
    }

    public function insertLoanPayment($data)
    {
        return $this->model->insertGetId($data);
    }

    public function updateLoanPayment($id, $userId)
    {
        return $this->where($this->modelField('id'), $id)
            ->where($this->modelField('del_flag'), '0')
            ->update([
                'del_flag' => '1',
                'upd_id'   => $userId,
                'upd_date' => now(),
            ]);
    }

    public function getLoanPaymentsWithLogsByApplicationId($applicationId)
    {
        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('amount'),
            $this->modelField('payment_date')  . ' as payment_date',
            $this->modelField('payment_type'),
            DB::raw("CASE
                WHEN " . LoanTransactionLog::field('type') . " = 1 THEN '入金'
                WHEN " . LoanTransactionLog::field('type') . " = 2 THEN '返金'
                ELSE 'その他' END AS transaction_type"),
            LoanTransactionLog::field('type'),
        ])
        ->with([
            'loanPaymentAllocations' => function ($q) {
                $q->select([
                    LoanPaymentAllocation::field('id'),
                    LoanPaymentAllocation::field('loan_payment_id'),
                    LoanPaymentAllocation::field('loan_schedule_id'),
                    LoanPaymentAllocation::field('before_payment_status'),
                ]);
            },
            'loanPaymentAllocations.loanSchedule'=> function ($q) {
                $q->select([
                    LoanSchedule::field('payment_status'),
                    LoanSchedule::field('id'),
                ]);
            },
        ])->leftJoin(LoanTransactionLog::getTableName(), $this->modelField('id'), LoanTransactionLog::field('loan_payment_id'))
        ->where($this->modelField('application_id'), $applicationId)
        ->groupBy($this->modelField('id'));

        return $q->get();
    }


}
