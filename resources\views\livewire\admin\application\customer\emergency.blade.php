<div>
    <h1 class="application-page-title">6.お客様登録画面 / 緊急連絡先</h1>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th>お名前</th>
                <td>
                    <div class="row">
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control" placeholder="姓"
                                wire:model='emergencyForm.emergency_last_name' maxlength="128">
                            <div>
                                @error('emergencyForm.emergency_last_name') <span class="message-error">{{ $message
                                    }}</span>@enderror
                            </div>
                        </div>
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control" placeholder="名"
                                wire:model='emergencyForm.emergency_first_name' maxlength="128">
                            <div>
                                @error('emergencyForm.emergency_first_name') <span class="message-error">{{ $message
                                    }}</span>@enderror
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th>お名前（フリガナ）</th>
                <td>
                    <div class="row">
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control" placeholder="セイ"
                                wire:model='emergencyForm.emergency_last_name_kana' maxlength="128">
                            <div>
                                @error('emergencyForm.emergency_last_name_kana') <span class="message-error">{{ $message
                                    }}</span>@enderror
                            </div>
                        </div>
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control" placeholder="メイ"
                                wire:model='emergencyForm.emergency_first_name_kana' maxlength="128">
                            <div>
                                @error('emergencyForm.emergency_first_name_kana') <span class="message-error">{{
                                    $message }}</span>@enderror
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th>郵便番号</th>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <input type="text" name="zip1" value="" class="form-control w-px-150" maxlength="4"
                                wire:model="emergencyForm.emergency_zip1">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="zip2" value="" class="form-control w-px-150" maxlength="4"
                                wire:model="emergencyForm.emergency_zip2">
                        </div>
                    </div>
                    <div>
                        <div>
                            @error('emergencyForm.emergency_zip1') <span class="message-error">{{
                                $message}}</span>@enderror
                        </div>
                        <div>
                            @error('emergencyForm.emergency_zip2') <span class="message-error">{{
                                $message}}</span>@enderror
                        </div>
                    </div>
                </td>
            </tr>
            <tr class="check-required-row">
                <th>都道府県</th>
                <td>
                    <div wire:ignore>
                        <select name="pref_id" class="form-select2 " wire:model="emergencyForm.emergency_pref_id"
                            style="width:280px;" data-placeholder="{{ trans2('select_default') }}">
                            <option value=""></option>
                            @foreach ($listPrefs as $key => $pref)
                            <option value="{{ data_get($pref,'id') }}">{{ data_get($pref,'name') }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        @error('emergencyForm.emergency_pref_id') <span class="message-error">{{
                            $message}}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th>市区町村</th>
                <td>
                    <input type="text" name="city" value="" class="form-control"
                        wire:model="emergencyForm.emergency_city" maxlength="1000">
                    <div>
                        @error('emergencyForm.emergency_city') <span class="message-error">{{ $message
                            }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th>町名・番地</th>
                <td>
                    <input type="text" name="address" value="" class="form-control"
                        wire:model="emergencyForm.emergency_address" maxlength="1000">
                    <div>
                        @error('emergencyForm.emergency_address') <span class="message-error">{{ $message
                            }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th>建物名・部屋番号</th>
                <td>
                    <input type="text" name="buidling" value="" class="form-control"
                        wire:model="emergencyForm.emergency_building" maxlength="1000">
                    <div>
                        @error('emergencyForm.emergency_building') <span class="message-error">{{ $message
                            }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th>電話番号</th>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <input type="text" name="" value="" class="form-control w-px-150"
                                wire:model="emergencyForm.emergency_tel1" maxlength="32">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="" value="" class="form-control w-px-150"
                                wire:model="emergencyForm.emergency_tel2" maxlength="32">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="" value="" class="form-control w-px-150"
                                wire:model="emergencyForm.emergency_tel3" maxlength="32">
                        </div>
                    </div>
                    <div>
                        <div>
                            @error('emergencyForm.emergency_tel1') <span class="message-error">{{
                                $message}}</span>@enderror
                        </div>
                        <div>
                            @error('emergencyForm.emergency_tel2') <span class="message-error">{{
                                $message}}</span>@enderror
                        </div>
                        <div>
                            @error('emergencyForm.emergency_tel3') <span
                                class="message-error">{{$message}}</span>@enderror
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="align-top">お申込者様とのご関係</th>
                <td class="align-top">
                    <div class="row flex-wra pt-2 pb-3">
                        <div class="col-3 mb-4">
                            <div class="form-radio">
                                <label>
                                    <input type="radio" name="relationship_flag"
                                        wire:model="emergencyForm.relationship_flag" value="1"
                                        class="form-radio-input form-disabled-control-flag" data-disabled-cancel="false"
                                        data-disabled-target="relationship" checked>
                                    <span class="form-radio-text">{{ trans2('RelationshipFlagEnum.SPOUSE') }}</span>
                                </label>
                            </div>
                        </div>
                        <div class="col-3 mb-4">
                            <div class="form-radio">
                                <label>
                                    <input type="radio" name="relationship_flag"
                                        wire:model="emergencyForm.relationship_flag" value="2"
                                        class="form-radio-input form-disabled-control-flag" data-disabled-cancel="false"
                                        data-disabled-target="relationship">
                                    <span class="form-radio-text">{{ trans2('RelationshipFlagEnum.PARENT_CHILD')
                                        }}</span>
                                </label>
                            </div>
                        </div>
                        <div class="col-3 mb-4">
                            <div class="form-radio">
                                <label>
                                    <input type="radio" name="relationship_flag"
                                        wire:model="emergencyForm.relationship_flag" value="3"
                                        class="form-radio-input form-disabled-control-flag" data-disabled-cancel="false"
                                        data-disabled-target="relationship">
                                    <span class="form-radio-text">{{ trans2('RelationshipFlagEnum.SIBLING') }}</span>
                                </label>
                            </div>
                        </div>
                        <div class="col-3 mb-4">
                            <div class="form-radio">
                                <label>
                                    <input type="radio" name="relationship_flag"
                                        wire:model="emergencyForm.relationship_flag" value="4"
                                        class="form-radio-input form-disabled-control-flag" data-disabled-cancel="false"
                                        data-disabled-target="relationship">
                                    <span class="form-radio-text">{{ trans2('RelationshipFlagEnum.RELATIVE') }}</span>
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-radio mb-2">
                                <label>
                                    <input type="radio" name="relationship_flag"
                                        wire:model="emergencyForm.relationship_flag" value="5"
                                        class="form-radio-input form-disabled-control-flag" data-disabled-cancel="true"
                                        data-disabled-target="relationship">
                                    <span class="form-radio-text">{{ trans2('Enum.OTHER') }}</span>
                                </label>
                            </div>
                            <div class="ps-5">
                                <input type="text" name="" value="" class="form-control  disabled-target-relationship"
                                    wire:model="emergencyForm.relationship_other"
                                    @disabled($emergencyForm->relationship_flag != 5)>
                                <div>
                                    @error('emergencyForm.relationship_other') <span class="message-error">{{ $message
                                        }}</span>@enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button wire:click.prevent="validateSave" class="btn btn-dark btn-large">{{ trans2('button.next') }}</button>
    </div>
</div>
@include('livewire.admin.application.customer.script')