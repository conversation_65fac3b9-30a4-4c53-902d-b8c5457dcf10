<div>
    <button type="button" class="btn-text fc-link p-0" data-bs-toggle="modal" data-bs-target="#confirmDeleteModal-{{ $id }}">{{ trans2('button.delete') }}</button>
    <div class="modal fade deleteModal" id="confirmDeleteModal-{{ $id }}" tabindex="-1">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <h5 class="modal-title">
                        <span class="title-icon">{{ trans2('confirm_modal.delete') }}</span>
                    </h5>
                    <p>{{ trans2('confirm_modal.alert') }}</p>
                    <form role="form">
                        <div class="d-flex justify-content-center pt-4 pb-4">
                            <div class="d-flex">
                                <button type="button" class="btn btn-border w-px-160 me-4" data-bs-dismiss="modal">{{ trans2('button.cancel') }}</button>
                                <button type="button" class="btn btn-danger w-px-160" data-bs-dismiss="modal" wire:click.prevent="delete">{{ trans2('button.delete_to') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{{--    <livewire:common.delete-confirm-modal :isNotDispatch="1" />--}}
</div>
