<?php

namespace App\Livewire\Admin\Application\Customer;

use App\Enums\CustomerTabEnum;
use App\Livewire\Admin\Application\Forms\ContactForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\CustomerRepository;
use App\Services\CustomerService;
use App\Services\PrefService;
use App\Services\ToastService;
use App\Traits\HandlesApplicationCustomer;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;

#[Layout('components.layouts.application-layout')]
class Contact extends BaseAdminPageComponent
{
    use HandlesApplicationCustomer;
    
    public ContactForm $contactForm;

    public function mount($customer_id,$application_id)
    {        
        $this->page = CustomerTabEnum::CONTACT;
        $this->pageTitle .= '｜' . trans2('project_name');
        $this->initCustomerAndApplication($customer_id, $application_id);

        $customer = app(CustomerRepository::class)->getInfoApplicationCustomer($customer_id);

        if ($customer) {
            $this->contactForm->fillFromCustomer($customer);
        }
    }

    public function validateSave()
    {
        $this->contactForm->validate();
        $this->dispatch('app-contact-store', $this->contactForm);
    }

    #[On('app-contact-store')]
    public function store()
    {
        $params = $this->contactForm->all();
        
        $customer = app(CustomerService::class)->update($this->customerId,$params);

        if($customer) {
            app(ToastService::class)->createSuccess();
            return redirect()->route('admin.customer.application.check', [
                'application_id' => $this->applicationId,
            ]);
        }

        app(ToastService::class)->createError();
    }

    public function render()
    {
        $this->dispatch('init-select2');
        return $this->viewLivewireAdmin('application.customer.contact', [
            'listPrefs' => app(PrefService::class)->getAllPrefs(),
        ]);
    }
}
