<?php

namespace App\Repositories;

use App\Models\LoanArrear;
use App\Models\LoanOverpayment;
use App\Models\LoanPayment;
use App\Models\LoanPaymentAllocation;
use App\Models\LoanRefund;
use App\Models\LoanTransactionLog;
use App\Models\LoanWithdrawal;
use DB;

class LoanWithdrawalRepository extends CustomRepository
{
    protected $model = LoanWithdrawal::class;

    public function getLoanWithdrawalsWithLogsByApplicationId($applicationId)
    {
        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('amount'),
            $this->modelField('withdrawal_date') . ' as payment_date',
            $this->modelField('withdrawal_type') . ' as payment_type',
            DB::raw("CASE
                WHEN " . LoanTransactionLog::field('type') . " = 1 THEN '入金'
                WHEN " . LoanTransactionLog::field('type') . " = 2 THEN '返金'
                ELSE 'その他' END AS transaction_type"),
            LoanTransactionLog::field('type'),
        ])->leftJoin(LoanTransactionLog::getTableName(), LoanTransactionLog::field('loan_withdrawal_id'), $this->modelField('id'))
        ->where($this->modelField('application_id'), $applicationId)
        ->groupBy($this->modelField('id'));

        return $q->get();
    }
}
