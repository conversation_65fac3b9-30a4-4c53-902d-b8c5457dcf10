{"__meta": {"id": "01K0BQ1QENJR9VTJS8AF9BJ1KY", "datetime": "2025-07-17 17:12:09", "utime": **********.575139, "method": "GET", "uri": "/management", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.032261, "end": **********.575155, "duration": 2.542894124984741, "duration_str": "2.54s", "measures": [{"label": "Booting", "start": **********.032261, "relative_start": 0, "end": **********.890821, "relative_end": **********.890821, "duration": 0.****************, "duration_str": "859ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.89084, "relative_start": 0.***************, "end": **********.575158, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.422168, "relative_start": 1.****************, "end": **********.613472, "relative_end": **********.613472, "duration": 0.*****************, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.41371, "relative_start": 2.****************, "end": **********.443643, "relative_end": **********.443643, "duration": 0.*****************, "duration_str": "29.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET management", "middleware": "admin, locale, auth:admin", "uses": "Closure() {#502\n  class: \"Livewire\\Volt\\VoltManager\"\n  this: Livewire\\Volt\\VoltManager {#493 …}\n  use: {\n    $componentName: \"App\\Livewire\\Admin\\Dashboard\\Index\"\n  }\n  file: \"C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\VoltManager.php\"\n  line: \"34 to 41\"\n}", "as": "admin.home", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "S3Npaj4xoSHtyVYig42xftxE7lXFc46QHe5x7VXi", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/management", "action_name": "admin.home", "controller_action": "Closure", "uri": "GET management", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>", "middleware": "admin, locale, auth:admin", "duration": "2.71s", "peak_memory": "24MB", "response": "Redirect to http://127.0.0.1:8000/management/login", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-298714885 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-298714885\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2076482068 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2076482068\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-302105473 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1123 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImltZnBOVG05bEQvdTBJeVBaYlhDQkE9PSIsInZhbHVlIjoiaXFWUkZ0dmtUWlB1NUtmTDZ5bWdLRGRyTFlRaHhTQVBXM2tEWTR0RWZnbERHU3FRdzQ0elE3SWRHUlo4NDE2ZXJaOFFJVGprb0RDZlV6YlFOR1U1WEF0L2pFQXpiV09vQ0NPdkV3MGNpcDRSdnd5QjNPeGkvRDdWVHRNOXUzVE5TT2NEZ09IUnVnVVlnbmxBcU91c3JnPT0iLCJtYWMiOiJmMGU1YTZhNjA1YjYwMGQ5ODJhYTIxOWY1ZDllNDdjMDc1OWNmZDAyODM2NjU1NTJmMmU1ZTA1NTc0MTg0ZTkwIiwidGFnIjoiIn0%3D; cookie_per_page=eyJpdiI6IkV4b3ZySWVaTjI0czgxRmpycEc3MHc9PSIsInZhbHVlIjoiQUk4ak14MytpVTdFUjBubkplSWZJK25Rc0FWbXV5K2pabU5nU0tuVUNycWx3UXpoZjNqdW9NNWlzcmJ3bmsvOCIsIm1hYyI6IjI5ZjgzNWFiMDIxNTg3MDc3MGU4YzFlNDA3OGVjNDg4N2ExODA5MmU2NDJjMzMyMzdmMWMxZDVkY2RjMDA1ZDMiLCJ0YWciOiIifQ%3D%3D; leasemart_session=w8w1HYeAl52hZGLblPhiSoOKeze2Xr5qGWmGpJqZ; XSRF-TOKEN=eyJpdiI6IndMUkdtZGhUcWlmeHpWWDlsZVp5dHc9PSIsInZhbHVlIjoicWNralQ3VDQ4MTlGUkI4QnRkNHBGMDlEYWl6OENtY1Exdjh0TC9hRlZvSmlxYlBQWEZmb29hZ3J1Qy9MUmYzRXVVcFU3ZU9hNThsYnR5VUtKR0hGUG9HZDlycFdrbUJIQ2JkQ1hSbmZKaUREVWNaMXpMdmxnb2hZTkgrOHc0WUIiLCJtYWMiOiI2ZWE1Mjk1MzhkMzIxYWExYTRkZGU5MjEzMGQwYjU1MmQ5NGFlYTg2ZmM5OTk0ZWMzNjlhMzVhOTUxMTZiOWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-302105473\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-614633141 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>leasemart_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614633141\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-575735438 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 08:12:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/management/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InJaVEpyc1hLczYzU0xmL0xWRVRZWlE9PSIsInZhbHVlIjoiMUNFU3J5YWcwbENwVG16TlVoaDZ3Z09QQy9pUnRaSkdMdFI4V0pCYmZSL0YwaUVVOHZpb3NzbDBLVjFyT0hULzVXeVk5VUlYVnBSTzg0S3UyZUZQZHIyeTNsZWtuQ21ieDQ0MjdEZm43eXVKNmFsa0ZpL3JPdkI4anJjdHVodk0iLCJtYWMiOiI4MWM1YjRhMzIxNjk1MmQzOWNiNDM3YjJjMTRiNWJmODI5OWMyMmIzNTg5Yjg5N2Q5NTdiMWQ1ZWM4MWFkOWMxIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 10:12:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InJaVEpyc1hLczYzU0xmL0xWRVRZWlE9PSIsInZhbHVlIjoiMUNFU3J5YWcwbENwVG16TlVoaDZ3Z09QQy9pUnRaSkdMdFI4V0pCYmZSL0YwaUVVOHZpb3NzbDBLVjFyT0hULzVXeVk5VUlYVnBSTzg0S3UyZUZQZHIyeTNsZWtuQ21ieDQ0MjdEZm43eXVKNmFsa0ZpL3JPdkI4anJjdHVodk0iLCJtYWMiOiI4MWM1YjRhMzIxNjk1MmQzOWNiNDM3YjJjMTRiNWJmODI5OWMyMmIzNTg5Yjg5N2Q5NTdiMWQ1ZWM4MWFkOWMxIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 10:12:09 GMT; path=/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575735438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-965347728 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S3Npaj4xoSHtyVYig42xftxE7lXFc46QHe5x7VXi</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965347728\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/management", "action_name": "admin.home", "controller_action": "Closure"}, "badge": "302 Found"}}