<?php

namespace App\Repositories;

use App\Enums\LoanTransactionTypeEnum;
use App\Models\LoanArrear;
use App\Models\LoanOverpayment;
use App\Models\LoanPayment;
use App\Models\LoanPaymentAllocation;
use App\Models\LoanRefund;
use App\Models\LoanSchedule;
use App\Models\LoanTransactionLog;
use DB;

class LoanTransactionLogRepository extends CustomRepository
{
    protected $model = LoanTransactionLog::class;

    public function getLoanTransactionLogByApplication($application_id)
    {
        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('ins_date'),
            $this->modelField('type'),
            DB::raw("CASE
                WHEN " . $this->modelField('type') . " = 1 THEN " . LoanPayment::field('amount') . "
                WHEN " . $this->modelField('type') . " = 2 THEN -loan_refunds.amount
                ELSE 0 END AS amount"),
            DB::raw("CASE
                WHEN " . $this->modelField('type') . " = 1 THEN '入金'
                WHEN " . $this->modelField('type') . " = 2 THEN '返金'
                ELSE 'その他' END AS transaction_type"),
            DB::raw("COALESCE(" . LoanPayment::field('payment_date') . ", " . LoanRefund::field('payment_date') . ") AS payment_date"),
            LoanPayment::field('payment_type'),
            LoanPaymentAllocation::field('loan_schedule_id'),
            LoanPaymentAllocation::field('before_payment_status'),

            $this->modelField('loan_payment_id'),
            $this->modelField('loan_refund_id'),
            $this->modelField('loan_payment_allocation_id'),

            LoanSchedule::field('application_id'),
            LoanSchedule::field('payment_status'),
        ]);

        $q->leftJoin(LoanRefund::getTableName(), $this->modelField('loan_refund_id'), LoanRefund::field('id'))
            ->leftJoin(LoanPaymentAllocation::getTableName(), $this->modelField('loan_payment_allocation_id'), LoanPaymentAllocation::field('id'))
            ->leftJoin(LoanPayment::getTableName(), $this->modelField('loan_payment_id'), LoanPayment::field('id'))
            ->leftJoin(LoanArrear::getTableName(), $this->modelField('loan_payment_id'), LoanArrear::field('id'))
            ->leftJoin(LoanSchedule::getTableName(), $this->modelField('loan_schedule_id'), LoanSchedule::field('id'));

        $q->where($this->modelField('application_id'), $application_id)
            ->whereIn($this->modelField('type'), LoanTransactionTypeEnum::getActiveTransactionTypeValues())
            ->orderByRaw("COALESCE(" . LoanPayment::field('payment_date') . ", " . LoanRefund::field('payment_date') . ") desc")
            ->orderBy('id', 'desc');

        return $q->get();
    }

    public function insertTransactionLog(array $data)
    {
        return $this->insert($data);
    }

    public function createLoanTransactionLog($data)
    {
        return $this->create($data);
    }
}
