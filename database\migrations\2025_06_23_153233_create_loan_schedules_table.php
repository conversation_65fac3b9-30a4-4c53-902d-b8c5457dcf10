<?php

use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('loan_schedules', function (BlueprintCustom $table) {
            $table->id();
            $table->integer('customer_id')->comment('顧客ID')->index();
            $table->integer('application_id')->comment('申込ID')->index();
            $table->date('payment_plan_date')->comment('入金予定日');
            $table->decimal('amount', 10, 2)->comment('入金予定額');
            $table->decimal('bonus_payment_amount', 10, 2)->comment('ボーナス支払額');
            $table->decimal('amount_paid', 10, 2)->comment('累計入金額');
            $table->char('type', 1)->comment('新規コード');
            $table->char('payment_type', 1)->comment('支払種別');
            $table->char('payment_company_flag', 1)->comment('収納会社');
            $table->char('result_status', 1)->comment('引落結果');
            $table->char('payment_status', 1)->comment('入金ステータス');
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_schedules');
    }
};
