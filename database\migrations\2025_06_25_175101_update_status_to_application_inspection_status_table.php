<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('application_inspection_status', function (Blueprint $table) {
            $table->char('status', 2)->comment('審査ステータス')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('application_inspection_status', function (Blueprint $table) {
            $table->char('status', 1)->comment('審査ステータス')->change();
        });
    }
};
