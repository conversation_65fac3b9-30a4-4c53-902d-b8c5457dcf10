{"__meta": {"id": "01JZS9SP1A802E01WW3Z3R93XZ", "datetime": "2025-07-10 13:34:14", "utime": **********.69898, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[13:34:14] LOG.debug: (Time: 26.68) SQL: select * from `administrators` where `id` = 2 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.66094, "xdebug_link": null, "collector": "log"}, {"message": "[13:34:14] LOG.debug: (Time: 00.52) SQL: select * from `customers` where `customers`.`id` = 6 limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.675023, "xdebug_link": null, "collector": "log"}, {"message": "[13:34:14] LOG.debug: (Time: 00.46) SQL: select `courses`.`id`, `courses`.`name_management`, `courses`.`name_application`, `courses`.`type`, `courses`.`unit_price` from `courses` where `type` = '1' and `split_fee_flag` = 1 and `courses`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.682994, "xdebug_link": null, "collector": "log"}, {"message": "[13:34:14] LOG.debug: (Time: 00.44) SQL: select `courses`.`id`, `courses`.`name_management`, `courses`.`name_application`, `courses`.`type`, `courses`.`unit_price` from `courses` where `type` = '2' and `split_fee_flag` = 1 and `courses`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.687103, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.443332, "end": **********.699001, "duration": 0.25566911697387695, "duration_str": "256ms", "measures": [{"label": "Booting", "start": **********.443332, "relative_start": 0, "end": **********.604261, "relative_end": **********.604261, "duration": 0.*****************, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.604274, "relative_start": 0.*****************, "end": **********.699003, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "94.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.611678, "relative_start": 0.*****************, "end": **********.613752, "relative_end": **********.613752, "duration": 0.002074003219604492, "duration_str": "2.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.696805, "relative_start": 0.*****************, "end": **********.69744, "relative_end": **********.69744, "duration": 0.0006349086761474609, "duration_str": "635μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x livewire.admin.application.brand", "param_count": null, "params": [], "start": **********.694754, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/application/brand.blade.phplivewire.admin.application.brand", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fapplication%2Fbrand.blade.php&line=1", "ajax": false, "filename": "brand.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.application.brand"}]}, "route": {"uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Application\\Brand@incrementQtyGeneral<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FApplication%2FBrand.php&line=224\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FApplication%2FBrand.php&line=224\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Application/Brand.php:224-230</a>"}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.028099999999999997, "accumulated_duration_str": "28.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 2 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.634366, "duration": 0.02668, "duration_str": "26.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 94.947}, {"sql": "select * from `customers` where `customers`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.674603, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ModelSynth.php:65", "source": {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/ModelSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\ModelSynth.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FModelSynth.php&line=65", "ajax": false, "filename": "ModelSynth.php", "line": "65"}, "connection": "local-ladybird", "explain": null, "start_percent": 94.947, "width_percent": 1.851}, {"sql": "select `courses`.`id`, `courses`.`name_management`, `courses`.`name_application`, `courses`.`type`, `courses`.`unit_price` from `courses` where `type` = '\\'1\\'' and `split_fee_flag` = 1 and `courses`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'1'", 1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 64}, {"index": 16, "namespace": null, "name": "app/Services/CourseService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\CourseService.php", "line": 108}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 118}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.68264, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CourseRepository.php:64", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FCourseRepository.php&line=64", "ajax": false, "filename": "CourseRepository.php", "line": "64"}, "connection": "local-ladybird", "explain": null, "start_percent": 96.797, "width_percent": 1.637}, {"sql": "select `courses`.`id`, `courses`.`name_management`, `courses`.`name_application`, `courses`.`type`, `courses`.`unit_price` from `courses` where `type` = '\\'2\\'' and `split_fee_flag` = 1 and `courses`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'2'", 1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 64}, {"index": 16, "namespace": null, "name": "app/Services/CourseService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\CourseService.php", "line": 108}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 119}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.686764, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CourseRepository.php:64", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FCourseRepository.php&line=64", "ajax": false, "filename": "CourseRepository.php", "line": "64"}, "connection": "local-ladybird", "explain": null, "start_percent": 98.434, "width_percent": 1.566}]}, "models": {"data": {"App\\Models\\Course": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": {"admin.application.brand #XiwOkdBO9WY07AooUVAZ": "array:4 [\n  \"data\" => array:14 [\n    \"saveForm\" => App\\Livewire\\Admin\\Application\\Forms\\BrandForm {#765\n      #component: App\\Livewire\\Admin\\Application\\Brand {#732\n        #__id: \"XiwOkdBO9WY07AooUVAZ\"\n        #__name: \"admin.application.brand\"\n        #listeners: []\n        #attributes: Livewire\\Features\\SupportAttributes\\AttributeCollection {#937\n          #items: array:1 [\n            0 => Livewire\\Attributes\\Layout {#886\n              #component: App\\Livewire\\Admin\\Application\\Brand {#732}\n              #subTarget: null\n              #subName: null\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#890\n                +name: \"ROOT\"\n              }\n              #levelName: null\n              +name: \"components.layouts.application-layout\"\n              +params: []\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n        #withValidatorCallback: null\n        #rulesFromOutside: []\n        #messagesFromOutside: []\n        #validationAttributesFromOutside: []\n        +guest: false\n        #viewData: []\n        +page: \"brand\"\n        +pageTitle: \"ブランド種類マスタ｜LadyBird\"\n        +redirecting: false\n        +saveForm: App\\Livewire\\Admin\\Application\\Forms\\BrandForm {#765}\n        +customerId: \"6\"\n        +customer: App\\Models\\Customer {#954\n          #connection: \"mysql\"\n          #table: \"customers\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:103 [\n            \"id\" => 6\n            \"last_name\" => \"aa\"\n            \"first_name\" => \"vv\"\n            \"last_name_kana\" => \"フリガナ\"\n            \"first_name_kana\" => \"フリガナ\"\n            \"sex\" => \"1\"\n            \"birthday\" => \"2025-06-04\"\n            \"email\" => null\n            \"tel1\" => \"11\"\n            \"tel2\" => \"22\"\n            \"tel3\" => \"33\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"city\" => \"荒川区\"\n            \"address\" => \"町屋\"\n            \"building\" => null\n            \"pref_kana_id\" => null\n            \"city_kana\" => null\n            \"address_kana\" => null\n            \"building_kana\" => null\n            \"contract_count\" => null\n            \"black_flag\" => \"0\"\n            \"emergency_last_name\" => null\n            \"emergency_first_name\" => null\n            \"emergency_last_name_kana\" => null\n            \"emergency_first_name_kana\" => null\n            \"emergency_tel1\" => null\n            \"emergency_tel2\" => null\n            \"emergency_tel3\" => null\n            \"emergency_zip1\" => null\n            \"emergency_zip2\" => null\n            \"emergency_pref_id\" => null\n            \"emergency_city\" => null\n            \"emergency_address\" => null\n            \"emergency_building\" => null\n            \"relationship_flag\" => null\n            \"relationship_other\" => null\n            \"annual_income\" => null\n            \"company_name\" => null\n            \"company_name_kana\" => null\n            \"company_tel1\" => null\n            \"company_tel2\" => null\n            \"company_tel3\" => null\n            \"company_zip1\" => null\n            \"company_zip2\" => null\n            \"company_pref_id\" => null\n            \"company_city\" => null\n            \"company_address\" => null\n            \"company_building\" => null\n            \"information_input_flag\" => null\n            \"gw_last_name\" => null\n            \"gw_first_name\" => null\n            \"gw_last_name_kana\" => null\n            \"gw_first_name_kana\" => null\n            \"gw_sex\" => null\n            \"gw_birthday\" => null\n            \"gw_tel1\" => null\n            \"gw_tel2\" => null\n            \"gw_tel3\" => null\n            \"gw_zip1\" => null\n            \"gw_zip2\" => null\n            \"gw_pref_id\" => null\n            \"gw_city\" => null\n            \"gw_address\" => null\n            \"gw_building\" => null\n            \"gw_relationship_flag\" => null\n            \"gw_relationship_other\" => null\n            \"gw_company_name\" => null\n            \"gw_company_name_kana\" => null\n            \"gw_company_tel1\" => null\n            \"gw_company_tel2\" => null\n            \"gw_company_tel3\" => null\n            \"gw_company_zip1\" => null\n            \"gw_company_zip2\" => null\n            \"gw_company_pref_id\" => null\n            \"gw_company_city\" => null\n            \"gw_company_address\" => null\n            \"gw_company_building\" => null\n            \"bank_account_name\" => null\n            \"bank_account_name_kana\" => null\n            \"bank_flag\" => null\n            \"bank_account_mark1\" => null\n            \"bank_account_mark2\" => null\n            \"bank_account_mark3\" => null\n            \"bank_account_number\" => null\n            \"bank_code\" => null\n            \"bank_name\" => null\n            \"branch_code\" => null\n            \"branch_name\" => null\n            \"bank_account_type\" => null\n            \"contact_flag\" => null\n            \"contact_hope_date1\" => null\n            \"contact_hope_start_time1\" => null\n            \"contact_hope_end_time1\" => null\n            \"contact_hope_date2\" => null\n            \"contact_hope_start_time2\" => null\n            \"contact_hope_end_time2\" => null\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-23 13:53:23\"\n            \"ins_id\" => 3\n            \"upd_date\" => \"2025-06-23 13:53:23\"\n            \"upd_id\" => 3\n          ]\n          #original: array:103 [\n            \"id\" => 6\n            \"last_name\" => \"aa\"\n            \"first_name\" => \"vv\"\n            \"last_name_kana\" => \"フリガナ\"\n            \"first_name_kana\" => \"フリガナ\"\n            \"sex\" => \"1\"\n            \"birthday\" => \"2025-06-04\"\n            \"email\" => null\n            \"tel1\" => \"11\"\n            \"tel2\" => \"22\"\n            \"tel3\" => \"33\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"city\" => \"荒川区\"\n            \"address\" => \"町屋\"\n            \"building\" => null\n            \"pref_kana_id\" => null\n            \"city_kana\" => null\n            \"address_kana\" => null\n            \"building_kana\" => null\n            \"contract_count\" => null\n            \"black_flag\" => \"0\"\n            \"emergency_last_name\" => null\n            \"emergency_first_name\" => null\n            \"emergency_last_name_kana\" => null\n            \"emergency_first_name_kana\" => null\n            \"emergency_tel1\" => null\n            \"emergency_tel2\" => null\n            \"emergency_tel3\" => null\n            \"emergency_zip1\" => null\n            \"emergency_zip2\" => null\n            \"emergency_pref_id\" => null\n            \"emergency_city\" => null\n            \"emergency_address\" => null\n            \"emergency_building\" => null\n            \"relationship_flag\" => null\n            \"relationship_other\" => null\n            \"annual_income\" => null\n            \"company_name\" => null\n            \"company_name_kana\" => null\n            \"company_tel1\" => null\n            \"company_tel2\" => null\n            \"company_tel3\" => null\n            \"company_zip1\" => null\n            \"company_zip2\" => null\n            \"company_pref_id\" => null\n            \"company_city\" => null\n            \"company_address\" => null\n            \"company_building\" => null\n            \"information_input_flag\" => null\n            \"gw_last_name\" => null\n            \"gw_first_name\" => null\n            \"gw_last_name_kana\" => null\n            \"gw_first_name_kana\" => null\n            \"gw_sex\" => null\n            \"gw_birthday\" => null\n            \"gw_tel1\" => null\n            \"gw_tel2\" => null\n            \"gw_tel3\" => null\n            \"gw_zip1\" => null\n            \"gw_zip2\" => null\n            \"gw_pref_id\" => null\n            \"gw_city\" => null\n            \"gw_address\" => null\n            \"gw_building\" => null\n            \"gw_relationship_flag\" => null\n            \"gw_relationship_other\" => null\n            \"gw_company_name\" => null\n            \"gw_company_name_kana\" => null\n            \"gw_company_tel1\" => null\n            \"gw_company_tel2\" => null\n            \"gw_company_tel3\" => null\n            \"gw_company_zip1\" => null\n            \"gw_company_zip2\" => null\n            \"gw_company_pref_id\" => null\n            \"gw_company_city\" => null\n            \"gw_company_address\" => null\n            \"gw_company_building\" => null\n            \"bank_account_name\" => null\n            \"bank_account_name_kana\" => null\n            \"bank_flag\" => null\n            \"bank_account_mark1\" => null\n            \"bank_account_mark2\" => null\n            \"bank_account_mark3\" => null\n            \"bank_account_number\" => null\n            \"bank_code\" => null\n            \"bank_name\" => null\n            \"branch_code\" => null\n            \"branch_name\" => null\n            \"bank_account_type\" => null\n            \"contact_flag\" => null\n            \"contact_hope_date1\" => null\n            \"contact_hope_start_time1\" => null\n            \"contact_hope_end_time1\" => null\n            \"contact_hope_date2\" => null\n            \"contact_hope_start_time2\" => null\n            \"contact_hope_end_time2\" => null\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-23 13:53:23\"\n            \"ins_id\" => 3\n            \"upd_date\" => \"2025-06-23 13:53:23\"\n            \"upd_id\" => 3\n          ]\n          #changes: []\n          #casts: array:7 [\n            \"sex\" => \"App\\Enums\\SexEnum\"\n            \"black_flag\" => \"App\\Enums\\BlackFlagEnum\"\n            \"relationship_flag\" => \"App\\Enums\\RelationshipFlagEnum\"\n            \"information_input_flag\" => \"App\\Enums\\InformationInputFlagEnum\"\n            \"bank_flag\" => \"App\\Enums\\BankFlagEnum\"\n            \"bank_account_type\" => \"App\\Enums\\BankAccountTypeEnum\"\n            \"contact_flag\" => \"App\\Enums\\ContactFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:103 [\n            0 => \"id\"\n            1 => \"last_name\"\n            2 => \"first_name\"\n            3 => \"last_name_kana\"\n            4 => \"first_name_kana\"\n            5 => \"sex\"\n            6 => \"birthday\"\n            7 => \"email\"\n            8 => \"tel1\"\n            9 => \"tel2\"\n            10 => \"tel3\"\n            11 => \"zip1\"\n            12 => \"zip2\"\n            13 => \"pref_id\"\n            14 => \"city\"\n            15 => \"address\"\n            16 => \"building\"\n            17 => \"pref_kana_id\"\n            18 => \"city_kana\"\n            19 => \"address_kana\"\n            20 => \"building_kana\"\n            21 => \"contract_count\"\n            22 => \"black_flag\"\n            23 => \"emergency_last_name\"\n            24 => \"emergency_first_name\"\n            25 => \"emergency_last_name_kana\"\n            26 => \"emergency_first_name_kana\"\n            27 => \"emergency_tel1\"\n            28 => \"emergency_tel2\"\n            29 => \"emergency_tel3\"\n            30 => \"emergency_zip1\"\n            31 => \"emergency_zip2\"\n            32 => \"emergency_pref_id\"\n            33 => \"emergency_city\"\n            34 => \"emergency_address\"\n            35 => \"emergency_building\"\n            36 => \"relationship_flag\"\n            37 => \"relationship_other\"\n            38 => \"annual_income\"\n            39 => \"company_name\"\n            40 => \"company_name_kana\"\n            41 => \"company_tel1\"\n            42 => \"company_tel2\"\n            43 => \"company_tel3\"\n            44 => \"company_zip1\"\n            45 => \"company_zip2\"\n            46 => \"company_pref_id\"\n            47 => \"company_city\"\n            48 => \"company_address\"\n            49 => \"company_building\"\n            50 => \"information_input_flag\"\n            51 => \"gw_last_name\"\n            52 => \"gw_first_name\"\n            53 => \"gw_last_name_kana\"\n            54 => \"gw_first_name_kana\"\n            55 => \"gw_sex\"\n            56 => \"gw_birthday\"\n            57 => \"gw_tel1\"\n            58 => \"gw_tel2\"\n            59 => \"gw_tel3\"\n            60 => \"gw_zip1\"\n            61 => \"gw_zip2\"\n            62 => \"gw_pref_id\"\n            63 => \"gw_city\"\n            64 => \"gw_address\"\n            65 => \"gw_building\"\n            66 => \"gw_relationship_flag\"\n            67 => \"gw_relationship_other\"\n            68 => \"gw_company_name\"\n            69 => \"gw_company_name_kana\"\n            70 => \"gw_company_tel1\"\n            71 => \"gw_company_tel2\"\n            72 => \"gw_company_tel3\"\n            73 => \"gw_company_zip1\"\n            74 => \"gw_company_zip2\"\n            75 => \"gw_company_pref_id\"\n            76 => \"gw_company_city\"\n            77 => \"gw_company_address\"\n            78 => \"gw_company_building\"\n            79 => \"bank_account_name\"\n            80 => \"bank_account_name_kana\"\n            81 => \"bank_flag\"\n            82 => \"bank_account_mark1\"\n            83 => \"bank_account_mark2\"\n            84 => \"bank_account_mark3\"\n            85 => \"bank_account_number\"\n            86 => \"bank_code\"\n            87 => \"bank_name\"\n            88 => \"branch_code\"\n            89 => \"branch_name\"\n            90 => \"bank_account_type\"\n            91 => \"contact_flag\"\n            92 => \"contact_hope_date1\"\n            93 => \"contact_hope_start_time1\"\n            94 => \"contact_hope_end_time1\"\n            95 => \"contact_hope_date2\"\n            96 => \"contact_hope_start_time2\"\n            97 => \"contact_hope_end_time2\"\n            98 => \"del_flag\"\n            99 => \"ins_id\"\n            100 => \"ins_date\"\n            101 => \"upd_id\"\n            102 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        +application: null\n        +brandShopList: Illuminate\\Database\\Eloquent\\Collection {#899\n          #items: []\n          #escapeWhenCastingToString: false\n        }\n        +productGeneral: array:1 [\n          0 => array:3 [\n            \"course_id\" => \"\"\n            \"quantity\" => 2\n            \"amount\" => \"\"\n          ]\n        ]\n        +productOptional: array:1 [\n          0 => array:3 [\n            \"course_id\" => \"\"\n            \"quantity\" => 0\n            \"amount\" => \"\"\n          ]\n        ]\n        +courseGeneralList: array:2 [\n          0 => array:5 [\n            \"id\" => 9\n            \"name_management\" => \"adasd\"\n            \"name_application\" => \"aaaaa\"\n            \"type\" => 1\n            \"unit_price\" => 1\n          ]\n          1 => array:5 [\n            \"id\" => 14\n            \"name_management\" => \"sdfsdf\"\n            \"name_application\" => \"\"\n            \"type\" => 1\n            \"unit_price\" => 1\n          ]\n        ]\n        +courseOptionalList: array:2 [\n          0 => array:5 [\n            \"id\" => 8\n            \"name_management\" => \"cccc\"\n            \"name_application\" => \"aaa\"\n            \"type\" => 2\n            \"unit_price\" => 0\n          ]\n          1 => array:5 [\n            \"id\" => 12\n            \"name_management\" => \"sadsa\"\n            \"name_application\" => \"dasdasd\"\n            \"type\" => 2\n            \"unit_price\" => 0\n          ]\n        ]\n        +totalAmount: 0\n      }\n      #propertyName: \"saveForm\"\n      #withValidatorCallback: null\n      #rulesFromOutside: []\n      #messagesFromOutside: []\n      #validationAttributesFromOutside: []\n      +shop_brand_id: \"\"\n      +staff_name: \"\"\n      +fee_type: \"2\"\n      +product_general: array:1 [\n        0 => array:3 [\n          \"course_id\" => \"\"\n          \"quantity\" => 2\n          \"amount\" => \"\"\n        ]\n      ]\n      +product_optional: array:1 [\n        0 => array:3 [\n          \"course_id\" => \"\"\n          \"quantity\" => 0\n          \"amount\" => \"\"\n        ]\n      ]\n    }\n    \"customerId\" => \"6\"\n    \"customer\" => App\\Models\\Customer {#954}\n    \"application\" => null\n    \"brandShopList\" => Illuminate\\Database\\Eloquent\\Collection {#899}\n    \"productGeneral\" => array:1 [\n      0 => array:3 [\n        \"course_id\" => \"\"\n        \"quantity\" => 2\n        \"amount\" => \"\"\n      ]\n    ]\n    \"productOptional\" => array:1 [\n      0 => array:3 [\n        \"course_id\" => \"\"\n        \"quantity\" => 0\n        \"amount\" => \"\"\n      ]\n    ]\n    \"courseGeneralList\" => array:2 [\n      0 => array:5 [\n        \"id\" => 9\n        \"name_management\" => \"adasd\"\n        \"name_application\" => \"aaaaa\"\n        \"type\" => 1\n        \"unit_price\" => 1\n      ]\n      1 => array:5 [\n        \"id\" => 14\n        \"name_management\" => \"sdfsdf\"\n        \"name_application\" => \"\"\n        \"type\" => 1\n        \"unit_price\" => 1\n      ]\n    ]\n    \"courseOptionalList\" => array:2 [\n      0 => array:5 [\n        \"id\" => 8\n        \"name_management\" => \"cccc\"\n        \"name_application\" => \"aaa\"\n        \"type\" => 2\n        \"unit_price\" => 0\n      ]\n      1 => array:5 [\n        \"id\" => 12\n        \"name_management\" => \"sadsa\"\n        \"name_application\" => \"dasdasd\"\n        \"type\" => 2\n        \"unit_price\" => 0\n      ]\n    ]\n    \"totalAmount\" => 0\n    \"page\" => \"brand\"\n    \"pageTitle\" => \"ブランド種類マスタ｜LadyBird\"\n    \"redirecting\" => false\n    \"guest\" => false\n  ]\n  \"name\" => \"admin.application.brand\"\n  \"component\" => \"App\\Livewire\\Admin\\Application\\Brand\"\n  \"id\" => \"XiwOkdBO9WY07AooUVAZ\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01JZS9SNKV2VM8JJM18Y75BX7D&op=get\"\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Application\\Brand@incrementQtyGeneral<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FApplication%2FBrand.php&line=224\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FApplication%2FBrand.php&line=224\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Application/Brand.php:224-230</a>", "middleware": "web", "duration": "257ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-422113510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-422113510\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-322595182 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1648 characters\">{&quot;data&quot;:{&quot;saveForm&quot;:[{&quot;shop_brand_id&quot;:&quot;&quot;,&quot;staff_name&quot;:&quot;&quot;,&quot;fee_type&quot;:&quot;2&quot;,&quot;product_general&quot;:[[[{&quot;course_id&quot;:&quot;&quot;,&quot;quantity&quot;:1,&quot;amount&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;product_optional&quot;:[[[{&quot;course_id&quot;:&quot;&quot;,&quot;quantity&quot;:0,&quot;amount&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;App\\\\Livewire\\\\Admin\\\\Application\\\\Forms\\\\BrandForm&quot;,&quot;s&quot;:&quot;form&quot;}],&quot;customerId&quot;:&quot;6&quot;,&quot;customer&quot;:[null,{&quot;class&quot;:&quot;App\\\\Models\\\\Customer&quot;,&quot;key&quot;:6,&quot;s&quot;:&quot;mdl&quot;}],&quot;application&quot;:null,&quot;brandShopList&quot;:[null,{&quot;keys&quot;:[],&quot;class&quot;:&quot;Illuminate\\\\Database\\\\Eloquent\\\\Collection&quot;,&quot;modelClass&quot;:null,&quot;s&quot;:&quot;elcln&quot;}],&quot;productGeneral&quot;:[[[{&quot;course_id&quot;:&quot;&quot;,&quot;quantity&quot;:1,&quot;amount&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;productOptional&quot;:[[[{&quot;course_id&quot;:&quot;&quot;,&quot;quantity&quot;:0,&quot;amount&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;courseGeneralList&quot;:[[[{&quot;id&quot;:9,&quot;name_management&quot;:&quot;adasd&quot;,&quot;name_application&quot;:&quot;aaaaa&quot;,&quot;type&quot;:1,&quot;unit_price&quot;:1},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:14,&quot;name_management&quot;:&quot;sdfsdf&quot;,&quot;name_application&quot;:&quot;&quot;,&quot;type&quot;:1,&quot;unit_price&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;courseOptionalList&quot;:[[[{&quot;id&quot;:8,&quot;name_management&quot;:&quot;cccc&quot;,&quot;name_application&quot;:&quot;aaa&quot;,&quot;type&quot;:2,&quot;unit_price&quot;:0},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;id&quot;:12,&quot;name_management&quot;:&quot;sadsa&quot;,&quot;name_application&quot;:&quot;dasdasd&quot;,&quot;type&quot;:2,&quot;unit_price&quot;:0},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;totalAmount&quot;:0,&quot;page&quot;:&quot;brand&quot;,&quot;pageTitle&quot;:&quot;\\u30d6\\u30e9\\u30f3\\u30c9\\u7a2e\\u985e\\u30de\\u30b9\\u30bf\\uff5cLadyBird&quot;,&quot;redirecting&quot;:false,&quot;guest&quot;:false},&quot;memo&quot;:{&quot;id&quot;:&quot;XiwOkdBO9WY07AooUVAZ&quot;,&quot;name&quot;:&quot;admin.application.brand&quot;,&quot;path&quot;:&quot;management\\/customers\\/6\\/application\\/brand&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;2238345198-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;5900b58566643fe6c2be657e78842c8c042946a173aa3266e359549ce258ca5d&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"19 characters\">incrementQtyGeneral</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>0</span>\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322595182\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1451979007 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2115</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/management/customers/6/application/brand</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjI3QTl0aFZTdU5Cd3o5VGd5Z24zSEE9PSIsInZhbHVlIjoibTl1VCtXWDVhL0xMaGNMc1JzeGNmWkwxQVg1VmI5K1I4TUJ2ci8xUHBXUjhwMi9YTjZjQk5TcmNjRmpKemtuL0VGOVZyYkU2ZnF6blIzWHZuV1FpdktKQnNiS0JZeldUb3pGQ3pCUWNDaEtTL1JsWk53bytUY2ZSdHY1cE12ekRlS0ppRWw2SDZVc0lsQXVyaWpGMW5BPT0iLCJtYWMiOiIyY2Y5ZTEyZmE5NjQyYmJkODE5NzIyMjFhODM3NDc5NjE2YTVlZDYxMzU1MmNkZmQ5YzJjYTI3OTA3NWE0NTA3IiwidGFnIjoiIn0%3D; ladybird_session=VYL4AtscJdT5gtzU7vz9eiCbIGhcbGn54iXnU9z5; XSRF-TOKEN=eyJpdiI6IlR4N1B0ODcvKzYyVXc3bVhXbTlmRmc9PSIsInZhbHVlIjoibGhKS05kMDFIWlB2Q3gzWkloTHBVRWdCVmNhYzlaV3FqR2NzdnlCRHdKUmRaVU5MZ1RqNkpKWG1TeGErdXF4MU9wN3ZlMHJaeVdqU3dWMnE5WFByRmp0TXhNR0djc2YzNVRkNzYzdVgxa2publZkVWJqM3NlZVZLdmdnNEErZ1giLCJtYWMiOiI1ZjRkOGI2N2QzM2RjMzg5YzhlMzA1NjFmNTI5NGNkZDdkMTIzYTNiNzI0ZWVkNjI4MTMwZGZmNDAwOTRjNzAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451979007\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-410371781 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">2||$2y$10$s0LI87x0oyRCf8LmrLWHVe24uMnHpU1z102HiqCMoYnnol50W9DsC</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410371781\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-374340967 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 04:34:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374340967\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-659996894 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01JZS9SNKV2VM8JJM18Y75BX7D&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659996894\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}