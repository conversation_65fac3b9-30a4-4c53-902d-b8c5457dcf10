<div class="scroll-validate">
    <div class="w-px-740 mw-100 mx-auto">
        <table class="table table-edit-borderless">
            <thead>
                <tr>
                    <th scope="col">{{ transm('holidays.attributes.holiday_date') }}</th>
                    <th scope="col">{{ transm('holidays.attributes.name') }}</th>
                </tr>
            </thead>
            <tbody>
                <div id="child-data"
                    data-selected-dates='@json($selectedHoliday)'>
                </div>

                @foreach ($form->holidays as $index => $holiday)
                    <tr class="form-parts" wire:key="{{ $index }}" data-parts-id="{{ $index }}">
                        <td>
                            <input type="text" name="" wire:model="form.holidays.{{ $index }}.holiday_date"
                                x-init="setTimeout(() => initializeDatepickers(@js($yearIndex)), 100)"
                                class="form-control datepicker w-px-230" data-date-format="yy/mm/dd">

                        </td>
                        <td class="w-100">
                            <div class="d-flex">
                                <input type="text" name="" wire:model="form.holidays.{{ $index }}.name"
                                    class="form-control flex-grow-1 me-3">
                                <button type="button" class="form-delete-button delete-form-parts" data-bs-toggle="modal" data-bs-target="#confirmDeleteModal" wire:click="getDeleteData({{data_get($holiday, 'id')}})"></button>
                            </div>
                        </td>
                    </tr>

                    @if($errors->has('form.holidays.' . $index . '.name') || $errors->has('form.holidays.' . $index . '.holiday_date'))
                        <tr class="form-parts">
                            <td>
                                @error('form.holidays.' . $index . '.holiday_date') <span class="message-error">{{ $message }}</span> @enderror
                            </td>
                            <td>
                                @error('form.holidays.' . $index . '.name') <span class="message-error">{{ $message }}</span> @enderror
                            </td>
                        </tr>
                    @endif
                @endforeach

                {{-- list add new --}}
                @foreach ($form->newHolidays as $index => $newHoliday)
                    <tr class="form-parts" wire:key="{{ 'new' . $index }}"
                        data-parts-id="{{ count($form->holidays) + $index }}">
                        <td>
                            <input type="text" name=""
                                wire:model="form.newHolidays.{{ $index }}.holiday_date"
                                x-init="setTimeout(() => initializeDatepickers(@js($yearIndex)), 100)"
                                class="form-control datepicker w-px-230" data-date-format="yy/mm/dd">
                        </td>
                        <td class="w-100">
                            <div class="d-flex">
                                <input type="text" name="" wire:model="form.newHolidays.{{ $index }}.name" class="form-control flex-grow-1 me-3">

                                @if ($loop->last)
                                    <button type="button" class="form-add-button" wire:click.prevent="addNewRow"></button>
                                @else
                                <button type="button" class="form-delete-button delete-form-parts" data-bs-toggle="modal" data-bs-target="#confirmDeleteModal" wire:click="getDeleteData({{$index}},{{true}})"></button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @if($errors->has('form.newHolidays.' . $index . '.name') || $errors->has('form.newHolidays.' . $index . '.holiday_date'))
                        <tr class="form-parts">
                            <td>
                                @error('form.newHolidays.' . $index . '.holiday_date') <span class="message-error">{{ $message }}</span> @enderror
                            </td>
                            <td>
                                @error('form.newHolidays.' . $index . '.name') <span class="message-error">{{ $message }}</span> @enderror
                            </td>
                        </tr>
                    @endif
                @endforeach
            </tbody>
            <livewire:common.delete-confirm-modal />
        </table>
        <div class="text-end mt-4">
            <button type="button" class="btn btn-dark btn-medium" wire:click.prevent="validateForm">{{ trans2('button.update') }}</button>
        </div>
    </div>
</div>
