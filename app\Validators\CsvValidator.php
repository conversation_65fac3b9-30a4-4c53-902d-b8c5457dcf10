<?php

namespace App\Validators;

use App\Enums\BankAccountTypeEnum;
use App\Enums\CarDepreciationFlagEnum;
use App\Enums\CarDepreciationStatusEnum;
use App\Enums\CarDepreciationTypeEnum;
use App\Enums\CarPurchaseTypeEnum;
use App\Enums\LoanScheduleTypeEnum;
use App\Enums\ResultStatusEnum;
use App\Enums\StatusEnum;
use App\Enums\SupplierStatusEnum;
use App\Enums\TypeEnum;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CsvValidator
{
    public function validateData($params, $screen, $attributes)
    {
        $messages = [];

        switch ($screen) {
            case getConstant('SCREENS.PAYMENT'):
                $rules = $this->paymentRules($params);
                break;
            default:
                $rules = [];
                break;
        }

        $defaultMessages = [
            '*.check_date_yyyy_mm_format' => trans('validation.date'),
            '*.check_date_format' => trans('validation.date_format_ymd'),
            '*.check_date_format_csv' => trans('validation.date_format_ymd'),
            '*.check_email' => trans('validation.email_format'),
        ];
        $messages = array_merge($messages, $defaultMessages);

        return Validator::make($params,
            $rules,
            $messages,
            $attributes,
        );
    }

    protected function paymentRules($params)
    {
        $dateRule = $this->dateRule();
        $costRule = $this->costRule();
        $numberOfPaymentRule = $this->numberOfPaymentRule();
        $rules = [
            'id' => [
                'required',
                'numeric',
                'in:999999',
            ],
            'payment_plan_date' => [
                'required',
                'regex:/^\d{4}(0[1-9]|1[0-2])$/',
            ],
            'bank_code' => [
                'required',
                'numeric',
                'max:9900'
            ],
            'branch_code' => 'required|numeric|max:999',
            'bank_account_type' => [
                'required',
                Rule::in(BankAccountTypeEnum::getValues())
            ],
            'bank_account_number' => 'required|number|max:32',
            'bank_account_name_kana' => $this->bankAccountKanaValidationRule(),
            'total_amount' => 'required|' . $costRule,
            'type' => [
                'required',
                Rule::in(LoanScheduleTypeEnum::getValues())
            ],
            'regist_number' => [
                'required',
                'number',
                'max:14',
                Rule::exists('applications', 'regist_number')->where('del_flag', 0)
            ],
            'result_status' => [
                'required',
                Rule::in(ResultStatusEnum::getValues())
            ],
        ];
        return $rules;
    }

    protected function dateRule(string $format = 'Y/m/d'): string
    {
        return "bail|date|check_date_format_csv";
    }

    protected function costRule(): string
    {
        return "bail|numeric|max:********.99|gt:0|regex:/^\d{1,8}(\.\d{1,2})?$/";
    }

    protected function numberOfPaymentRule(string $min = '-32768', string $max = '32767'): string
    {
        return "bail|integer|min:{$min}|max:{$max}";
    }

    public function bankAccountKanaValidationRule(): array
    {
        return [
            'required',
            'string',
            'max:512',
            function ($attribute, $value, $fail) {
                if (!preg_match('/^[ｱ-ﾝﾞﾟA-Z0-9\(\)\.\-\/ ]+$/u', $value)) {
                    $fail(__('口座名義（フリガナ）には、半角カタカナ／半角英数字（大文字）／記号を入力してください。'));
                }
            },
        ];
    }
}
