@push('scripts')
<script src="{{ asset('assets/admin/js/page/customer/postcode.js') }}"></script>
<script>
    const zip1FormField = document.querySelector('input[name=zip1]');
    const zip2FormField = document.querySelector('input[name=zip2]');
    const addressFormField = document.querySelector('input[name=address]');
    const cityFormField = document.querySelector('input[name=city]');
    const buildingFormField = document.querySelector('input[name=building]');
    const prefFormField = document.querySelector('select[name=pref_id]');
    //name kana
    const prefKanaFormField = document.querySelector('select[name=pref_kana_id]');
    const addressKanaFormField = document.querySelector('input[name=address_kana]');
    const cityKanaFormField = document.querySelector('input[name=city_kana]');
    //gw
    const zip1GwFormField = document.querySelector('input[name=gw_company_zip1]');
    const zip2GwFormField = document.querySelector('input[name=gw_company_zip2]');
    const prefGwFormField = document.querySelector('select[name=company_pref_id]');
    const cityGwFormField = document.querySelector('input[name=gw_company_city]');
    const addressGwFormField = document.querySelector('input[name=gw_company_address]');

    const onChangeZipCode = () => {
        if (!zip1FormField || !zip2FormField) return;

        let zip1 = zip1FormField.value.trim();
        let zip2 = zip2FormField.value.trim();
        if (zip1.length !== 3 || zip2.length !== 4) return;

        let postcode = zip1 + zip2;

        getAddressFromPostcode(postcode).then(addressData => {
            let prefCode = addressData.data?.prefCode ?? '';
            let city = addressData.data?.city ?? '';
            let address = addressData.data?.town ?? '';
            let cityKana = addressData.data?.fullWidthKana?.city ?? '';
            let addressKana = addressData.data?.fullWidthKana?.town ?? '';

            const componentId = cityFormField?.closest('[wire\\:id]')?.getAttribute('wire:id');
            if (!componentId) return;

            const safeSet = (el, model, val) => {
                if (el && model && val != null && val !== undefined && val !== '' && el.value !== val) {
                    el.value = val;
                    Livewire.find(componentId).set(model, val);
                }
            };

            safeSet(cityFormField, cityFormField?.getAttribute('wire:model'), city);
            safeSet(addressFormField, addressFormField?.getAttribute('wire:model'), address);
            safeSet(cityKanaFormField, cityKanaFormField?.getAttribute('wire:model'), cityKana);
            safeSet(addressKanaFormField, addressKanaFormField?.getAttribute('wire:model'), addressKana);

            if (prefFormField) {
                prefFormField.value = parseInt(prefCode);
                $(prefFormField).trigger('change');
                const model = prefFormField.getAttribute('wire:model');
                if (model) Livewire.find(componentId).set(model, parseInt(prefCode));
            }

            if (prefKanaFormField) {
                prefKanaFormField.value = parseInt(prefCode);
                $(prefKanaFormField).trigger('change');
                const model = prefKanaFormField.getAttribute('wire:model');
                if (model) Livewire.find(componentId).set(model, parseInt(prefCode));
            }
        });
    };

    const onChangeZipCodeCompnay = () => {
        if (!zip1GwFormField || !zip2GwFormField) return;

        let zip1 = zip1GwFormField.value.trim();
        let zip2 = zip2GwFormField.value.trim();
        if (zip1.length !== 3 || zip2.length !== 4) return;

        let postcode = zip1 + zip2;

        getAddressFromPostcode(postcode).then(addressData => {
            let prefCode = addressData.data?.prefCode ?? '';
            let city = addressData.data?.city ?? '';
            let address = addressData.data?.town ?? '';

            const componentId = cityGwFormField?.closest('[wire\\:id]')?.getAttribute('wire:id');
            if (!componentId) return;

            const safeSet = (el, model, val) => {
                if (el && model && val != null && val !== undefined && val !== '' && el.value !== val) {
                    el.value = val;
                    Livewire.find(componentId).set(model, val);
                }
            };

            safeSet(cityGwFormField, cityGwFormField?.getAttribute('wire:model'), city);
            safeSet(addressGwFormField, addressGwFormField?.getAttribute('wire:model'), address);

            if (prefGwFormField) {
                prefGwFormField.value = parseInt(prefCode);
                $(prefGwFormField).trigger('change');
                const model = prefGwFormField.getAttribute('wire:model');
                if (model) Livewire.find(componentId).set(model, parseInt(prefCode));
            }
        });
    };

    zip1FormField?.addEventListener('change', onChangeZipCode);
    zip2FormField?.addEventListener('blur', onChangeZipCode);
    zip1GwFormField?.addEventListener('change', onChangeZipCodeCompnay);
    zip2GwFormField?.addEventListener('blur', onChangeZipCodeCompnay);
</script>
<script>
    window.addEventListener('init-select2', function () {
            setTimeout(() => {
                initSelect2Binding();
                initDatepickerBinding(); 
            }, 50);
        });

        document.addEventListener('livewire:initialized', () => {
            initSelect2Binding();
            initDatepickerBinding(); 
        });

        document.addEventListener("livewire:navigated", () => {
            initSelect2Binding();
            initDatepickerBinding();
        });

        function initSelect2Binding() {
            document.querySelectorAll('select.form-select2').forEach(function (el) {
                if (!$(el).hasClass('select2-hidden-accessible')) {
                    $(el).select2().on('change', function () {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');

                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                        }
                    });
                }
            });
        }

        function initDatepickerBinding() {
            document.querySelectorAll('.datepicker').forEach(function (el) {
                if (!el.classList.contains('datepicker-initialized')) {
                    el.classList.add('datepicker-initialized');

                    $(el).datepicker({
                        dateFormat: 'yy年mm月dd日',
                        onSelect: function (jpDateText) {
                            const model = el.getAttribute('wire:model');
                            if (model) {
                                const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                                const valueToLivewire = convertToYmd(jpDateText);
                                Livewire.find(componentId).set(model, valueToLivewire);
                                el.value = jpDateText;
                            }
                        }
                    });
                }
            });
        }

        function convertToYmd(jpDate) {
            return jpDate
                .replace('年', '-')
                .replace('月', '-')
                .replace('日', '')
                .replace(/\s/g, '');
        }
</script>
@endpush