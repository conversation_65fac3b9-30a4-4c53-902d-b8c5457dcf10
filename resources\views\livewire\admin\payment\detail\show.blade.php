@extends('livewire.admin.payment.detail.index')
@section('detail-tab')
<div>
    <div class="card mb-3">
        <div class="card-body">
            <div class="small fc-secandary mb-5">入金詳細（支払ID：{{data_get($loanSchedule, 'id') ?? '' }}）</div>
            <div class="container-min">
                <div class="d-flex align-items-center mb-5">
                    <h2 class="heading-2 mt-0 mb-0 me-5">入金予定日：{{data_get($loanSchedule, 'payment_plan_date') ?? '' }}
                    </h2>
                    <dl class="d-flex align-items-center">
                        <dt>入金ステータス：</dt>
                        <dd class="ms-2">
                            <span
                                class="badge badge-status rounded-pill badge-{{ $payment_status_badge_class }} px-4 fs-14 mb-1">
                                {{ $payment_status_text }}
                            </span>
                        </dd>
                    </dl>
                </div>
                <ul class="page-content-tabs nav nav-tabs mb-4">
                    <li class="nav-item flex-grow-1">
                        <a href="#section-1" class="nav-link active">入金の基本情報</a>
                    </li>
                    <li class="nav-item flex-grow-1">
                        <a href="#section-2" class="nav-link">入金情報</a>
                    </li>
                    <li class="nav-item flex-grow-1">
                        <a href="#section-3" class="nav-link">履歴</a>
                    </li>
                    <li class="nav-item flex-grow-1">
                        <a href="#section-4" class="nav-link">後続事務</a>
                    </li>
                    <li class="nav-item flex-grow-1">
                        <a href="#section-5" class="nav-link">関連する入金情報</a>
                    </li>

                </ul>
                <div id="section-1" class="section">
                    <h2 class="heading-2 mt-0">入金の基本情報</h2>
                    <table class="table table-details">
                        <tbody>
                            <tr>
                                <th>支払ID</th>
                                <td>{{data_get($loanSchedule, 'id') ?? '' }}</td>
                            </tr>
                            <tr>
                                <th>入金予定日</th>
                                <td>{{data_get($loanSchedule, 'payment_plan_date') ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>支払種別</th>
                                <td>{{$payment_type_text}}</td>
                            </tr>
                            <tr>
                                <th>{{$payment_company_flag_text}}登録番号</th>
                                <td>{{data_get($loanSchedule->application,'regist_number')}}</td>
                            </tr>
                            <tr>
                                <th>お名前</th>
                                <td><a href="{{ getRoute('customer.details', ['id' => $loanSchedule->customer->id]) }}">{{
                                        $loanSchedule->customer->last_name }}　{{
                                        $loanSchedule->customer->first_name }}</a></td>
                            </tr>
                            <tr>
                                <th>お名前（フリガナ）</th>
                                <td>{{ $loanSchedule->customer->last_name_kana }}　{{
                                    $loanSchedule->customer->first_name_kana }}</td>
                            </tr>
                            <tr>
                                <th>契約ステータス</th>
                                <td><span class="status-icon {{$contract_status_colors}}"></span>{{$contract_status_text
                                    ?? ''}}
                                </td>
                            </tr>
                            <tr>
                                <th>契約内容</th>
                                <td>
                                    <div class="d-flex align-items-start justify-content-between">
                                        @foreach($applicationCourse as $course)
                                        {{$course->shop_brand_name}}<br>
                                        {{$course->item_type_name}}<br>
                                        {{$course->course_name_application}}<br><br>
                                        @endforeach
                                        <a href="{{getRoute('contract.details', ['id' => $loanSchedule->application->id])}}"
                                            class="small">契約詳細</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div id="section-2" class="section">
                    <h2 class="heading-2 mt-0">入金情報</h2>
                    <div class="row">
                        <div class="col-6 pe-4">
                            <table class="table table-details">
                                <tbody>
                                    <tr>
                                        <th class="w-px-180">ステータス</th>
                                        <td><span class="status-icon {{ $payment_status_badge_class }}"></span>{{
                                            $payment_status_text }}</td>
                                    </tr>
                                    <tr>
                                        <th class="w-px-180">入金予定日</th>
                                        <td>{{$loanSchedule->payment_plan_date}}</td>
                                    </tr>
                                    <tr>
                                        <th class="w-px-180">実入金日</th>
                                        <td>
                                            @if($paymentDate->isEmpty())
                                            -
                                            @else
                                            {!! $paymentDate->pluck('payment_date')->implode('<br>') !!}
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="w-px-180">実返金日</th>
                                        <td>
                                            @if($loanSchedule->loanRefunds->isEmpty())
                                            -
                                            @else
                                            {!! $loanSchedule->loanRefunds->pluck('payment_date')->implode('<br>') !!}
                                            @endif
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-6 ps-4">
                            <table class="table table-details">
                                <tbody>
                                    <tr>
                                        <th class="w-px-180">支払総額</th>
                                        <td>{{ formatCurrency($loanSchedule->total_amount) }}</td>
                                    </tr>
                                    <tr>
                                        <th class="w-px-180">元金（税込）</th>
                                        <td>{{ formatCurrency(($loanSchedule->total_amount) -
                                            $perPaymentAmount)}}</td>
                                    </tr>
                                    <tr>
                                        <th class="w-px-180">分割手数料</th>
                                        <td>{{ ($perPaymentAmount > 0) ? formatCurrency($perPaymentAmount) : '-'
                                            }}</td>
                                    </tr>
                                    <tr>
                                        <th class="w-px-180">損害遅延金</th>
                                        <td>{{ ($loanSchedule->loanArrears->sum('delay_damage_amount') > 0) ?
                                            formatCurrency($loanSchedule->loanArrears->sum('delay_damage_amount')) : '-'
                                            }}</td>
                                    </tr>
                                    <tr>
                                        <th class="w-px-180">再発行手数料</th>
                                        <td>{{ $loanSchedule->loanArrears->sum('reissue_fee') > 0 ?
                                            formatCurrency($loanSchedule->loanArrears->sum('reissue_fee')) : '-' }}</td>
                                    </tr>
                                    <tr>
                                        <th class="w-px-180">過不足金</th>
                                        <td>{{formatCurrency($loanSchedule->balance_difference) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div id="section-3" class="section">
                    <h2 class="heading-2 mt-0">履歴</h2>
                    <table class="table table-border">
                        <thead>
                            <tr>
                                <th scope="col">履歴番号</th>
                                <th scope="col">入金ステータス</th>
                                <th scope="col">実入金日/実返金日</th>
                                <th scope="col">更新日</th>
                                <th scope="col">更新者</th>
                                <th scope="col">備考</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($loanScheduleTransaction as $item)
                            <tr>
                                <td>{{$item->loan_schedule_id}}</td>
                                <td>
                                    <span
                                        class="badge badge-status badge-lg rounded-pill badge-{{$payment_status_badge_class}}">{{$payment_status_text}}</span>
                                </td>
                                <td>{{ \Carbon\Carbon::parse($item->transaction_date)->format('Y/m/d') ?? '-' }}</td>
                                <td>{{ \Carbon\Carbon::parse($item->upd_date)->format('Y/m/d') ?? '-'}}</td>
                                <td>{{ $item->name ?? '-'}}</td>
                                <td>{{ $item->comment ?? '-'}}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div id="section-4" class="section">
                    <h2 class="heading-2 mt-0">後続事務</h2>
                    <div class="row">
                        @if($loanSchedule->payment_status->value == \App\Enums\PaymentStatusEnum::WAITING_FOR_PAYMENT)
                        <div class="col-4">
                            <a href="{{ getRoute('payment.details.registration', ['id' => $loanSchedule->id, 'status' => \App\Enums\PaymentStatusEnum::PAID]) }}"
                                class="btn btn-dark p-4 w-100 mb-3">入金済の登録</a>
                            <ul>
                                <li class="small mb-1">・入金予定日に入金された場合</li>
                                <li class="small mb-1">・<span class="status-icon status-success"></span>入金済みにステータス変更
                                </li>
                            </ul>
                        </div>
                        <div class="col-4">
                            <a href="{{ getRoute('payment.details.registration', ['id' => $loanSchedule->id, 'status' => \App\Enums\PaymentStatusEnum::PAID_LUMP_SUM]) }}"
                                class="btn btn-dark p-4 w-100 mb-3">入金済（一括入金）の登録</a>
                            <ul>
                                <li class="small mb-1">・過去の未入金分がすべて入金された場合</li>
                                <li class="small mb-1">・<span
                                        class="status-icon status-success"></span>入金済（一括入金）にステータス変更</li>
                            </ul>
                        </div>
                        @endif
                        @if($loanSchedule->payment_status->value == \App\Enums\PaymentStatusEnum::PAID ||
                        $loanSchedule->payment_status->value == \App\Enums\PaymentStatusEnum::PAID_DEFERRED_RESOLVED ||
                        $loanSchedule->payment_status->value == \App\Enums\PaymentStatusEnum::PAID_LUMP_SUM ||
                        $loanSchedule->payment_status->value == \App\Enums\PaymentStatusEnum::PAID_OVERPAYMENT_RESOLVED)
                        <div class="col-4">
                            <a href="{{ getRoute('payment.details.registration', ['id' => $loanSchedule->id, 'status' => \App\Enums\PaymentStatusEnum::NOT_PAID_OVERDUE]) }}"
                                class="btn btn-dark p-4 w-100 mb-3">未入金に戻す</a>
                            <ul>
                                <li class="small mb-1">・入金予定日に入金されていない場合</li>
                                <li class="small mb-1">・<span class="status-icon status-warning"></span>未入金（遅延中）にステータス変更
                                </li>
                            </ul>
                        </div>
                        @endif
                    </div>
                </div>
                <div id="section-5" class="section pb-4">
                    <h2 class="heading-2 mt-0">関連する入金情報</h2>
                    <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
                        <table class="table table-payment-info">
                            <thead>
                                <tr>
                                    <th scope="col" class="text-center">入金ID</th>
                                    <th scope="col">入金予定日</th>
                                    <th scope="col">ステータス</th>
                                    <th scope="col">請求額</th>
                                    <th scope="col">遅延損害金</th>
                                    <th scope="col">引落結果</th>
                                    <th scope="col">清算日</th>
                                    <th scope="col">手動処理</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($loanScheduleLatestPayment as $item)
                                <tr class="{{$id == $item->loan_schedule_id ? 'current-cell current-cell-top' : ''}}"
                                    onclick="window.location='{{ getRoute('payment.details',['id'=>$item->loan_schedule_id])  }}'"
                                    style="cursor: pointer;">
                                    <td rowspan="2" class="text-center">{{$item->loan_schedule_id}}</td>
                                    <td class="border-start">{{ $item->payment_plan_date ?
                                        \Carbon\Carbon::parse($item->payment_plan_date)->format('Y/m/d') : '' }}</td>
                                    <td>
                                        <span
                                            class="badge badge-status badge-lg rounded-pill badge-{{\App\Enums\PaymentStatusEnum::colors()[$item->payment_status] ?? 'status-secondary'}}">
                                            {{\App\Enums\PaymentStatusEnum::texts()[$item->payment_status] ?? '不明'}}
                                        </span>
                                    </td>
                                    <td>{{ ($item->scheduled_amount > 0) ? str_replace('¥ ', '¥',
                                        formatCurrency($item->scheduled_amount)) : '-' }}</td>
                                    <td>{{ ($item->delay_damage_amount > 0) ? str_replace('¥ ', '¥',
                                        formatCurrency($item->delay_damage_amount)) : '-' }}</td>
                                    <td>{{\App\Enums\ResultStatusEnum::texts()[$item->result_status] ?? '-'}}</td>
                                    <td>{{$item->payment_date ?? '-'}}</td>
                                    <td>{{\App\Enums\AutoManualFlagEnum::texts()[$item->auto_manual_flag] ?? '-'}}</td>
                                </tr>
                                <tr class="{{$id == $item->loan_schedule_id ? 'current-cell current-cell-bottom' : ''}}" onclick="window.location='{{ getRoute('payment.details',['id'=>$item->loan_schedule_id])  }}'"
                                    style="cursor: pointer;">
                                    <td class="text-wrap" colspan="8">{{$item->transaction_comment ?? '-'}}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="text-end">
                    <span class="small me-4">{{ transm('_default.attributes.ins_date') }}：{{ $loanSchedule->ins_date ?
                        \Carbon\Carbon::parse($loanSchedule->ins_date)->format('Y/m/d') : '' }}</span>
                    <span class="small me-4">{{ transm('_default.attributes.upd_date') }}：{{ $loanSchedule->upd_date ?
                        \Carbon\Carbon::parse($loanSchedule->upd_date)->format('Y/m/d') : '' }}</span>
                    <!-- <button type="button" class="small btn-text fc-primary" data-bs-toggle="modal"
                        data-bs-target="#updateHistoryModal">{{ trans2('button.history') }}</button> -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
