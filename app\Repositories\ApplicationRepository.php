<?php

namespace App\Repositories;

use App\Enums\AuthTypeEnum;
use App\Models\Application;
use App\Models\ApplicationCourse;
use App\Models\ApplicationInspectionStatus;
use App\Models\Brand;
use App\Models\Course;
use App\Models\Customer;
use App\Models\ItemType;
use App\Models\LoanArrear;
use App\Models\Shop;
use App\Models\ShopBrand;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApplicationRepository extends CustomRepository
{
    protected $model = Application::class;

    public function getListForSearchContract($dataSearch, $perPage, $isCSV = false)
    {
        $keyword = data_get($dataSearch, 'keyword');
        $brandId = data_get($dataSearch, 'brand_id');
        $shopId = data_get($dataSearch, 'shop_id');
        $applicationDateFrom = data_get($dataSearch, 'application_date_from');
        $applicationDateTo = data_get($dataSearch, 'application_date_to');
        $contractDateFrom = data_get($dataSearch, 'contract_date_from');
        $contractDateTo = data_get($dataSearch, 'contract_date_to');
        $paymentStartMonthFrom = data_get($dataSearch, 'payment_start_month_from');
        $paymentStartMonthTo = data_get($dataSearch, 'payment_start_month_to');
        $contractCancelDateFrom = data_get($dataSearch, 'contract_cancel_date_from');
        $contractCancelDateTo = data_get($dataSearch, 'contract_cancel_date_to');
        $itemTypeId = data_get($dataSearch, 'item_type_id');
        $courseId = data_get($dataSearch, 'course_id');
        $contract_status = data_get($dataSearch, 'contract_status');

        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('customer_id'),
            $this->modelField('contract_id'),
            $this->modelField('brand_id'),
            $this->modelField('shop_brand_id'),
            $this->modelField('application_date'),
            $this->modelField('contract_status'),
            $this->modelField('contract_date'),
            $this->modelField('regist_number'),
            $this->modelField('payment_company_flag'),
        ])
            ->with([
                'customer' => function ($q) {
                    $q->select(
                        Customer::field('id'),
                        Customer::field('first_name'),
                        Customer::field('last_name'),
                        Customer::field('last_name_kana'),
                        Customer::field('first_name_kana'),
                    );
                },
                'brand',
                'courses' => function ($q) {
                    $q->select([
                        Course::field('id'),
                        Course::field('name_application'),
                        Course::field('item_type_id')
                    ]);
                },
                'shopBrand' => function ($q) {
                    $q->select([
                        ShopBrand::field('id'),
                        ShopBrand::field('name')
                    ]);
                },
                'courses.itemType' => function ($q) {
                    $q->select([
                        ItemType::field('id'),
                        ItemType::field('name')
                    ]);
                },
            ])
            ->join(Customer::getTableName(), $this->modelField('customer_id'), Customer::field('id'))
            ->join(ApplicationCourse::getTableName(), $this->modelField('id'), ApplicationCourse::field('application_id'))
            ->join(Course::getTableName(), ApplicationCourse::field('course_id'), Course::field('id'))
            ->when(!blank($keyword), function ($q) use ($keyword) {
                $q->where(Customer::field('first_name'), 'like', "%$keyword%")
                    ->orWhere(Customer::field('last_name'), 'like', "%$keyword%")
                    ->orWhere(Customer::field('first_name_kana'), 'like', "%$keyword%")
                    ->orWhere(Customer::field('last_name_kana'), 'like', "%$keyword%");
            })
            //advanced search
            ->when($brandId, function ($q) use ($brandId) {
                $q->where($this->modelField('brand_id'), $brandId);
            })
            ->when($shopId, function ($q) use ($shopId) {
                $q->where($this->modelField('shop_id'), $shopId);
            })
            ->when($courseId, function ($q) use ($courseId) {
                $q->where(ApplicationCourse::field('course_id'), $courseId);
            })
            ->when($itemTypeId, function ($q) use ($itemTypeId) {
                $q->where(Course::field('item_type_id'), $itemTypeId);
            })

            //application_date
            ->when(!blank($applicationDateFrom) && !blank($applicationDateTo), function ($q) use ($applicationDateFrom, $applicationDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('application_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$applicationDateFrom, $applicationDateTo]);
            })
            ->when(!blank($applicationDateFrom), function ($q) use ($applicationDateFrom) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('application_date') . ", '%Y/%m/%d') >= ?", [$applicationDateFrom]);
            })
            ->when(!blank($applicationDateTo), function ($q) use ($applicationDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('application_date') . ", '%Y/%m/%d') <= ?", [$applicationDateTo]);
            })

            //contract_date
            ->when(!blank($contractDateFrom) && !blank($contractDateTo), function ($q) use ($contractDateFrom, $contractDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('contract_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$contractDateFrom, $contractDateTo]);
            })
            ->when(!blank($contractDateFrom), function ($q) use ($contractDateFrom) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('contract_date') . ", '%Y/%m/%d') >= ?", [$contractDateFrom]);
            })
            ->when(!blank($contractDateTo), function ($q) use ($contractDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('contract_date') . ", '%Y/%m/%d') <= ?", [$contractDateTo]);
            })

            //payment_start_month
            ->when(!blank($paymentStartMonthFrom) && !blank($paymentStartMonthTo), function ($q) use ($paymentStartMonthFrom, $paymentStartMonthTo) {
                $q->whereBetween($this->modelField('payment_start_month'), [$paymentStartMonthFrom, $paymentStartMonthTo]);
            })
            ->when(!blank($paymentStartMonthFrom), function ($q) use ($paymentStartMonthFrom) {
                $q->where($this->modelField('payment_start_month'), '>=', [$paymentStartMonthFrom]);
            })
            ->when(!blank($paymentStartMonthTo), function ($q) use ($paymentStartMonthTo) {
                $q->where($this->modelField('payment_start_month'), '<=', [$paymentStartMonthTo]);
            })

            //contract_cancel_date
            ->when(!blank($contractCancelDateFrom) && !blank($contractCancelDateTo), function ($q) use ($contractCancelDateFrom, $contractCancelDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('contract_cancel_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$contractCancelDateFrom, $contractCancelDateTo]);
            })
            ->when(!blank($contractCancelDateFrom), function ($q) use ($contractCancelDateFrom) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('contract_cancel_date') . ", '%Y/%m/%d') >= ?", [$contractCancelDateFrom]);
            })
            ->when(!blank($contractCancelDateTo), function ($q) use ($contractCancelDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('contract_cancel_date') . ", '%Y/%m/%d') <= ?", [$contractCancelDateTo]);
            })

            //contract_status
            ->when(!empty($contract_status) && !in_array('', $contract_status, true), function ($q) use ($contract_status) {
                $q->whereIn($this->modelField('contract_status'), $contract_status);
            });

            $q->whereNotNull($this->modelField('contract_status'))
            ->groupBy(
                $this->modelField('id'),
                $this->modelField('customer_id'),
                $this->modelField('contract_id'),
                $this->modelField('brand_id'),
                $this->modelField('shop_brand_id'),
                $this->modelField('application_date'),
                $this->modelField('contract_status'),
                $this->modelField('contract_date'),
                $this->modelField('regist_number'),
                $this->modelField('payment_company_flag'),
            );

        return !$isCSV ? $q->paginate($perPage) : $q->get();
    }

    public function getCountAppByCustomerId($customerId)
    {
        return $this->where('customer_id', $customerId)
            ->count();
    }

    public function getApplicationsByCustomerId($customerId)
    {
        return $this->select([
            $this->modelField('id'),
            $this->modelField('customer_id'),
            $this->modelField('status'),
            $this->modelField('shop_brand_id'),
            $this->modelField('brand_id'),
            $this->modelField('shop_id'),
            $this->modelField('application_date'),
            $this->modelField('ins_date'),
        ])->where('customer_id', $customerId)
            ->where('del_flag', 0)
            ->orderByDesc('ins_date')
            ->get();
    }

    public function getApplicationsForContractByCustomer($customer_id)
    {
        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('contract_id'),
            $this->modelField('payment_company_flag'),
            $this->modelField('regist_number'),
            $this->modelField('contract_status'),
            $this->modelField('contract_date'),

            $this->modelField('shop_brand_id'),
        ])
            ->with([
                'courses' => function ($q) {
                    $q->select([
                        Course::field('id'),
                        Course::field('name_application'),
                        Course::field('item_type_id')
                    ]);
                },
                'shopBrand' => function ($q) {
                    $q->select([
                        ShopBrand::field('id'),
                        ShopBrand::field('name')
                    ]);
                },
                'courses.itemType' => function ($q) {
                    $q->select([
                        ItemType::field('id'),
                        ItemType::field('name')
                    ]);
                },
            ])
            ->join(Customer::getTableName(), $this->modelField('customer_id'), Customer::field('id'))
            ->where(Application::field('customer_id'), $customer_id)
            ->whereNotNull(Application::field('contract_status'))
            ->orderBy($this->modelField('upd_date'), 'desc');

        return $q->get();
    }

    public function getApplicationsForContract()
    {
        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('contract_id'),
            $this->modelField('payment_company_flag'),
            $this->modelField('regist_number'),
            $this->modelField('contract_status'),
            $this->modelField('contract_date'),

            $this->modelField('shop_brand_id'),
        ])
            ->with([
                'courses' => function ($q) {
                    $q->select([
                        Course::field('id'),
                        Course::field('name_application'),
                        Course::field('item_type_id')
                    ]);
                },
                'shopBrand' => function ($q) {
                    $q->select([
                        ShopBrand::field('id'),
                        ShopBrand::field('name')
                    ]);
                },
                'courses.itemType' => function ($q) {
                    $q->select([
                        ItemType::field('id'),
                        ItemType::field('name')
                    ]);
                },
            ])
            ->join(Customer::getTableName(), $this->modelField('customer_id'), Customer::field('id'))
            ->whereNotNull(Application::field('contract_status'));

        return $q->get();
    }

    public function getContractByApplication($applicationId)
    {
        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('contract_id'),
            $this->modelField('payment_company_flag'),
            $this->modelField('regist_number'),
            $this->modelField('staff_name'),
            $this->modelField('contract_status'),
            $this->modelField('contract_date'),
            $this->modelField('application_date'),
            $this->modelField('contract_cancel_date'),
            $this->modelField('contract_cancel_amount'),
            $this->modelField('total_amount'),
            $this->modelField('fee_amount'),
            $this->modelField('payment_start_month'),
            $this->modelField('payment_last_month'),
            $this->modelField('payment_count'),
            $this->modelField('bonus_month_payment_amount'),
            $this->modelField('bonus_payment_amount'),
            $this->modelField('bonus_payment_month1'),
            $this->modelField('bonus_payment_month2'),
            $this->modelField('bonus_payment_start_month'),
            $this->modelField('first_month_payment_amount'),
            $this->modelField('second_month_payment_amount'),
            $this->modelField('payment_company_regist_date'),
            $this->modelField('ins_date'),
            $this->modelField('upd_date'),
            $this->modelField('re_invoice_doc_target_flag'),
            $this->modelField('contract_comment'),
            $this->modelField('fee_type'),
            $this->modelField('bonus_payment_count'),

            $this->modelField('shop_brand_id'),
            $this->modelField('customer_id'),
            $this->modelField('shop_id'),
            $this->modelField('brand_id'),
        ])
            ->with([
                'courses' => function ($q) {
                    $q->select([
                        Course::field('id'),
                        Course::field('name_application'),
                        Course::field('name_management'),
                        Course::field('item_type_id')
                    ]);
                },
                'shopBrand' => function ($q) {
                    $q->select([
                        ShopBrand::field('id'),
                        ShopBrand::field('name')
                    ]);
                },
                'courses.itemType' => function ($q) {
                    $q->select([
                        ItemType::field('id'),
                        ItemType::field('name')
                    ]);
                },
                'customer' => function ($q) {
                    $q->select([
                        Customer::field('id'),
                        Customer::field('last_name'),
                        Customer::field('first_name'),
                        Customer::field('last_name_kana'),
                        Customer::field('first_name_kana'),
                        Customer::field('information_input_flag'),
                        Customer::field('bank_account_name'),
                        Customer::field('bank_account_name_kana'),
                        Customer::field('bank_flag'),
                        Customer::field('bank_account_mark1'),
                        Customer::field('bank_account_mark2'),
                        Customer::field('bank_account_mark3'),
                        Customer::field('bank_account_number'),
                        Customer::field('bank_code'),
                        Customer::field('bank_name'),
                        Customer::field('branch_code'),
                        Customer::field('branch_name'),
                        Customer::field('bank_account_type'),
                        Customer::field('bank_account_number'),
                    ]);
                },
            ])
            ->where($this->modelField('id'), $applicationId);

        return $q->first();
    }

    public function getApplicationForMemo($applicationId) {
        return $this->select([
            $this->modelField('*'),
        ])->where('id', $applicationId)
            ->where('del_flag', 0)
            ->orderByDesc('ins_date')
            ->first();
    }

    public function getApplicationForDetail($applicationId) {
        return $this->select([
            $this->modelField('*'),
        ])->where('id', $applicationId)
            ->where('del_flag', 0)
            ->orderByDesc('ins_date')
            ->first();
    }

    public function getMaxContractId()
    {
        return $this->max($this->modelField('contract_id')) ?? 0;
    }

    public function getCountAppByPaymentStartMonth($shopBrandId, $paymentStartMonth)
    {
        return $this->where('shop_brand_id', $shopBrandId)
            ->where('payment_start_month', $paymentStartMonth)
            ->where('del_flag', 0)
            ->count();
    }

    public function getApplicationCount($status, $params)
    {
        $currentUser = getCurrentUser();
        $q = $this->where('status', $status);

        if (!empty($params['shopId'])) {
            $q->where('shop_id', $params['shopId']);
        }

        if ($currentUser->isBrand()) {
            if (!empty($params['shopBrandId'])) {
                $q->where('shop_brand_id', $params['shopBrandId']);
            } else {
                $q->whereIn('brand_id', $currentUser->listBrandIds());
            }
        }

        if ($currentUser->isStore()) {
            $q->whereIn('shop_id', $currentUser->listShopIds());
        }

        return $q->count();
    }

    public function getListBalancesForDashboard($params, $isCSV = false)
    {
        $from = convertJapaneseYearMonthToYYYYMM($params['from']) ?? null;
        $to = convertJapaneseYearMonthToYYYYMM($params['to']) ?? null;
        $sub = DB::table(LoanArrear::getTableName())
            ->select(LoanArrear::field('application_id'), DB::raw('SUM(amount) as amount'))
            ->where(LoanArrear::field('del_flag'), 0)
            ->groupBy(LoanArrear::field('application_id'));
        $authType = null;
        $currentUser = getCurrentUser();
        $brandId = $params['brand_id'] ?? null;
        $storeId = $params['store_id'] ?? null;

        $q = DB::table(Application::getTableName())->select(DB::raw(
            "applications.id, applications.payment_start_month AS target_month,

            COUNT(applications.id) AS total_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.id END) AS new_contracts,

            SUM(CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.total_amount ELSE 0 END) AS new_contract_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 2 THEN applications.id END) AS completed_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 3 THEN applications.id END) AS cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 3 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS cancelled_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 6 THEN applications.id END) AS forced_cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 6 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS forced_contract_cancel_amount,

            COUNT(DISTINCT loan_arrears.application_id) AS uncollected_contracts,
            SUM(IFNULL(loan_arrears.amount, 0)) AS uncollected_amount"
        ));
        if (empty($params['auth_type'])) {
            $authType = $currentUser->auth_type->value;
        } else {
            $authType = $params['auth_type'];
        }

        $q->leftJoinSub($sub, LoanArrear::getTableName(), function ($join) {
            $join->on(LoanArrear::field('application_id'), '=', $this->modelField('id'));
        });

        switch ($authType) {
            case AuthTypeEnum::ADMIN:
                $q->groupBy([
                    $this->modelField('payment_start_month'),
                ]);
                break;
            case AuthTypeEnum::BRAND:
                $authAdminIds = $currentUser->administratorBrands?->pluck('brand_id')->toArray() ?? [];

                $q->addSelect([
                    Brand::field('id'),
                    Brand::field('name'),
                ])->join(Brand::getTableName(), $this->modelField('brand_id'), Brand::field('id'))
                    ->when($brandId, function($q) use ($brandId) {
                        $q->where('applications.brand_id', $brandId);
                    }, function($q) {
                        $q->where('applications.brand_id', []);
                    })
                    // ->when($currentUser?->isBrand(), function ($q) use ($authAdminIds) {
                    //     $q->whereIn('applications.brand_id', $authAdminIds);
                    // })
                    ->groupBy([
                        $this->modelField('payment_start_month'),
                        Brand::field('id')
                    ]);
                    break;
            case AuthTypeEnum::STORE:
                $authAdminIds = $currentUser->administratorShops?->pluck('shop_id')->toArray() ?? [];

                $q->addSelect([
                    Shop::field('id'),
                    Shop::field('name'),
                ])->join(Shop::getTableName(), $this->modelField('shop_id'), Shop::field('id'))
                    ->when($storeId, function($q) use ($storeId) {
                        $q->where('applications.shop_id', $storeId);
                    }, function ($q) use ($authAdminIds) {
                        $q->whereIn('applications.shop_id', $authAdminIds);
                    })
                    ->groupBy([
                        $this->modelField('payment_start_month'),
                        Shop::field('id')
                    ]);
                    break;
        }

        $q->when($from, function ($q) use ($from) {
            $q->where('payment_start_month', '>=', $from);
        });
        $q->when($to, function ($q) use ($to) {
            $q->where('payment_start_month', '<=', $to);
        });

        $q->where($this->modelField('del_flag'),0)
        ->orderBy(Application::field('payment_start_month'));

        return $q->get();
    }

    public function getCustomerIdByRegisNumber($registNumber)
    {
        return $this->select([
            $this->modelField('id'),
            $this->modelField('customer_id'),
        ])->where($this->modelField('regist_number'), $registNumber)
        ->get()->first();
    }

    public function getInspectionStatusForNotify($customerId = 3)
    {
        $currentUser = getCurrentUser();

        $application = $this->where('customer_id', $customerId)
            ->orderByDesc('id')
            ->first();

        if(!$application) return null;

        $inspection = ApplicationInspectionStatus::where('application_id', $application->id)
                        ->when($currentUser->isAdmin(), function($q) { // admin
                            
                            $q->whereNull('to_shop_id');

                        }, function($q) { // brand or shop
                        
                            $q->whereNotNull('to_shop_id');

                        })
                        ->orderByDesc('id')
                        ->first();

        return $inspection ?? null;
    } 
}
