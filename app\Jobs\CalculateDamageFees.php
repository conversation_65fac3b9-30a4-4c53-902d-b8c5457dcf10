<?php

namespace App\Jobs;

use Carbon\Carbon;
use DateTime;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CalculateDamageFees implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $schedule;

    CONST REISSUE_FEE = 500;

    public function __construct(object $schedule)
    {
        $this->schedule = $schedule;
    }

    public function handle(): void
    {
        $today = Carbon::now();
        $amount = $this->schedule->amount;
        $planDate = $this->schedule->payment_plan_date;

        $delayFee = $this->calculatePenalty($amount, $planDate, $today);

        $arrear = DB::table('loan_arrears')->where('loan_schedule_id', $this->schedule->id)->first();

        $reissueFee = $arrear->reissue_fee + self::REISSUE_FEE;

        DB::table('loan_arrears')
            ->where('loan_schedule_id', $this->schedule->id)
            ->update([
                'damage_delay_fee'  => $delayFee,
                'reissue_fee'       => $reissueFee,
            ]);
    }

    private function calculatePenalty($amount,  $paymentPlanDate,  $today)
    {
        $paymentDate = Carbon::parse($paymentPlanDate);
        $endDate = $today->copy();

        $holidays = $this->getHolidayDates($paymentDate, $endDate);

        while ($this->isHolidayOrWeekend($endDate, $holidays)) {
                $endDate->subDay();
        }

        $diffDays = (int)$paymentDate->diff($endDate)->format('%a');

        $yearDays = $this->isLeapYear((int)$paymentDate->format('Y')) ? 366 : 365;

        $penalty = floor($amount * 0.2 / $yearDays * $diffDays);

        return $penalty;
    }

    private function isHolidayOrWeekend(DateTime $date, $holidays) {
        $weekday = (int)$date->format('w');
        $dateStr = $date->format('Y-m-d');
        return ($weekday === 0 || $weekday === 6 || in_array($dateStr, $holidays));
    }

    private function isLeapYear($year) {
        return ($year % 4 === 0) && (($year % 100 !== 0) || ($year % 400 === 0));
    }

    private function getHolidayDates( $startDate,  $endDate)
    {
        return DB::table('holidays')
            ->whereBetween('holiday_date', [$startDate, $endDate])
            ->pluck('holiday_date')
            ->toArray();
    }
}
