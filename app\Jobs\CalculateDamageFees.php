<?php

namespace App\Jobs;

use App\Repositories\LoanArrearRepository;
use App\Repositories\HolidayRepository;
use Carbon\Carbon;
use DateTime;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CalculateDamageFees implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $schedule;
    protected $loanArrearRepository;
    protected $holidayRepository;

    CONST REISSUE_FEE = 500;

    public function __construct(object $schedule)
    {
        $this->schedule = $schedule;
    }

    public function handle(): void
    {
        $this->loanArrearRepository = app(LoanArrearRepository::class);
        $this->holidayRepository = app(HolidayRepository::class);

        $today = Carbon::now()->day(26)->toDateString();
        $amount = $this->schedule->amount;
        $planDate = $this->schedule->payment_plan_date;

        $delayFee = $this->calculatePenalty($amount, $planDate, $today);

        $arrear = $this->loanArrearRepository
            ->where('loan_schedule_id', $this->schedule->id)
            ->first();

        if (empty($arrear)) {
           return;
        }

        $reissueFee = $arrear->reissue_fee + self::REISSUE_FEE;

        dd($delayFee, $reissueFee);

        $this->loanArrearRepository
            ->where('loan_schedule_id', $this->schedule->id)
            ->update([
                'delay_damage_amount'  => $delayFee,
                'reissue_fee'       => $reissueFee,
            ]);
    }

    private function calculatePenalty($amount, $paymentPlanDate, $today)
    {
        $startDate = Carbon::parse($paymentPlanDate);
        $endDate = Carbon::parse($today);

        if ($endDate->lt($startDate)) {
            return 0;
        }

        $holidays = $this->getHolidayDates($startDate, $endDate);

        $delayDays = 0;
        $date = $startDate->copy();

        while ($date->lte($endDate)) {
            if (!$this->isHolidayOrWeekend($date, $holidays)) {
                $delayDays++;
            }
            $date->addDay();
        }

        $year = $startDate->year;
        $yearDays = $this->isLeapYear($year) ? 366 : 365;

        $penalty = floor($amount * 0.2 / $yearDays * $delayDays);

        return $penalty;
    }

    private function isHolidayOrWeekend(DateTime $date, $holidays) {
        $weekday = (int)$date->format('w');
        $dateStr = $date->format('Y-m-d');
        return ($weekday === 0 || $weekday === 6 || in_array($dateStr, $holidays));
    }

    private function isLeapYear($year) {
        return ($year % 4 === 0) && (($year % 100 !== 0) || ($year % 400 === 0));
    }

    private function getHolidayDates($startDate, $endDate)
    {
        return $this->holidayRepository->getHolidayDatesBetween($startDate, $endDate);
    }
}
