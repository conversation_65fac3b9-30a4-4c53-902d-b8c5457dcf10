<?php

namespace App\Livewire\Admin\Dashboard;

use App\Enums\AuthTypeEnum;
use App\Livewire\Base\BaseDataListComponent;
use App\Services\CsvService;
use Livewire\Attributes\Reactive;
use Livewire\Component;
use App\Services\DashboardService;
use Livewire\Attributes\On;

class DataList extends BaseDataListComponent
{
    #[Reactive]
    public $authType;

    #[Reactive]
    public $from;

    #[Reactive]
    public $to;

    #[Reactive]
    public $brandId;

    #[Reactive]
    public $storeId;

    public function render()
    {
        $params = [
            'auth_type' => $this->authType,
            'from' => $this->from,
            'to' => $this->to,
            'brand_id' => $this->brandId,
            'store_id' => $this->storeId
        ];

        $dataList = app(DashboardService::class)->getListBalancesForDashboard($params, false);
        return $this->viewLivewireAdmin('dashboard.data-list', [
            'dataList' => $dataList,
        ]);
    }

    #[On('export_csv')]
    public function downloadCSV()
    {
        $dataSearch = [
            'authType' => $this->authType,
            'from' => $this->from,
            'to' => $this->to,
            'brandId' => $this->brandId,
            'storeId' => $this->storeId,
        ];

        $dataList = app(DashboardService::class)->getListBalancesForDashboard($dataSearch, true);
        $headers = getConfig('csv.dashboard.header');
        $filename = getConfig('csv.dashboard.filename') . '.csv';

        return app(DashboardService::class)->exportCsvDashboard($filename, $headers, $dataList);
    }
}
