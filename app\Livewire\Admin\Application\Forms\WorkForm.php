<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Livewire\Base\BaseAdminForm;
use App\Models\Customer;
use App\Rules\TelValidationRule;
use App\Rules\ZipCodeValidationRule;

class WorkForm extends BaseAdminForm
{
    public $annual_income = '';
    public $company_name = '';
    public $company_name_kana = '';
    public $company_zip1 = '';
    public $company_zip2 = '';
    public $company_pref_id = '';
    public $company_city = '';
    public $company_address = '';
    public $company_building = '';
    public $company_tel1 = '';
    public $company_tel2 = '';
    public $company_tel3 = '';

    public function rules()
    {
        $rules = [
            'annual_income' => 'required|string|max:11',
            'company_name' => 'nullable|string|max:128',
            'company_name_kana' => 'nullable|katakana|max:128',
            'company_zip1' => ['nullable', 'max:4', new ZipCodeValidationRule()],
            'company_zip2' => ['nullable', 'max:4', new ZipCodeValidationRule()],
            'company_pref_id' => 'nullable',
            'company_city' => 'nullable|string|max:1000',
            'company_address' => 'nullable|string|max:1000',
            'company_tel1' => ['nullable', 'max:32', new TelValidationRule()],
            'company_tel2' => ['nullable', 'max:32', new TelValidationRule()],
            'company_tel3' => ['nullable', 'max:32', new TelValidationRule()],
        ];

        if ($this->annual_income >= 1) {
        $rules['company_name'] = 'required|string|max:128';
        $rules['company_name_kana'] = 'required|katakana|max:128';
        $rules['company_zip1'] = ['required', 'max:4', new ZipCodeValidationRule()];
        $rules['company_zip2'] = ['required', 'max:4', new ZipCodeValidationRule()];
        $rules['company_pref_id'] = 'required';
        $rules['company_city'] = 'required|string|max:1000';
        $rules['company_address'] = 'required|string|max:1000';
        $rules['company_tel1'] = ['required', 'max:32', new TelValidationRule()];
        $rules['company_tel2'] = ['required', 'max:32', new TelValidationRule()];
        $rules['company_tel3'] = ['required', 'max:32', new TelValidationRule()];
    }

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.work.attributes');
    }

    public function fillFromCustomer(Customer $customer): void
    {
        $this->annual_income = $customer->annual_income ?? '';
        $this->company_name = $customer->company_name ?? '';
        $this->company_name_kana = $customer->company_name_kana ?? '';
        $this->company_tel1 = $customer->company_tel1 ?? '';
        $this->company_tel2 = $customer->company_tel2 ?? '';
        $this->company_tel3 = $customer->company_tel3 ?? '';
        $this->company_zip1 = $customer->company_zip1 ?? '';
        $this->company_zip2 = $customer->company_zip2 ?? '';
        $this->company_pref_id = $customer->company_pref_id ?? '';
        $this->company_city = $customer->company_city ?? '';
        $this->company_address = $customer->company_address ?? '';
        $this->company_building = $customer->company_building ?? '';
    }
}
