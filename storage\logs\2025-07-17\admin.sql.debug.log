
#SQL in Request: URI=/livewire/update | ACTION=livewire:admin.auth.login@login | SCRIPT=/index.php | SERVER=127.0.0.1 | IP=127.0.0.1 | AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 | REFERER=http://127.0.0.1:8000/management/login | 
[2025-07-17 17:12:27] (Time: 73.11) SQL: select * from `administrators` where `email` = '<EMAIL>' and `administrators`.`del_flag` = '0' limit 1  
[2025-07-17 17:12:27] (Time: 00.62) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1  

#SQL in Request: URI=/management/login/code | ACTION= | SCRIPT=/index.php | SERVER=127.0.0.1 | IP=127.0.0.1 | AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 | REFERER=http://127.0.0.1:8000/management/login | 
[2025-07-17 17:12:28] (Time: 19.31) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1  

#SQL in Request: URI=/management | ACTION= | SCRIPT=/index.php | SERVER=127.0.0.1 | IP=127.0.0.1 | AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 | REFERER=http://127.0.0.1:8000/management/login | 
[2025-07-17 17:12:28] (Time: 02.05) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1  
[2025-07-17 17:12:28] (Time: 02.76) SQL: select * from `shop_brands` where `shop_brands`.`del_flag` = '0'  
[2025-07-17 17:12:29] (Time: 02.58) SQL: select * from `shops` where `shops`.`del_flag` = '0'  
[2025-07-17 17:12:29] (Time: 00.51) SQL: select * from `shops` where `shops`.`del_flag` = '0'  
[2025-07-17 17:12:29] (Time: 01.49) SQL: select * from `brands` where `brands`.`del_flag` = '0'  

#SQL in Request: URI=/livewire/update | ACTION=livewire:admin.dashboard.review-table@__lazyLoad | SCRIPT=/index.php | SERVER=127.0.0.1 | IP=127.0.0.1 | AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 | REFERER=http://127.0.0.1:8000/management | 
[2025-07-17 17:12:30] (Time: 11.85) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1  
[2025-07-17 17:12:30] (Time: 02.83) SQL: select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:30] (Time: 00.44) SQL: select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:30] (Time: 00.53) SQL: select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:30] (Time: 00.64) SQL: select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:30] (Time: 00.47) SQL: select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.43) SQL: select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.52) SQL: select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.68) SQL: select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.43) SQL: select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.43) SQL: select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.63) SQL: select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.43) SQL: select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 02.62) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.33) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.37) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0'  
[2025-07-17 17:12:31] (Time: 00.47) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0'  
