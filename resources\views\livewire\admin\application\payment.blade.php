@php
$feeTypeCompanyPaid = \App\Enums\FeeTypeEnum::COMPANY_PAID;
$optionMonths = config('constant.OPTION_MONTHS');
@endphp
<div class="container-min" x-data="inputPayment">
    <h1 class="application-page-title">2.{{ trans2('screens.application.payment.title') }}</h1>
    <h2 class="heading-2">{{ trans2('screens.application.payment.sec_total_amount_title') }}</h2>
    <table class="table table-details">
        <tbody>
            <tr>
                <th class="align-middle" style="width:44%">③{{ trans2('screens.application.payment.remaining_amount') }}（①{{ trans2('screens.application.payment.total_cash_price') }}－②{{ trans2('screens.application.payment.down_payment') }}）</th>
                <td class="align-middle text-end">
                    <span class="me-1" id="remaining-amount">{{ $remaining_amount ?? '' }}</span>
                </td>
            </tr>
            <tr>
                <th class="align-middle" style="width:44%">④{{ trans2('screens.application.payment.installment_fee') }}</th>
                <td class="align-middle text-end"><span class="me-1" id="fee-amount">{{ $fee_amount ?? '' }}</span></td>
            </tr>
            <tr>
                <th class="align-middle" style="width:44%">⑤{{ trans2('screens.application.payment.year_rate') }}（％）</th>
                <td class="align-middle text-end">{{ isset($application?->brand?->year_rate) ? $application?->brand?->year_rate . '%' : '' }}</td>
            </tr>
            <tr>
                <th class="align-middle" style="width:44%">⑥{{ trans2('screens.application.payment.total_installment_payment_amount') }}（③{{ trans2('screens.application.payment.remaining_amount') }}＋④{{ trans2('screens.application.payment.installment_fee') }}）</th>
                <td class="align-middle text-end"><span class="me-1" id="fee-total-amount">{{ $fee_total_amount ?? '' }}</span></td>
            </tr>
            <tr>
                <th class="align-middle" style="width:44%">⑦{{ trans2('screens.application.payment.total_payment_amount') }}（②{{ trans2('screens.application.payment.down_payment') }}＋⑥{{ trans2('screens.application.payment.total_installment_payment_amount') }}）</th>
                <td class="align-middle text-end"><span class="me-1" id="total-amount">{{ $total_amount ?? '' }}</span></td>
            </tr>
        </tbody>
    </table>
    <h2 class="heading-2">{{ trans2('screens.application.payment.sec_payment_method_title') }}</h2>
    <table class="table table-edit" id="table-form-data" x-data="{ bonus_flag: @entangle('paymentForm.bonus_flag') }">
        <tbody>
            <tr>
                <th class="required" style="width:44%">{{ trans2('screens.application.payment.down_payment') }}（②）<span class="required-icon">{{ trans2('required') }}</span></th>
                <td>
                    <div class="form-parts-unit w-px-200">
                        <div class="form-unit">&yen;</div>
                        <input type="text" wire:model="paymentForm.deposit" name="deposit"
                            class="form-control form-required" x-data x-ref="incomeInput"
                            x-on:keydown="handleDepositKeyDown" x-on:input="handleDepositInput" id="inputIncome" />
                    </div>
                    @error('paymentForm.deposit')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="required" style="width:44%">{{ trans2('screens.application.payment.payment_frequency') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                <td>
                    <div class="form-parts-unit w-px-100">
                        <input type="number" pattern="[0-9]*" wire:model="paymentForm.payment_count" name="payment_count" value="{{ $application?->fee_type?->value == 1 ? config('constant.MAX_PAYMENT_COUNT_APPLICATION') : '' }}"
                            class="form-control form-required pe-5" {{ $application?->fee_type?->value == 1 ? 'readonly' : '' }}>
                        <div class="form-unit">{{ trans2('times') }}</div>
                    </div>

                    @error('paymentForm.payment_count')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="required" style="width:44%">{{ trans2('screens.application.payment.payment_start_month') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                <td>
                    <input type="text" wire:model="paymentForm.payment_start_month" name="payment_start_month" value="" class="form-control form-required monthpicker w-px-200" readonly>
                    @error('paymentForm.payment_start_month')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr>
                <th style="width:44%">{{ trans2('screens.application.payment.final_payment_month') }}</th>
                <td x-data="finalMonthCalc()" x-init="init()">
                    <span x-text="year"></span>年<span x-text="month"></span>月
                </td>
            </tr>
            <tr>
                <th class="required" style="width:44%">{{ trans2('screens.application.payment.bonus_payment') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                <td>
                    <div class="row px-3">
                        <div class="form-radio col-6">
                            <label>
                                <input type="radio" wire:model="paymentForm.bonus_flag" x-model="bonus_flag" name="bonus" value="{{ \App\Enums\BonusFlagEnum::ENABLED }}" class="form-radio-input form-hidden-control" checked>
                                <span class="form-radio-text">{{ trans2('Enum.ENABLED') }}</span>
                            </label>
                        </div>
                        <div class="form-radio col-6">
                            <label>
                                <input type="radio" wire:model="paymentForm.bonus_flag" x-model="bonus_flag" name="bonus" value="{{ \App\Enums\BonusFlagEnum::DISABLED }}" class="form-radio-input form-hidden-control">
                                <span class="form-radio-text">{{ trans2('Enum.DISABLED') }}</span>
                            </label>
                        </div>
                    </div>
                    @error('paymentForm.bonus_flag')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr class="hidden-target-bonus" x-show="bonus_flag == {{\App\Enums\BonusFlagEnum::ENABLED}}">
                <th class="required" style="width:44%">{{ trans2('screens.application.payment.bonus_payment_amount') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                <td>
                    <div class="form-parts-unit w-px-200">
                        <div class="form-unit">&yen;</div>
                        <input type="text" wire:model="paymentForm.bonus_payment_amount" name="bonus_payment_amount"
                            class="form-control form-required" x-data x-ref="incomeInput"
                            x-on:keydown="handleDepositKeyDown" x-on:input="handleDepositInput" id="inputIncome" />
                    </div>
                    @error('paymentForm.bonus_payment_amount')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
            <tr class="hidden-target-bonus" x-show="bonus_flag == {{\App\Enums\BonusFlagEnum::ENABLED}}">
                <th class="required" style="width:44%">{{ trans2('screens.application.payment.bonus_payment_month') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                <td>
                    <div class="row">
                        <div class="col-6">
                            <div wire:ignore>
                                <select wire:model="paymentForm.bonus_payment_month1" name="bonus_payment_month1" class="form-select2 form-required" data-placeholder="1回目" style="width:100%;">
                                    <option value=""></option>
                                    @foreach($optionMonths as $val => $label)
                                    <option value="{{ $val }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @error('paymentForm.bonus_payment_month1')
                            <div>
                                <span class="message-error">{{ $message }}</span>
                            </div>
                            @enderror
                        </div>
                        <div class="col-6">
                            <div>
                                <select wire:model="paymentForm.bonus_payment_month2" name="bonus_payment_month2" class="form-select2 form-required" data-placeholder="2回目" style="width:100%;">
                                    <option value="0">指定しない</option>
                                    @foreach($optionMonths as $val => $label)
                                    <option value="{{ $val }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @error('paymentForm.bonus_payment_month2')
                            <div>
                                <span class="message-error">{{ $message }}</span>
                            </div>
                            @enderror
                        </div>
                    </div>
                </td>
            </tr>
            <tr class="hidden-target-bonus" x-show="bonus_flag == {{\App\Enums\BonusFlagEnum::ENABLED}}">
                <th class="required" style="width:44%">{{ trans2('screens.application.payment.bonus_payment_count') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                <td>
                    <div class="form-parts-unit w-px-100">
                        <input type="number" pattern="[0-9]*" wire:model="paymentForm.bonus_payment_count" name="bonus_payment_count" value="" class="form-control form-required pe-5">
                        <div class="form-unit">{{ trans2('times') }}</div>
                    </div>
                    @error('paymentForm.bonus_payment_count')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
        </tbody>
    </table>
    <h2 class="heading-2">{{ trans2('screens.application.payment.sec_installment_title') }}</h2>
    <table class="table table-details">
        <tbody>
            <tr>
                <th class="align-middle" style="width:44%">{{ trans2('screens.application.payment.first_installment_payment') }}</th>
                <td class="align-middle text-end"><span class="me-1" id="first-month-payment-amount">{{ $first_month_payment_amount ? formatCurrency($first_month_payment_amount) : '' }}</span></td>
            </tr>
            <tr>
                <th class="align-middle" style="width:44%">{{ trans2('screens.application.payment.second_installment_payment') }}</th>
                <td class="align-middle text-end"><span class="me-1" id="second-month-payment-amount">{{ $second_month_payment_amount ? formatCurrency($second_month_payment_amount) : '' }}</span></td>
            </tr>
        </tbody>
    </table>
    <table class="table table-edit mt-5">
        <tbody>
            <tr>
                <th class="required" style="width:44%">{{ trans2('screens.application.payment.send_docs') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                <td>
                    <div class="row px-3">
                        <div class="form-radio col-6">
                            <label>
                                <input type="radio" wire:model="paymentForm.doc_send_flag" name="destination" value="1" class="form-radio-input" checked>
                                <span class="form-radio-text">{{ trans2('screens.application.payment.send_docs_one_home') }}</span>
                            </label>
                        </div>
                        <div class="form-radio col-6">
                            <label>
                                <input type="radio" wire:model="paymentForm.doc_send_flag" name="destination" value="2" class="form-radio-input">
                                <span class="form-radio-text">{{ trans2('screens.application.payment.send_docs_place') }}</span>
                            </label>
                        </div>
                    </div>
                    @error('paymentForm.doc_send_flag')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                    @enderror
                </td>
            </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button class="btn btn-dark btn-large" wire:click.prevent="validateSave">{{ trans2('button.next') }}</button>
    </div>
</div>
@script
<script>
    Alpine.data('inputPayment', () => ({
        handleDepositKeyDown(event) {
            if (event.key === ',') {
                event.preventDefault();
            }
        },
        handleDepositInput(event) {
            let v = event.target.value;
            v = v.replace(/,/g, '');
            if (v.includes('.')) {
                const parts = v.split('.');
                const decimalPart = parts[1].slice(0, 2);
                v = parts[0] + '.' + decimalPart;
            }

            event.target.value = v;
        }
    }))
</script>
@endscript
@push('scripts')
<script>
    window.addEventListener('init-select2', function() {
        setTimeout(() => {
            initSelect2Binding();
        }, 50);
    });

    function initSelect2Binding() {

        document.querySelectorAll('select.form-select2').forEach(function(el) {

            if (!$(el).hasClass('select2-hidden-accessible')) {
                $(el).select2().on('change', function(e) {
                    const value = $(this).val();
                    const model = el.getAttribute('wire:model');

                    if (model) {
                        const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                        Livewire.find(componentId).set(model, value);

                        // Trigger specific method for bonus_payment_month1
                        if (model === 'paymentForm.bonus_payment_month1') {
                            Livewire.find(componentId).call('setBonusPaymentSecondMonth');
                        }
                    }
                });
            }
        });
    }

    document.addEventListener('livewire:initialized', () => {
        initSelect2Binding();
    });
</script>
<script>
    $(document).ready(function() {
        const applicationTotalAmount = @js($application?-> sub_total_amount);
        const feeType = @js($application?-> fee_type?-> value);
        const yearRate = @js($application?->brand?->year_rate);
        let bonusPaymentCountInit = null;

        const calcFeeAmount = (yearRate, feeType, paymentCount, applicationTotal, depositAmount) => {
            // Case 1: REGULAR fee_type
            if (feeType === 2) {
                let installmentFee = (yearRate * paymentCount) / 100;
                let finalFee = (applicationTotal - depositAmount) * installmentFee;
                return Math.floor(finalFee || 0); // Use || 0 to handle null/undefined
            }

            // Case 2: Self-paid fee_type (default)
            let prevFee = (applicationTotal - depositAmount) / (1 + 0.252); // (1 + 0.252) is fixed
            return applicationTotal - Math.floor(prevFee);
        }

        const calcPaymentBonusCount = (paymentCount, bonusPaymentMonth1, bonusPaymentMonth2) => {
            if (paymentCount && (parseInt(bonusPaymentMonth1) || parseInt(bonusPaymentMonth2))) {
                const countBonusPaymentMonth = (bonusPaymentMonth1 && bonusPaymentMonth2) ? 2 : 1;
                const bonusPaymentCount = (paymentCount / 12) * countBonusPaymentMonth;

                if (bonusPaymentCountInit !== bonusPaymentCount) {
                    $('input[name=bonus_payment_count]').val(Math.floor(bonusPaymentCount));
                    bonusPaymentCountInit = bonusPaymentCount;

                    // update livewire data
                    const bonusPayCountEl = document.querySelector('input[name=bonus_payment_count]');
                    const model = bonusPayCountEl.getAttribute('wire:model');

                    if (model) {
                        const componentId = bonusPayCountEl.closest('[wire\\:id]').getAttribute('wire:id');
                        Livewire.find(componentId).set(model, Math.floor(bonusPaymentCount));
                    }
                }
            }
        }

        const calcAmountAndRenderData = (e) => {
            const depositAmountVal = $('input[name=deposit]').val() ?? 0;
            const paymentCountVal = $('input[name=payment_count]').val() ?? 0;
            const remainingAmount = Math.max(0, applicationTotalAmount - depositAmountVal);
            const bonusFlagVal = $('input[name=bonus]:checked').val();
            const feeAmount = calcFeeAmount(yearRate, feeType, paymentCountVal, applicationTotalAmount, depositAmountVal); // follow excel...
            const feeTotalAmount = Math.floor(remainingAmount) + feeAmount;
            const totalAmount = Math.floor(parseFloat(depositAmountVal) + parseFloat(feeTotalAmount));
            const paymentStartMonthVal = $('input[name=payment_start_month]').val();
            const bonusPaymentMonth1Val = $('select[name=bonus_payment_month1]').val();
            const bonusPaymentMonth2Val = $('select[name=bonus_payment_month2]').val();
            let usuallyMonthPaymentAmount = 0;
            let bonusMonthPaymentAmount = 0;
            const MIN_PAYMENT_COUNT = 3;
            const MAX_PAYMENT_COUNT = 36;

            if (paymentCountVal < MIN_PAYMENT_COUNT || paymentCountVal > MAX_PAYMENT_COUNT) {
                return;
            }

            // call calc payment count
            calcPaymentBonusCount(paymentCountVal, bonusPaymentMonth1Val, bonusPaymentMonth2Val);

            const bonusPaymentAmountVal = $('input[name=bonus_payment_amount]').val();
            const bonusPaymentCountVal = $('input[name=bonus_payment_count]').val();

            // split payment_start_month
            const [year, month] = paymentStartMonthVal?.split('年');
            const paymentStartMonth = month?.replace('月', '');

            if (bonusFlagVal == 1) {
                // usually_month_payment_amount  =（⑥分割支払金合計 - ボーナス支払額）/ 支払回数)
                usuallyMonthPaymentAmount = Math.floor(paymentCountVal > 0 ? (feeTotalAmount - bonusPaymentAmountVal) / paymentCountVal : 0);
                // bonus_month_payment_amount = ボーナス支払額 / ボーナス支払回数
                bonusMonthPaymentAmount = Math.floor(bonusPaymentCountVal > 0 ? bonusPaymentAmountVal / bonusPaymentCountVal : 0);
            } else {
                // usually_month_payment_amount = ⑥分割支払金合計 / 支払回数
                usuallyMonthPaymentAmount = Math.floor(paymentCountVal > 0 ? feeTotalAmount / paymentCountVal : 0);
            }

            // update to livewire
            const formComponent = document.querySelector('input[name=bonus_payment_count]');
            const componentId = formComponent.closest('[wire\\:id]').getAttribute('wire:id');

            if ((paymentStartMonth == bonusPaymentMonth1Val) || (paymentStartMonth == bonusPaymentMonth2Val)) {
                let firstPaymentAmount = usuallyMonthPaymentAmount + bonusMonthPaymentAmount;
                let secondPaymentAmount = usuallyMonthPaymentAmount;

                $('#first-month-payment-amount').text(formatPrice(firstPaymentAmount));
                $('#second-month-payment-amount').text(formatPrice(secondPaymentAmount));

                if (componentId) {
                    try {
                        Livewire.find(componentId).set('first_month_payment_amount', Math.floor(firstPaymentAmount));
                        Livewire.find(componentId).set('second_month_payment_amount', Math.floor(secondPaymentAmount));
                    } catch (e) {
                        console.error(e);
                    }
                }
            } else {
                let firstPaymentAmount = usuallyMonthPaymentAmount;
                let secondPaymentAmount = usuallyMonthPaymentAmount;

                $('#first-month-payment-amount').text(formatPrice(usuallyMonthPaymentAmount));
                $('#second-month-payment-amount').text(formatPrice(usuallyMonthPaymentAmount));

                if (componentId) {
                    try {
                        Livewire.find(componentId).set('first_month_payment_amount', Math.floor(firstPaymentAmount));
                        Livewire.find(componentId).set('second_month_payment_amount', Math.floor(secondPaymentAmount));
                    } catch (e) {
                        console.error(e);
                    }
                }
            }

            // fill to html
            $('#remaining-amount').text(formatPrice(remainingAmount));
            $('#fee-total-amount').text(formatPrice(feeTotalAmount));
            $('#total-amount').text(formatPrice(totalAmount));
            $('#fee-amount').text(formatPrice(feeAmount));

            // update value to livewire
            try {
                Livewire.find(componentId).set('remaining_amount', formatPrice(remainingAmount));
                Livewire.find(componentId).set('fee_amount', formatPrice(feeAmount));
                Livewire.find(componentId).set('fee_total_amount', formatPrice(feeTotalAmount));
                Livewire.find(componentId).set('total_amount', formatPrice(totalAmount));
            } catch (e) {
                console.error(e);
            }
        }

        // listen input change
        $('#table-form-data').on('change', 'input, select', calcAmountAndRenderData);

        // init dom -> call calc
        calcAmountAndRenderData();
    });
</script>
<script>
    // Alpine.js for updateFinalPaymentMonth logic
    document.addEventListener('alpine:init', () => {
        Alpine.data('finalMonthCalc', () => ({
            year: '--',
            month: '--',
            paymentCount: 0,
            paymentStartMonth: '',
            calc() {
                if (!this.paymentStartMonth || !this.paymentCount || !this.paymentStartMonth.includes('年') || !this.paymentStartMonth.includes('月')) {
                    this.year = '--';
                    this.month = '--';
                    return;
                }
                const [year, month] = this.paymentStartMonth.split('年');
                const monthNum = parseInt(month.replace('月', ''));
                const startYear = parseInt(year);
                const paymentCount = parseInt(this.paymentCount);
                let totalMonths = monthNum + paymentCount - 1;
                let resultYear = startYear + Math.floor((totalMonths - 1) / 12);
                let resultMonth = ((totalMonths - 1) % 12) + 1;
                this.year = resultYear;
                this.month = resultMonth.toString().padStart(2, '0');
            },
            init() {
                this.paymentCount = document.querySelector('input[name=payment_count]')?.value || 0;
                this.paymentStartMonth = document.querySelector('input[name=payment_start_month]')?.value || '';
                this.calc();

                document.querySelector('input[name=payment_count]')?.addEventListener('input', (e) => {
                    this.paymentCount = e.target.value;
                    this.calc();
                });
                document.querySelector('input[name=payment_start_month]')?.addEventListener('input', (e) => {
                    this.paymentStartMonth = e.target.value;
                    this.calc();
                });

                document.addEventListener('livewire:load', () => {
                    this.paymentCount = document.querySelector('input[name=payment_count]')?.value || 0;
                    this.paymentStartMonth = document.querySelector('input[name=payment_start_month]')?.value || '';
                    this.calc();
                });
                Livewire.hook('morph.updated', () => {
                    this.paymentCount = document.querySelector('input[name=payment_count]')?.value || 0;
                    this.paymentStartMonth = document.querySelector('input[name=payment_start_month]')?.value || '';
                    this.calc();
                });
            }
        }));
    });
</script>
@endpush
