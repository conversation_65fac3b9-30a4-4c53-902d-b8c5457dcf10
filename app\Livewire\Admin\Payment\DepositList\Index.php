<?php

namespace App\Livewire\Admin\Payment\DepositList;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\LoanScheduleService;
use App\Services\LoanPaymentService;
use App\Services\LoanWithdrawalService;
use App\Services\LoanTransactionLogService;
use App\Services\ToastService;
use Gate;
use Livewire\Component;

class Index extends BaseAdminPageComponent
{
    public $customer_id;
    public $isCanDelete;
    public $loanSchedule;
    public $depositList;
    public $payment;
    // public $loanTransactionLogs;
    // public $loanTransactionLogs;
    public $loanPayments;
    public $loanWithdrawals;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::PAYMENT;
        parent::__construct();
        $subTitle = trans2('screens.payment.deposit_list_tab.page_title');
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle = $this->pageTitle . $subTitle . $suffixTitle;
    }
    protected $listeners = ['refresh-deposit' => '$refresh'];

    public function mount($payment_id)
    {
        $this->payment_id = $payment_id;
        $this->loanSchedule = app(LoanScheduleService::class)->getloanScheduleById($payment_id);
        $brandIds = collect($this->loanSchedule->application?->brand_id);
        $shopIds = collect($this->loanSchedule->application?->shop_id);
        Gate::authorize('allowed-update', [SidebarMenuEnum::PAYMENT, $brandIds, $shopIds]);

        if (empty($this->loanSchedule)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.payment.index'));
        }

        $this->isCanDelete = true;
    }

    public function getData($id, $flag){
        $data = [
            'id' => $id,
            'flag' => $flag,
        ];
        $this->dispatch('log-show', $data);
    }

    public function render()
    {
        $this->depositList = app(LoanScheduleService::class)->getListByApplication(data_get($this->loanSchedule, 'application_id'));
        $this->payment = app(LoanScheduleService::class)->getListForPaymentTableByApplication(data_get($this->loanSchedule, 'application_id'));
        $this->loanPayments = app(LoanPaymentService::class)->getLoanPaymentsWithLogsByApplicationId(data_get($this->loanSchedule, 'application_id'));
        $this->loanWithdrawals = app(LoanWithdrawalService::class)->getLoanWithdrawalsWithLogsByApplicationId(data_get($this->loanSchedule, 'application_id'));

        $mergesCollection = $this->loanPayments->concat($this->loanWithdrawals)->values()->sortBy('transaction_date');
        return $this->viewLivewireAdmin('payment.deposit-list.index', [
            'depositList' => $this->depositList,
            'payment' => $this->payment,
            'mergesCollection' => $mergesCollection,
        ]);
    }
}
