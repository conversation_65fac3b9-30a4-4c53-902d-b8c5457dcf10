<?php

namespace App\Livewire\Admin\Balance;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use Livewire\Component;

class Index extends BaseAdminPageComponent
{
    public function __construct()
    {
        $this->page = SidebarMenuEnum::BALANCE;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle = $this->pageTitle  . $suffixTitle;
    }
    public function render()
    {
        return $this->viewLivewireAdmin('balance.index');
    }
}
