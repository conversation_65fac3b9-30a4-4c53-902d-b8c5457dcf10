<?php

namespace App\Repositories;

use App\Enums\AuthTypeEnum;
use App\Enums\PaymentStatusEnum;
use App\Models\Administrator;
use App\Models\Application;
use App\Models\ApplicationCourse;
use App\Models\Brand;
use App\Models\Course;
use App\Models\Customer;
use App\Models\ItemType;
use App\Models\LoanArrear;
use App\Models\LoanPayment;
use App\Models\LoanPaymentAllocation;
use App\Models\LoanRefund;
use App\Models\LoanSchedule;
use App\Models\LoanTransactionLog;
use App\Models\Shop;
use App\Models\ShopBrand;
use Carbon\Carbon;
use DB;

class LoanScheduleRepository extends CustomRepository
{
    protected $model = LoanSchedule::class;

    public function getListForSearch($dataSearch, $perPage)
    {
        $currentUser = getCurrentUser();

        $monthIndex = data_get($dataSearch, 'month_index');
        $thisTime = Carbon::now()->addMonths($monthIndex);

        $brandId = data_get($dataSearch, 'brand_id');
        $shopId = data_get($dataSearch, 'shop_id');
        $paymentPlanDateFrom = data_get($dataSearch, 'payment_plan_date_from');
        $paymentPlanDateTo = data_get($dataSearch, 'payment_plan_date_to');
        $itemTypeId = data_get($dataSearch, 'item_type_id');
        $courseId = data_get($dataSearch, 'course_id');
        $status = data_get($dataSearch, 'payment_status');
        $focusBrandId = data_get($dataSearch, 'focus_brand_id');

        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('application_id'),
            Application::field('payment_company_flag'),
            $this->modelField('payment_status'),
            Customer::field('first_name'),
            Customer::field('last_name'),
            $this->modelField('payment_plan_date'),
            $this->modelField('amount_paid') . ' as total_paid_amount',
        ]);

        $q->join(Application::getTableName(), $this->modelField('application_id'), Application::field('id'))
            ->join(Customer::getTableName(), $this->modelField('customer_id'), Customer::field('id'))
            ->leftJoin(ApplicationCourse::getTableName(), Application::field('id'), ApplicationCourse::field('application_id'))
            ->leftJoin(Course::getTableName(), ApplicationCourse::field('course_id'), Course::field('id'))

            // advanced search
            ->when($focusBrandId, function ($q) use ($focusBrandId) {
                $q->where(Application::field('brand_id'), $focusBrandId);
            })
            ->when($brandId, function ($q) use ($brandId) {
                $q->where(Application::field('brand_id'), $brandId);
            })
            ->when($shopId, function ($q) use ($shopId) {
                $q->where(Application::field('shop_id'), $shopId);
            })
            ->when($courseId, function ($q) use ($courseId) {
                $q->where(ApplicationCourse::field('course_id'), $courseId);
            })
            ->when($itemTypeId, function ($q) use ($itemTypeId) {
                $q->where(Course::field('item_type_id'), $itemTypeId);
            })

            //payment_plan_date
            ->when(!blank($paymentPlanDateFrom) && !blank($paymentPlanDateTo), function ($q) use ($paymentPlanDateFrom, $paymentPlanDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$paymentPlanDateFrom, $paymentPlanDateTo]);
            })
            ->when(!blank($paymentPlanDateFrom), function ($q) use ($paymentPlanDateFrom) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') >= ?", [$paymentPlanDateFrom]);
            })
            ->when(!blank($paymentPlanDateTo), function ($q) use ($paymentPlanDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') <= ?", [$paymentPlanDateTo]);
            })

            //status
            ->when(!empty($status) && !in_array('', $status, true), function ($q) use ($status) {
                $q->whereIn($this->modelField('payment_status'), $status);
            })
            ->when(blank($paymentPlanDateFrom) && blank($paymentPlanDateTo), function ($q) use ($thisTime) {
                $q->whereYear($this->modelField('payment_plan_date'), $thisTime->year)
                    ->whereMonth($this->modelField('payment_plan_date'), $thisTime->month);
            })
            ->with([
                'application.courses' => function ($q) {
                    $q->select([
                        Course::field('id'),
                        Course::field('name_application'),
                        Course::field('item_type_id')
                    ]);
                },
                'application.shopBrand' => function ($q) {
                    $q->select([
                        ShopBrand::field('id'),
                        ShopBrand::field('name')
                    ]);
                },
                'application.courses.itemType' => function ($q) {
                    $q->select([
                        ItemType::field('id'),
                        ItemType::field('name')
                    ]);
                },
            ])
            ->whereNotNull(Application::field('contract_status'))
            ->orderBy($this->modelField('payment_plan_date'), 'asc')
            ->groupBy(
                $this->modelField('id'),
                Application::field('payment_company_flag'),
                $this->modelField('payment_status'),
                Customer::field('first_name'),
                Customer::field('last_name'),
                $this->modelField('payment_plan_date'),
            );

        if ($currentUser->isStore()) {
            $q->whereIn(Application::field('shop_id'), $currentUser->listShopIds());
        }

        if ($currentUser->isBrand()) {
            $q->whereIn(Application::field('brand_id'), $currentUser->listBrandIds());
        }

        return $q->paginate($perPage);
    }

    public function getListByApplication($application_id)
    {
        $q = $this->select([
            $this->modelField('id') . ' as loan_schedule_id',
            $this->modelField('payment_plan_date'),
            $this->modelField('payment_status'),
            $this->modelField('total_amount'),
            $this->modelField('amount_paid'),
            $this->modelField('payment_type'),

            $this->modelField('application_id'),
        ])->orderBy($this->modelField('payment_plan_date'), 'asc')
            ->groupBy(
                'loan_schedule_id',
                $this->modelField('payment_plan_date'),
                $this->modelField('payment_status'),
                $this->modelField('total_amount'),
                $this->modelField('amount_paid'),
                $this->modelField('payment_type'),
            )
            ->where($this->modelField('application_id'), $application_id);

        return $q->get();
    }

    public function getListByApplicationAndPaymentStatus($application_id, $payment_status)
    {
        $q = $this->select(['*'])->orderBy($this->modelField('payment_plan_date'), 'asc')
            ->where($this->modelField('application_id'), $application_id)
            ->whereIn($this->modelField('payment_status'), $payment_status);

        return $q->get();
    }

    public function getListForPaymentTable($dataSearch)
    {
        $monthIndex = data_get($dataSearch, 'month_index');
        $thisTime = Carbon::now()->addMonths($monthIndex);

        $brandId = data_get($dataSearch, 'brand_id');
        $shopId = data_get($dataSearch, 'shop_id');
        $paymentPlanDateFrom = data_get($dataSearch, 'payment_plan_date_from');
        $paymentPlanDateTo = data_get($dataSearch, 'payment_plan_date_to');
        $itemTypeId = data_get($dataSearch, 'item_type_id');
        $courseId = data_get($dataSearch, 'course_id');
        $status = data_get($dataSearch, 'payment_status');
        $currentUser = getCurrentUser();

        $loanScheduleBase = DB::table($this->getTableName())
            ->select(
                $this->modelField('id'),
                $this->modelField('total_amount'),
                $this->modelField('payment_status'),
                Application::field('brand_id'),
                Application::field('shop_id'),
                Application::field('contract_status'),
            )
            ->join(Application::getTableName(), Application::field('id'), LoanSchedule::field('application_id'))
            ->leftJoin(ApplicationCourse::getTableName(), Application::field('id'), ApplicationCourse::field('application_id'))
            ->where($this->modelField('del_flag'), 0)
            ->where(Application::field('del_flag'), 0)
            ->when($brandId, function ($q) use ($brandId) {
                $q->where(Application::field('brand_id'), $brandId);
            })
            ->when($shopId, function ($q) use ($shopId) {
                $q->where(Application::field('shop_id'), $shopId);
            })
            ->when($courseId, function ($q) use ($courseId) {
                $q->where(ApplicationCourse::field('del_flag'), 0)
                    ->where(ApplicationCourse::field('course_id'), $courseId);
            })
            ->when($itemTypeId, function ($q) use ($itemTypeId) {
                $q->leftJoin(Course::getTableName(), ApplicationCourse::field('course_id'), Course::field('id'))
                    ->where(ApplicationCourse::field('del_flag'), 0)
                    ->where(Course::field('del_flag'), 0)
                    ->where(Course::field('item_type_id'), $itemTypeId);
            })
            ->when(!blank($paymentPlanDateFrom) && !blank($paymentPlanDateTo), function ($q) use ($paymentPlanDateFrom, $paymentPlanDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$paymentPlanDateFrom, $paymentPlanDateTo]);
            })
            ->when(!blank($paymentPlanDateFrom), function ($q) use ($paymentPlanDateFrom) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') >= ?", [$paymentPlanDateFrom]);
            })
            ->when(!blank($paymentPlanDateTo), function ($q) use ($paymentPlanDateTo) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') <= ?", [$paymentPlanDateTo]);
            })
            ->when(!empty($status) && !in_array('', $status, true), function ($q) use ($status) {
                $q->whereIn($this->modelField('payment_status'), $status);
            })
            ->when(blank($paymentPlanDateFrom) && blank($paymentPlanDateTo), function ($q) use ($thisTime) {
                $q->whereYear($this->modelField('payment_plan_date'), $thisTime->year)
                    ->whereMonth($this->modelField('payment_plan_date'), $thisTime->month);
            })
            ->whereNotNull(Application::field('contract_status'))
            ->groupBy(
                $this->modelField('id'),
                $this->modelField('total_amount'),
                $this->modelField('payment_status')
            );

        $finalQuery = DB::table(DB::raw("({$loanScheduleBase->toSql()}) as ls"))
            ->mergeBindings($loanScheduleBase)
            ->join(Brand::getTableName(), Brand::field('id'), 'ls.brand_id')
            ->join(ShopBrand::getTableName(), ShopBrand::field('shop_id'), 'ls.shop_id')
            ->select(
                Brand::field('id'),
                Brand::field('name'),
                DB::raw("
                SUM(CASE WHEN ls.payment_status IN (1, 2) THEN 1 ELSE 0 END) as waiting_count,
                SUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN 1 ELSE 0 END) as deposited_count,
                SUM(CASE WHEN ls.payment_status = 2 THEN 1 ELSE 0 END) as unpaid_count,
                SUM(CASE WHEN ls.payment_status IN (8,9) THEN 1 ELSE 0 END) as other_count,
                SUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN 1 ELSE 0 END) as grand_count,

                SUM(CASE WHEN ls.payment_status IN (1, 2) THEN ls.total_amount ELSE 0 END) as waiting_total_amount,
                SUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN ls.total_amount ELSE 0 END) as deposited_total_amount,
                SUM(CASE WHEN ls.payment_status = 2 THEN ls.total_amount ELSE 0 END) as unpaid_total_amount,
                SUM(CASE WHEN ls.payment_status IN (8,9) THEN ls.total_amount ELSE 0 END) as other_total_amount,
                SUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN ls.total_amount ELSE 0 END) as grand_total_amount
            ")
            )->where(ShopBrand::field('del_flag'), 0)
            ->where(Brand::field('del_flag'), 0)
            ->groupBy(Brand::field('id'), Brand::field('name'));

        if ($currentUser->isStore()) {
            $finalQuery->whereIn(ShopBrand::field('shop_id'), $currentUser->listShopIds());
        }
        if ($currentUser->isBrand()) {
            $finalQuery->whereIn(Brand::field('id'), $currentUser->listBrandIds());
        }

        return $finalQuery->get();
    }

    public function getListForPaymentTableByApplication($application_id)
    {
        $loanScheduleBase = DB::table($this->getTableName())
            ->select(
                $this->modelField('id'),
                $this->modelField('total_amount'),
                $this->modelField('payment_status'),
                Application::field('brand_id'),
                Application::field('shop_id'),
            )
            ->join(Application::getTableName(), Application::field('id'), LoanSchedule::field('application_id'))
            ->where($this->modelField('del_flag'), 0)
            ->where(Application::field('del_flag'), 0)
            ->where($this->modelField("application_id"), $application_id)
            ->groupBy(
                $this->modelField('id'),
                $this->modelField('total_amount'),
                $this->modelField('payment_status')
            );

        $finalQuery = DB::table(DB::raw("({$loanScheduleBase->toSql()}) as ls"))
            ->mergeBindings($loanScheduleBase)
            ->join(ShopBrand::getTableName(), ShopBrand::field('shop_id'), 'ls.shop_id')
            ->select(
                DB::raw("
                SUM(CASE WHEN ls.payment_status IN (1, 2) THEN 1 ELSE 0 END) as waiting_count,
                SUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN 1 ELSE 0 END) as deposited_count,
                SUM(CASE WHEN ls.payment_status = 2 THEN 1 ELSE 0 END) as unpaid_count,
                SUM(CASE WHEN ls.payment_status IN (8,9) THEN 1 ELSE 0 END) as other_count,
                SUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN 1 ELSE 0 END) as grand_count,

                SUM(CASE WHEN ls.payment_status IN (1, 2) THEN ls.total_amount ELSE 0 END) as waiting_total_amount,
                SUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN ls.total_amount ELSE 0 END) as deposited_total_amount,
                SUM(CASE WHEN ls.payment_status = 2 THEN ls.total_amount ELSE 0 END) as unpaid_total_amount,
                SUM(CASE WHEN ls.payment_status IN (8,9) THEN ls.total_amount ELSE 0 END) as other_total_amount,
                SUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN ls.total_amount ELSE 0 END) as grand_total_amount
            ")
            )->where(ShopBrand::field('del_flag'),0);

        return $finalQuery->get()->first();
    }

    public function getPaymentForCsv($dataSearch, $paymentStatus)
    {
        $monthIndex = data_get($dataSearch, 'month_index');
        $brandId = data_get($dataSearch, 'brand_id');
        $shopId = data_get($dataSearch, 'shop_id');
        $itemTypeId = data_get($dataSearch, 'item_type_id');
        $courseId = data_get($dataSearch, 'course_id');
        $payment_plan_date_from = data_get($dataSearch, 'payment_plan_date_from');
        $payment_plan_date_to = data_get($dataSearch, 'payment_plan_date_to');
        $thisTime = Carbon::now()->addMonths($monthIndex);

        $currentUser = getCurrentUser();
        if ($paymentStatus == PaymentStatusEnum::WAITING_FOR_PAYMENT) {
            $q = $this->select([
                $this->modelField('id'),
                $this->modelField('payment_plan_date'),
                Customer::field('bank_flag'),
                Customer::field('bank_code'),
                Customer::field('bank_account_mark1'),
                Customer::field('branch_code'),
                Customer::field('bank_account_type'),
                Customer::field('bank_account_number'),
                Customer::field('bank_account_name_kana'),
                Customer::field('bank_flag'),
                $this->modelField('total_amount'),
                $this->modelField('type'),
                Application::field('regist_number'),
                $this->modelField('application_id'),
                $this->modelField('customer_id'),
                $this->modelField('payment_status'),
            ]);
        } elseif ($paymentStatus == PaymentStatusEnum::NOT_PAID_OVERDUE) {
            $q = $this->select([
                $this->modelField('id'),
                $this->modelField('application_id'),
                $this->modelField('customer_id'),
                $this->modelField('payment_plan_date'),
                $this->modelField('payment_type'),
                $this->modelField('amount_paid'),
                $this->modelField('total_amount'),
                Application::field('regist_number'),
                $this->modelField('payment_status'),
            ]);
        } else {
            $q = $this->select('*');
        }

        $q->Join(Application::getTableName(), Application::field('id'), $this->modelField('application_id'))
            ->Join(Customer::getTableName(), Customer::field('id'), $this->modelField('customer_id'))
            ->leftJoin(ApplicationCourse::getTableName(), Application::field('id'), $this->modelField('application_id'))
            ->leftJoin(Course::getTableName(), ApplicationCourse::field('course_id'), Course::field('id'))

            // advanced search
            ->when($brandId, function ($q) use ($brandId) {
                $q->where(Application::field('brand_id'), $brandId);
            })
            ->when($shopId, function ($q) use ($shopId) {
                $q->where(Application::field('shop_id'), $shopId);
            })
            ->when($courseId, function ($q) use ($courseId) {
                $q->where(ApplicationCourse::field('course_id'), $courseId);
            })
            ->when($itemTypeId, function ($q) use ($itemTypeId) {
                $q->where(Course::field('item_type_id'), $itemTypeId);
            })

            ->when(empty($payment_plan_date_from) && empty($payment_plan_date_to), function ($q) use ($thisTime) {
                $q->whereYear($this->modelField('payment_plan_date'), $thisTime->year)
                    ->whereMonth($this->modelField('payment_plan_date'), $thisTime->month);
            })
            ->when(!blank($payment_plan_date_from) && !blank($payment_plan_date_to), function ($q) use ($payment_plan_date_from, $payment_plan_date_to) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$payment_plan_date_from, $payment_plan_date_to]);
            })
            ->when(!blank($payment_plan_date_from), function ($q) use ($payment_plan_date_from) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') >= ?", [$payment_plan_date_from]);
            })
            ->when(!blank($payment_plan_date_to), function ($q) use ($payment_plan_date_to) {
                $q->whereRaw("DATE_FORMAT(" . $this->modelField('payment_plan_date') . ", '%Y/%m/%d') <= ?", [$payment_plan_date_to]);
            })

            ->where($this->modelField('payment_status'), $paymentStatus)

            ->orderBy($this->modelField('payment_plan_date'), 'asc')
            ->groupBy(
                $this->modelField('id'),
            );

        if ($currentUser->isStore()) {
            $q->whereIn(Application::field('shop_id'), $currentUser->listShopIds());
        }

        if ($currentUser->isBrand()) {
            $q->whereIn(Application::field('brand_id'), $currentUser->listBrandIds());
        }

        return $q->get();
    }

    public function getLoanScheduleById($id)
    {
        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('customer_id'),
            $this->modelField('application_id'),
            $this->modelField('payment_plan_date'),
            $this->modelField('payment_status'),
            $this->modelField('total_amount'),
            $this->modelField('amount_paid'),
            $this->modelField('payment_type'),
            $this->modelField('payment_company_flag'),
            $this->modelField('result_status'),
            $this->modelField('ins_date'),
            $this->modelField('upd_date'),
        ])
            ->selectRaw('(' . $this->modelField('amount_paid') . ' - ' . $this->modelField('total_amount') . ') as balance_difference')
            ->with([
                'customer' => function ($q) {
                    $q->select([
                        Customer::field('id'),
                        Customer::field('last_name'),
                        Customer::field('first_name'),
                        Customer::field('last_name_kana'),
                        Customer::field('first_name_kana'),
                    ]);
                },
                'application' => function ($q) {
                    $q->select([
                        Application::field('id'),
                        Application::field('regist_number'),
                        Application::field('contract_status'),
                        Application::field('fee_amount'),
                        Application::field('payment_count'),
                        Application::field('contract_id'),
                        Application::field('total_amount'),
                        Application::field('payment_start_month'),
                        Application::field('payment_last_month'),
                        Application::field('brand_id'),
                        Application::field('shop_id'),
                    ]);
                },
                'loanRefunds' => function ($q) {
                    $q->select([
                        LoanRefund::field('id'),
                        LoanRefund::field('loan_schedule_id'),
                        LoanRefund::field('amount'),
                        LoanRefund::field('payment_date'),
                    ]);
                },
                'loanArrears' => function ($q) {
                    $q->select([
                        LoanArrear::field('id'),
                        LoanArrear::field('loan_schedule_id'),
                        LoanArrear::field('delay_damage_amount'),
                        LoanArrear::field('reissue_fee'),
                    ]);
                },
            ])
            ->where($this->modelField('id'), $id);

        return $q->first();
    }

    public function getLoanScheduleTransactionsById($loanScheduleId)
    {
        $q = $this->select([
            LoanSchedule::field('id') . ' as loan_schedule_id',
            LoanPayment::field('id') . ' as loan_payment_id',
            LoanPayment::field('payment_status'),
            DB::raw('COALESCE(' . LoanPayment::field('payment_date') . ', ' . LoanRefund::field('payment_date') . ') as transaction_date'),
            LoanPayment::field('upd_date'),
            LoanPayment::field('upd_id'),
            LoanPayment::field('auto_manual_flag'),
            LoanTransactionLog::field('comment'),
            Administrator::field('name'),
        ])
        ->leftJoin(LoanPaymentAllocation::getTableName(), LoanPaymentAllocation::field('loan_schedule_id'), '=', LoanSchedule::field('id'))
        ->leftJoin(LoanPayment::getTableName(), LoanPaymentAllocation::field('loan_payment_id'), '=', LoanPayment::field('id'))
        ->leftJoin(LoanRefund::getTableName(), LoanRefund::field('loan_schedule_id'), '=', LoanSchedule::field('id'))
        ->leftJoin(LoanTransactionLog::getTableName(), LoanTransactionLog::field('loan_schedule_id'), '=', LoanSchedule::field('id'))
        ->leftJoin(Administrator::getTableName(), Administrator::field('id'), '=', LoanPayment::field('upd_id'))
        ->where(LoanSchedule::field('id'), $loanScheduleId)
        ->orderBy('transaction_date', 'desc');

        return $q->get();
    }

    public function getLoanSchedulesWithArrearsAndLatestPayment($applicationId)
    {
        $loanArrearsSub = DB::table(LoanArrear::getTableName())
            ->select([
                LoanArrear::field('loan_schedule_id'),
                DB::raw('SUM(delay_damage_amount) AS total_delay_damage')
            ])
            ->where(LoanArrear::field('del_flag'), '0')
            ->groupBy(LoanArrear::field('loan_schedule_id'));

        $latestPaymentSub = DB::table(LoanPaymentAllocation::getTableName())
            ->leftJoin(LoanPayment::getTableName(), function ($join) {
                $join->on(
                    LoanPaymentAllocation::field('loan_payment_id'),
                    '=',
                    LoanPayment::field('id')
                );
            })
            ->where(LoanPaymentAllocation::field('del_flag'), '0')
            ->whereNotNull(LoanPayment::field('payment_date'))
            ->select([
                LoanPaymentAllocation::field('loan_schedule_id'),
                LoanPayment::field('payment_date'),
                LoanPayment::field('result_status'),
                LoanPayment::field('payment_type'),
                LoanPayment::field('auto_manual_flag'),
            ])
            ->whereRaw(
                LoanPayment::field('payment_date') . " = (
                    SELECT MAX(lp2.payment_date)
                    FROM loan_payment_allocations as lpa2
                    JOIN loan_payments as lp2 ON lpa2.loan_payment_id = lp2.id
                    WHERE lpa2.loan_schedule_id = " . LoanPaymentAllocation::field('loan_schedule_id') . "
                    AND lp2.del_flag = '0'
                    AND lpa2.del_flag = '0'
                )"
            );

        $latestLogSub = DB::table(LoanTransactionLog::getTableName())
            ->select([
                'loan_schedule_id',
                DB::raw('MAX(comment) as comment'),
            ])
            ->where('del_flag', '0')
            ->groupBy('loan_schedule_id');

        return DB::table(LoanSchedule::getTableName())
            ->select([
                LoanSchedule::field('id') . ' as loan_schedule_id',
                LoanSchedule::field('payment_plan_date'),
                LoanSchedule::field('payment_status'),
                LoanSchedule::field('total_amount') . ' as scheduled_amount',
                DB::raw('COALESCE(la.total_delay_damage, 0) as delay_damage_amount'),
                'lp.payment_date',
                'lp.result_status',
                'lp.auto_manual_flag',
                'lp.payment_type',
                'log.comment as transaction_comment',
            ])
            ->leftJoinSub($loanArrearsSub, 'la', function ($join) {
                $join->on('la.loan_schedule_id', '=', LoanSchedule::field('id'));
            })
            ->leftJoinSub($latestPaymentSub, 'lp', function ($join) {
                $join->on('lp.loan_schedule_id', '=', LoanSchedule::field('id'));
            })
            ->leftJoinSub($latestLogSub, 'log', function ($join) {
                $join->on('log.loan_schedule_id', '=', LoanSchedule::field('id'));
            })
            ->where(LoanSchedule::field('application_id'), $applicationId)
            ->where(LoanSchedule::field('del_flag'), '0')
            ->orderBy(LoanSchedule::field('payment_plan_date'), 'asc')
            ->get();
    }


    public function updateLoanSchedule($id, array $data)
    {
        return $this->where('id', $id)->update($data);
    }

    public function getListBalances($params)
    {
        $from = convertJapaneseYearMonthToYYYYMM($params['from']) ?? null;
        $to = convertJapaneseYearMonthToYYYYMM($params['to']) ?? null;

        $authType = null;
        $currentUser = getCurrentUser();
        $brandId = $params['brand_id'] ?? null;
        $storeId = $params['store_id'] ?? null;

        $paymentMonth = "DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m')";

        $q = $this->select(DB::raw(
            "DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') AS payment_month,

            SUM(loan_schedules.total_amount - loan_schedules.amount_paid) AS loan_balance,

            SUM(loan_schedules.amount_paid) AS paid_amount,

            SUM(IFNULL(loan_arrears.delay_damage_amount, 0)) AS delay_damage_total,
            SUM(IFNULL(loan_arrears.reissue_fee, 0)) AS reissue_fee_total,

            SUM(DISTINCT IFNULL(applications.sub_total_amount, 0)) AS principal_total,
            SUM(DISTINCT IFNULL(applications.fee_amount, 0)) AS fee_total,

            SUM(DISTINCT IF(
                DATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),
                IFNULL(applications.total_amount, 0), 0
            )) AS current_month_contract_total,

            SUM(DISTINCT IF(
                DATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),
                IFNULL(applications.sub_total_amount, 0), 0
            )) AS current_month_principal_total,

            SUM(DISTINCT IF(
                DATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),
                IFNULL(applications.contract_cancel_amount, 0), 0
            )) AS cancel_amount_total,

            SUM(DISTINCT IF(
                DATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),
                IFNULL(applications.forced_contract_cancel_amount, 0), 0
            )) AS forced_cancel_amount_total,

            SUM(
                (loan_schedules.total_amount - loan_schedules.amount_paid)
                * (IFNULL(applications.sub_total_amount, 0) / NULLIF(applications.total_amount, 0))
            ) AS remaining_principal_estimated"
        ))
            ->leftJoin(LoanArrear::getTableName(), LoanArrear::field('loan_schedule_id'), $this->modelField('id'))
            ->leftJoin(Application::getTableName(), $this->modelField('application_id'), Application::field('id'));


        if (empty($params['auth_type'])) {
            $authType = $currentUser->auth_type->value;
        } else {
            $authType = $params['auth_type'];
        }

        switch ($authType) {
            case AuthTypeEnum::ADMIN:
                $q->groupBy(DB::raw($paymentMonth));
                break;
            case AuthTypeEnum::BRAND:
                $authAdminIds = $currentUser->administratorBrands?->pluck('brand_id')->toArray() ?? [];

                $q->addSelect([
                    Application::field('brand_id'),
                ])
                ->when($brandId, function($q) use ($brandId) {
                    $q->where('applications.brand_id', $brandId);
                }, function ($q) {
                    $q->whereIn('applications.brand_id', []);
                })
                ->groupBy([
                    DB::raw($paymentMonth),
                    Application::field('brand_id'),
                ]);
                break;
            case AuthTypeEnum::STORE:
                $authAdminIds = $currentUser->administratorShops?->pluck('shop_id')->toArray() ?? [];

                $q->addSelect([
                    Application::field('shop_id'),
                ])
                ->when($storeId, function($q) use ($storeId) {
                    $q->where('applications.shop_id', $storeId);
                }, function ($q) use ($authAdminIds) {
                    $q->whereIn('applications.shop_id', $authAdminIds);
                })
                ->groupBy([
                    DB::raw($paymentMonth),
                    Application::field('shop_id'),
                ]);
                break;
        }

        $q->when($from, function ($q) use ($from, $paymentMonth) {
            $q->where(DB::raw($paymentMonth), '>=', $from);
        });
        $q->when($to, function ($q) use ($to, $paymentMonth) {
            $q->where(DB::raw($paymentMonth), '<=', $to);
        });

        $q->orderBy('payment_month');

        return $q->get();
    }

    public function getLoanScheduleByApplicationIdAndPaymentPlanDate($applicationId, $paymentPlanDateYYYYmm)
    {
        $year = substr($paymentPlanDateYYYYmm, 0, 4);
        $month = substr($paymentPlanDateYYYYmm, 4, 2);

        return $this->select([
            $this->modelField('id'),
        ])
        ->where($this->modelField('application_id'), $applicationId)
        ->whereYear($this->modelField('payment_plan_date'), $year)
        ->whereMonth($this->modelField('payment_plan_date'), $month)
        ->get();
    }

    public function getLoanScheduleByApplicationIdPaymentPlanDatePaymentStatus($applicationId, $paymentPlanDateYYYYmm, $paymentStatus = [])
    {
        return $this->select([
            '*'
        ])
        ->where($this->modelField('application_id'), $applicationId)
        ->where($this->modelField('payment_plan_date'), Carbon::createFromFormat('Ym', $paymentPlanDateYYYYmm)->day(27)->format('Y-m-d'))
        ->whereIn($this->modelField('payment_status'), $paymentStatus)
        ->orderBy($this->modelField('payment_plan_date'), 'asc')
        ->get();
    }

}
