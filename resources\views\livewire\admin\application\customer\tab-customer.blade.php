@php
use App\Enums\CustomerTabEnum;
@endphp
<ol class="application-steps-list">
    <li style="display: flex; justify-content: center; align-items: center;"  class="{{ CustomerTabEnum::checkCurrentOrCompleteTab(CustomerTabEnum::BASIC) }}">
        <span class="txt">{{ trans2('screens.customer_setup.tab_title.basic') }}</span>
    </li>
    <li style="display: flex; justify-content: center; align-items: center;"  class="{{ CustomerTabEnum::checkCurrentOrCompleteTab(CustomerTabEnum::EMERGENCY) }}">
        <span class="txt">{{ trans2('screens.customer_setup.tab_title.emergency') }}</span>
    </li>
    <li style="display: flex; justify-content: center; align-items: center;"  class="{{ CustomerTabEnum::checkCurrentOrCompleteTab(CustomerTabEnum::WORK) }}">
        <span class="txt">{!! trans2('screens.customer_setup.tab_title.work') !!}</span>
    </li>
    <li style="display: flex; justify-content: center; align-items: center;"  class="{{ CustomerTabEnum::checkCurrentOrCompleteTab(CustomerTabEnum::GUARANTOR) }}">
        <span class="txt">{!! trans2('screens.customer_setup.tab_title.guarantor') !!}</span>
    </li>
    <li style="display: flex; justify-content: center; align-items: center;"  class="{{ CustomerTabEnum::checkCurrentOrCompleteTab(CustomerTabEnum::ACCOUNT) }}">
        <span class="txt">{{ trans2('screens.customer_setup.tab_title.account') }}</span>
    </li>
    <li style="display: flex; justify-content: center; align-items: center;"  class="{{ CustomerTabEnum::checkCurrentOrCompleteTab(CustomerTabEnum::CONTACT) }}">
        <span class="txt">{{ trans2('screens.customer_setup.tab_title.contact') }}</span>
    </li>
</ol>
