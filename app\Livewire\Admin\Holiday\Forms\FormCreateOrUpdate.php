<?php

namespace App\Livewire\Admin\Holiday\Forms;

use App\Enums\CreditFlagEnum;
use App\Enums\SplitFeeFlagEnum;
use App\Livewire\Base\BaseAdminForm;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Validate;
use App\Services\ItemTypeService;
use App\Enums\TypeEnum;

class FormCreateOrUpdate extends BaseAdminForm
{
    public $holidays;
    public $newHolidays;

    public function rules()
    {
        $rules = [
            'holidays.*.holiday_date' => 'required|date|date_format:Y/m/d|check_date_format',
            'holidays.*.name' => [
                'required',
                'max:255',
            ],
            'newHolidays.*.holiday_date' => 'nullable|date|date_format:Y/m/d|check_date_format',
            'newHolidays.*.name' => [
                'nullable',
                'max:255',
            ],
        ];
        foreach ($this->newHolidays as $key => $value) {
            if (array_key_exists('holiday_date', $value) || array_key_exists('name', $value)) {
                $rules['newHolidays.' . $key . '.name'] = [
                    'required',
                    'max:255',
                ];
                $rules['newHolidays.' . $key . '.holiday_date'] = 'required|date|date_format:Y/m/d|check_date_format';
            }
        }

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.holidays.attributes');
    }

}
