<?php

use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('loan_refunds', function (BlueprintCustom $table) {
            $table->id();
            $table->integer('customer_id')->comment('顧客ID')->index();
            $table->integer('application_id')->comment('申込ID')->index();
            $table->integer('loan_schedule_id')->comment('返済予定ID')->nullable()->index();
            $table->integer('loan_payment_id')->comment('ローン入金ID')->nullable()->index();
            $table->date('payment_date')->comment('返金日')->nullable();
            $table->decimal('amount', 10, 2)->comment('金額');
            $table->text('comment')->comment('返金理由')->nullable();
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SchemaCustom::dropIfExists('loan_refunds');
    }
};
