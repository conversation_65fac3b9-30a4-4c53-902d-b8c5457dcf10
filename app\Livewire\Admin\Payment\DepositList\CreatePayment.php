<?php

namespace App\Livewire\Admin\Payment\DepositList;

use App\Livewire\Admin\Payment\Forms\CreateForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\PaymentService;
use App\Services\ToastService;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class CreatePayment extends BaseAdminPageComponent
{
    public CreateForm $createForm;
    public $applicationId;
    public $customerId;

    public function __construct()
    {
        $this->dispatch('init-select2');
    }

    public function validateCreate()
    {
        $this->createForm->validate();
        $this->js("$('#newPaymentsModal').modal('hide');");
        $this->setDataCreateConfirmModal(getConstant('SCREENS.PAYMENT'), 'create-payment', 'newPaymentsModal');
    }

    #[On('create-payment')]
    public function createPayment()
    {
        $params = $this->createForm->all();
        DB::beginTransaction();
        $result = app(PaymentService::class)->createPayment($this->applicationId, $this->customerId, $params);

        if ($result['result']) {
            DB::commit();
            $this->toastSuccess(__('messages.create_success'));
            $this->dispatch('refresh-deposit');
        } else {
            DB::rollBack();
            if($result['message'] !== ""){
                $this->js("message = " . json_encode($result['message']).";
                    $('#messageBox').text(message);
                    $('#messageModal').modal('show');"
                );
            } else {
                $this->toastError(__('messages.create_failed'));
            }
        }
        $this->createForm->reset();
    }

    #[On('reset-data')]
    public function resetData()
    {
        $this->createForm->reset();
    }

    public function render()
    {
        return $this->viewLivewireAdmin('payment.deposit-list.create-payment');
    }
}
