<?php

namespace App\Livewire\Admin\Customer\Application;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\CustomerRepository;
use App\Services\ApplicationService;
use App\Services\ToastService;

class Index extends BaseAdminPageComponent
{
    public $customer_id;

    protected $customer = null;

    public $isCanDelete;

    public $applications = null;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CUSTOMER;
        parent::__construct();
    }

    public function mount($customer_id)
    {
        $this->customer_id = $customer_id;
        $this->customer = app()->make(CustomerRepository::class)->getBasicInfoCustomer($customer_id);

        if (empty($this->customer)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }

        $this->isCanDelete = !app(ApplicationService::class)->getCountAppByCustomerId($this->customer_id);
        $this->applications = app(ApplicationService::class)->getAppsByCustomerId($this->customer_id);
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.application.index', [
            'customer' => $this->customer,
            'applications' => $this->applications,
        ]);
    }
}
