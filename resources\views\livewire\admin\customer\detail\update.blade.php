@extends('livewire.admin.customer.detail.index')
@section('detail-tab')
    @php
        $func = \App\Enums\FunctionEnum::CUSTOMER;
    @endphp
    <div class="card mb-3">
        <div class="card-body">
            <div class="container-min">
                <form role="form">
                    <div class="mb-4 d-flex justify-content-end">
                        <button wire:click.prevent="validateUpdate" class="btn btn-dark btn-medium">{{ trans2('button.update') }}</button>
                    </div>
                    <div id="selected-date"
                        data-selected-dates='@json($selectedHoliday)'>
                    </div>
                    <table class="table table-edit">
                        <tbody>
                        <tr>
                            <th class="text-nowrap">{{ transm('customer.attributes.ID') }}</th>
                            <td>{{ data_get($customer, 'id') }}</td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">{{ transm('customer.attributes.name') }}<span
                                    class="required-icon">{{ trans2('required') }}</span></th>
                            <td>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="text" wire:model="updateForm.last_name" name=""
                                               class="form-control form-required" placeholder="姓" maxlength="128">
                                        <div>
                                            <span
                                                class="message-error">{{ $errors->first('updateForm.last_name') }}</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <input type="text" wire:model="updateForm.first_name" name=""
                                               class="form-control form-required" placeholder="名" maxlength="128">
                                        <div>
                                            <span
                                                class="message-error">{{ $errors->first('updateForm.first_name') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">{{ transm('customer.attributes.name_hiragana') }}<span
                                    class="required-icon">{{ trans2('required') }}</span></th>
                            <td>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="text" wire:model="updateForm.last_name_kana" name=""
                                               class="form-control form-required" placeholder="セイ" maxlength="128">
                                        <div>
                                            <span
                                                class="message-error">{{ $errors->first('updateForm.last_name_kana') }}</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <input type="text" wire:model="updateForm.first_name_kana" name=""
                                               class="form-control form-required" placeholder="メイ" maxlength="128">
                                        <div>
                                            <span
                                                class="message-error">{{ $errors->first('updateForm.first_name_kana') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">{{ transm('customer.attributes.sex') }}<span
                                    class="required-icon">{{ trans2('required') }}</span></th>
                            <td>
                                <div class="d-flex">
                                    <div class="form-radio me-5">
                                        <label>
                                            <input type="radio" wire:model="updateForm.sex" name="sex" value="1"
                                                   class="form-radio-input" {{ $customer?->check?->value == 1 ? 'checked' : '' }}>
                                            <span class="form-radio-text">{{ trans2('SexEnum.FEMALE') }}</span>
                                        </label>
                                    </div>
                                    <div class="form-radio">
                                        <label>
                                            <input type="radio" wire:model="updateForm.sex" name="sex" value="2"
                                                   class="form-radio-input" {{ $customer?->check?->value == 2 ? 'checked' : '' }}>
                                            <span class="form-radio-text">{{ trans2('SexEnum.MALE') }}</span>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.sex') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">
                                {{ transm('customer.attributes.birthday') }}<span
                                    class="required-icon">{{ trans2('required') }}</span>
                            </th>
                            <td>
                                <input type="text" wire:model="updateForm.birthday" name="" value=""
                                       class="{{ empty($updateForm->birthday) ? 'form-required' : '' }} form-control datepicker"
                                       x-init="setTimeout(() => initializeDatepickers(), 100)"
                                       data-date-format="yy/mm/dd">
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.birthday') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap">{{ transm('customer.attributes.email') }}</th>
                            <td>
                                <input type="email" wire:model="updateForm.email" name=""
                                       class="form-control" maxlength="128">
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.email') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">{{ transm('customer.attributes.tel') }}<span
                                    class="required-icon">{{ trans2('required') }}</span></th>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <input type="text" wire:model="updateForm.tel1" name=""
                                               class="form-control form-required w-px-150" maxlength="32">
                                    </div>
                                    <div class="mx-2">-</div>
                                    <div>
                                        <input type="text" wire:model="updateForm.tel2" name=""
                                               class="form-control form-required w-px-150" maxlength="32">
                                    </div>
                                    <div class="mx-2">-</div>
                                    <div>
                                        <input type="text" wire:model="updateForm.tel3"
                                               name=""
                                               class="form-control form-required w-px-150" maxlength="32">
                                    </div>
                                </div>
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.tel1') }}</span>
                                </div>
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.tel2') }}</span>
                                </div>
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.tel3') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">
                                {{ transm('customer.attributes.postcode') }}<span
                                    class="required-icon">{{ trans2('required') }}</span></th>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <input type="text" wire:model="updateForm.zip1" name="zip1"
                                               class="form-control form-required w-px-150" maxlength="4">
                                    </div>
                                    <div class="mx-2">-</div>
                                    <div>
                                        <input type="text" wire:model="updateForm.zip2" name="zip2"
                                               class="form-control form-required w-px-150" maxlength="4">
                                    </div>
                                </div>
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.zip1') }}</span>
                                </div>
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.zip2') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">{{ transm('customer.attributes.prefecture') }}<span
                                    class="required-icon">{{ trans2('required') }}</span></th>
                            <td>
                                <div wire:ignore>
                                    <select name="pref_id" class="form-select2 form-required"
                                            wire:model="updateForm.pref_id" style="width:280px;"
                                            data-placeholder="{{ trans2('select_default') }}">
                                        <option value=""></option>
                                        @foreach ($listPref as $key => $pref)
                                            <option value="{{ data_get($pref,'id') }}" {{ $pref?->id == $customer?->pref_id ? 'selected' : '' }}>{{ data_get($pref,'name') }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div>
                                    @error('updateForm.pref_id') <span
                                        class="message-error">{{ $message }}</span> @enderror
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">{{ transm('customer.attributes.city') }}<span
                                    class="required-icon">{{ trans2('required') }}</span></th>
                            <td>
                                <input type="text" wire:model="updateForm.city" name="city"
                                       class="form-control form-required">
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.city') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap required">{{ transm('customer.attributes.address') }}<span
                                    class="required-icon">{{ trans2('required') }}</span></th>
                            <td>
                                <input type="text" wire:model="updateForm.address" name="address"
                                       class="form-control form-required">
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.address') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap">{{ transm('customer.attributes.building') }}</th>
                            <td>
                                <input type="text" wire:model="updateForm.building" name="building"
                                       class="form-control">
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.building') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap">{{ transm('customer.attributes.contract_count') }}</th>
                            <td>
                                <div class="form-parts-unit d-inline-block">
                                    <input type="text" wire:model="updateForm.contract_count" name=""
                                           class="form-control w-px-140">
                                    <div class="form-unit">{{ trans2('times') }}</div>
                                </div>
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.contract_count') }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-nowrap">{{ transm('customer.attributes.black') }}</th>
                            <td>
                                <div class="form-check">
                                    <label>
                                        <input wire:model="updateForm.black_flag" type="checkbox" name="" {{ $customer?->black_flag?->value ? 'checked' : '' }} value="" class="form-check-input">
                                        <span
                                            class="form-check-text">{{ transm('customer.attributes.black') }}</span>
                                    </label>
                                </div>
                                <div>
                                    <span class="message-error">{{ $errors->first('updateForm.black_flag') }}</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="mb-4 mt-4 d-flex justify-content-between align-items-center">
                        <a href="{{ getRoute('customer.details', ['id' => $customer?->id]) }}"
                           class="btn-text-prev small">{{ trans2('screens.customer.edit.back') }}</a>
                        <button wire:click.prevent="validateUpdate"
                                class="btn btn-dark btn-medium">{{ trans2('button.update') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @include('livewire.admin.customer.script')
@endsection

