<?php

return [
    // routes config
    'routes' => [
        'api' => [
            'prefix' => env('API_PREFIX', 'api'),
            'middleware' => 'api',
            'namespace' => '\Api',
            'as' => 'api.',
        ],
        'admin' => [
            'prefix' => env('ADMIN_PREFIX', 'management'),
            'middleware' => 'admin',
            'namespace' => '\Admin',
            'as' => 'admin.',
        ],
        'web' => [
            'prefix' => env('WEB_PREFIX', '/'),
            'middleware' => 'web',
            'namespace' => '\Web',
            'as' => 'web.',
        ],
    ],

    // model field
    'model_field' => [
        'created' => ['at' => 'ins_date', 'by' => 'ins_id'],
        'updated' => ['at' => 'upd_date', 'by' => 'upd_id'],
        'deleted' => ['flag' => 'del_flag', 'at' => '', 'by' => ''],
    ],

    // model field name
    'model_field_name' => [
        'deleted_flag' => '削除フラグ',
        'created_at' => '登録日時',
        'created_by' => '登録者ID',
        'updated_at' => '更新日時',
        'updated_by' => '更新者ID',
        'deleted_at' => '削除日時',
        'deleted_by' => '削除者ID',
    ],

    // deleted flag
    'deleted_flag' => [
        'off' => 0,
        'on' => 1,
    ],

    // static version for js, css...
    'static_version' => env('STATIC_VERSION', date('YmdHis')),

    // upload
    'media_dir' => 'uploaded/media',
    'ext_blacklist' => ['php', 'phtml', 'html'],
    'tmp_upload_dir' => 'tmp_uploads',
    'no_avatar' => 'assets/css/admin/img/image_default.png',

    // file info
    'file' => [
        'default' => [
            'image' => [
                'ext' => ['jpeg', 'jpg', 'png', 'gif', 'JPG', 'JPEG', 'PNG', 'GIF'], // extension
                'size' => ['min' => 0.001, 'max' => 20], // MB
                'accept' => '.jpeg, .jpg, .png, .gif, .JPG, .JPEG, .PNG, .GIF'
            ]
        ],
    ],

    // export CSV
    'csv' => [
        'users' => [
            'filename' => 'users_' . date('YmdHis'),
            'header' => [
                'id' => 'ID',
                'name' => '名前',
                'email' => 'メールアドレス',
                'created_at' => '登録日時',
                'updated_at' => '更新日時',
            ],
        ],
        'item_types' => [
            'filename' => 'item_types_' . date('YmdHis'),
            'header' => [
                'view_id' => 'ID',
                'name' => '商品区分',
                'ins_date' => '作成日時',
                'upd_date' => '最終編集日時',
            ],
        ],
        'brands' => [
            'filename' => 'brands_' . date('YmdHis'),
            'header' => [
                'view_id' => 'ID',
                'name' => 'ブランド種類名称',
                'shop_brands_count' => '店舗数',
                'year_rate' => '実質年率',
                'brand_item_types' => '取扱商品区分',
                'ins_date' => '作成日時',
                'upd_date' => '最終編集日時',
            ],
        ],
        'shops' => [
            'filename' => 'shops_' . date('YmdHis'),
            'header' => [
                'view_id' => 'ID',
                'pref_name' => '都道府県',
                'name' => '店舗名',
                'shop_brands_count' => '店舗ブランド数',
                'ins_date' => '作成日時',
                'upd_date' => '最終編集日時',
            ],
        ],
        'shop_brands' => [
            'filename' => 'shop_brands_' . date('YmdHis'),
            'header' => [
                'view_id' => 'ID',
                'name' => '店舗ブランド名',
                'brand_name' => 'ブランド名',
                'shop_name' => '店舗名',
                'tel' => '電話番号',
                'credit_flag' => '申込システムの利用',
                'ins_date' => '作成日時',
                'upd_date' => '最終編集日時',
            ],
        ],
        'courses' => [
            'filename' => 'courses_' . date('YmdHis'),
            'header' => [
                'view_id' => 'ID',
                'name_management' => 'コース名（管理）',
                'name_application' => 'コース名（申込）',
                'unit_price' => '単価',
                'principal_price' => '元金',
                'split_fee_flag' => '分割手数料',
                'credit_flag' => '申込システムの利用',
                'ins_date' => '作成日時',
                'upd_date' => '最終編集日時',
            ],
        ],
        'administrators' => [
            'filename' => 'administrators_' . date('YmdHis'),
            'header' => [
                'id' => 'ID',
                'name' => '管理者名',
                'email' => 'メールアドレス',
                'in_charge' => '担当',
                'tel' => '電話番号',
                'lock_flag' => 'ロックフラグ',
                'last_login_datetime' => '最終ログイン日時',
                'auth_type' => '権限',
                'ins_date' => '作成日時',
                'upd_date' => '最終編集日時',
            ],
        ],
        'customers' => [
            'filename' => 'customers_' . date('YmdHis'),
            'header' => [
                'id' => 'お客様ID	',
                'last_name' => 'お名前（姓）',
                'first_name' => 'お名前（名）',
                'last_name_kana' => 'お名前（姓）カナ',
                'first_name_kana' => 'お名前（名）カナ',
                'birthday' => '誕生日',
                'tel1' => '電話番号1',
                'tel2' => '電話番号2',
                'tel3' => '電話番号3',
                'application_status' => 'ステータス',
                'application_date' => '申込日',
                'brand_name' => 'ブランド',
                'ins_date' => '登録日時',
                'upd_date' => '更新日時',
            ],
        ],
        'contracts' => [
            'filename' => 'contracts_' . date('YmdHis'),
            'header' => [
                'contract_id' => 'ID',
                'full_name' => 'お名前',
                'payment_company_flag' => '収納会社',
                'regist_number' => '登録番号',
                'contract_status' => 'ステータス',
                'contract_date' => '契約成立日',
                'courses' => '契約内容',
            ],
        ],
        'payments' => [
            'unpaid_balance' => [
                'filename' => 'ジャックス_未入金一覧_' . date('YmdHis'),
                'header' => [
                    'id' => 'ID',
                    'customer_id' => '顧客ID',
                    'application_id' => '申込ID',
                    'payment_plan_date' => '入金予定日',
                    'payment_type' => '支払種別',
                    'amount_paid' => '金額',
                    'balance_difference' => '過不足金',
                    'regist_number' => '登録番号',
                    'payment_status' => '入金ステータス',
                ],
            ],
            'deposit_data' => [
                'filename' => 'ジャックス_入金データ_' . date('YmdHis'),
                'header' => [
                    'id' => '委託者番号',
                    'payment_plan_date' => '請求年月',
                    'bank_code' => '金融機関コード',
                    'branch_code' => '支店コード',
                    'bank_account_type' => '預金種目',
                    'bank_account_number' => '口座番号',
                    'bank_account_name_kana' => '口座名義人名（カナ）',
                    'total_amount' => '請求金額',
                    'type' => '新規コード',
                    'regist_number' => '契約者番号',
                ],
            ],
        ],
        'dashboard' => [
            'filename' => 'ダッシュボード_' . date('YmdHis'),
            'header' => [
                'target_month' => '',
                'total_contracts' => '契約件数',
                'new_contracts' => '新規契約件数',
                'new_contract_amount' => '新契約総支払額',
                'completed_contracts' => '契約満了件数',
                'cancelled_contracts' => '解約件数',
                'cancelled_amount' => '解約金額',
                'forced_cancelled_contracts' => '強制解約件数',
                'forced_contract_cancel_amount' => '強制解約金額',
                'uncollected_contracts' => '未回収件数',
                'uncollected_amount' => '未回収金額',
            ]
        ],
        'balances' => [
            'filename' => '残高管理_' . date('YmdHis'),
            'header' => [
                'payment_month' => '',
                'balance' => '残高',
                'balance_principal ' => '残高(元金(税込))',
                'payment_amount' => '支払額',
                'principal ' => '元金(税込)',
                'installment_fee' => '分割手数料',
                'late_fee' => '損害遅延金',
                'reissue_fee' => '再発行手数料',
                'total_payment_new_contract' => '新契約総支払額',
                'total_payment_new_contract_principal' => '新契約総支払額(元金(税込))',
                'canceled_fee' => '解約金',
                'forced_termination_fee' => '強制解約金',
            ],
        ],
    ],

    // paginate number
    'page_number' => 10,

    // gmo
    'gmo' => [
        'url' => env('GMO_URL', ''),
        'url_link_type' => env('GMO_URL_LINK_TYPE', ''),
        'public_key' => env('GMO_PUBLIC_KEY', ''),
        'hash_key' => env('GMO_HASH_KEY', ''),
        'site_id' => env('GMO_SITE_ID', ''),
        'site_pass' => env('GMO_SITE_PASS', ''),
        'shop_id' => env('GMO_SHOP_ID', ''),
        'shop_pass' => env('GMO_SHOP_PASS', ''),
    ],

    // logs
    'logs' => [
        'sql_log_filename' => 'sql',
        'zip_log' => [
            'keep_day' => env('ZIP_LOG_KEEP_DAY', 5),
        ],
        'dump_db' => [
            'file_name' => 'database_backup_' . date('YmdHis') . '.sql.gz',
            'path' => storage_path('/backups/database'),
            'max_file' => env('DUMP_DB_MAX_FILE', 7),
        ],
    ],

    // fire base
    'fire_base' => [
        'url_get_info' => 'https://iid.googleapis.com/iid/info/',
        'url_add_topic' => 'https://iid.googleapis.com/iid/v1:batchAdd',
        'url_remove_topic' => 'https://iid.googleapis.com/iid/v1:batchRemove',
        'url_send' => 'https://fcm.googleapis.com/fcm/send',
        'server_key' => env('FCM_SERVER_KEY', ''), // use Server key
        'sound' => '', // sound
        'limit_tokens' => 100,
    ],

    // password reset expired time
    'password_reset_expired_time' => 15, // minutes

    // http
    'http_client_verify' => env('HTTP_CLIENT_VERIFY', true),

    // postgres application prefix name
    'postgres_application_prefix' => env('POSTGRES_APPLICATION_PREFIX', 'application'),

    // mail
    'mail' => [
        'api_url' => env('API_MAIL_URL'),
        'api_token' => env('API_MAIL_TOKEN')
    ],

    // auth two factor
    'disable_two_factor' => env('DISABLE_TWO_FACTOR', false),

    // cookie per page
    'cookie_per_page_name' => 'cookie_per_page',

    // pdf
    'pdf_extension_path' => app_path() . env('LIB_CONVERT_HTML_PDF_SRC', ''), // wkhtmltox | wkhtmltox_linux
    'pdf_script_path' => base_path(env('PDF_SCRIPT_PATH', '')),

    // import csv
    'csv_sample_payment_headers' => [
        'id' => '委託者番号',
        'payment_plan_date' => '請求年月',
        'bank_code' => '金融機関コード',
        'branch_code' => '支店コード',
        'bank_account_type' => '預金種目',
        'bank_account_number' => '口座番号',
        'bank_account_name_kana' => '口座名義人名（カナ）',
        'total_amount' => '請求金額',
        'type' => '新規コード',
        'regist_number' => '契約者番号',
        'result_status' => '引落結果',
    ],

    // required
    'init_required' => 'init_required',
    'monthly_required' => 'monthly_required',

    // validate month
    'month_30_days' => ['04', '06', '09', '11'],
    'month_31_days' => ['01', '03', '05', '07', '08', '10', '12'],

    // ...

    'start_month' => [
        1 => '1月',
        2 => '2月',
        3 => '3月',
        4 => '4月',
        5 => '5月',
        6 => '6月',
        7 => '7月',
        8 => '8月',
        9 => '9月',
        10 => '10月',
        11 => '11月',
        12 => '12月',
    ],
    'page_params' => [
        'dashboard_operator' => 'page_do',
    ],
    'folder_file' => 'file',
    'folder_image' => 'image',
    's3' => [
        'base_url' => env('AWS_CDN_URL', '')
    ],
    'search_all' => 'all',
    'page_number_dash_board' => 5,

    // ...
    'mail_login_confirm_subject' => '【LadyBird】認証コード',
    'mail_reset_pass_subject' => '【LadyBird】パスワード再設定',

    // sms
    'sms' => [
        'api_url' => env('SMS_API_URL', ''),
        'username' => env('SMS_API_USERNAME', ''),
        'password' => env('SMS_API_PASSWORD', ''),
    ],
    // api get address from postcode
    'api_postcode' => 'https://apis.postcode-jp.com/api/v6/postcodes/',
    'api_postcode_key' => env('API_POSTCODE_JP_KEY', ''),
];
