<?php

namespace App\Models;

use App\Models\Base\CustomModel;
use App\Models\Presenters\PLoanPayment;
use App\Models\Presenters\PLoanWithdrawal;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Loan<PERSON>ithdrawal extends CustomModel
{
    use PLoanWithdrawal;
    use HasFactory;

    protected $table = 'loan_withdrawals';

    protected $fillable = [
        'id',
        'customer_id',
        'application_id',
        'withdrawal_date',
        'amount',
        'withdrawal_type',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function loanTransactionLogs(): HasMany
    {
        return $this->hasMany(LoanTransactionLog::class, 'loan_withdrawal_id', 'id');
    }
    public function loanRefunds(): HasMany
    {
        return $this->hasMany(LoanRefund::class, 'loan_withdrawal_id', 'id');
    }
}
