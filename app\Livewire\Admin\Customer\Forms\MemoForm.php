<?php

namespace App\Livewire\Admin\Customer\Forms;

use App\Enums\ApplicationInspectionCancelStatusEnum;
use App\Enums\ApplicationInspectionStatusEnum;
use App\Enums\ApplicationInspectionToShopEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Models\Application;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\Rule;

class MemoForm extends BaseAdminForm
{
    public $to_shop_id = ApplicationInspectionToShopEnum::HEADQUARTERS;

    public $status;

    public $cancel_status;

    public $comment;

    public $files;

    public function rules()
    {
        $rules = [
            'to_shop_id' => ['nullable', Rule::in(ApplicationInspectionToShopEnum::getValues())],
            'status' => ['nullable', Rule::in(ApplicationInspectionStatusEnum::getValues())],
            'cancel_status' => ['nullable', Rule::in(ApplicationInspectionCancelStatusEnum::getValues())],
            'comment' => 'nullable|string|max:1000',
            'files' => 'nullable|array',
            'files.*' => [
                'file',
                'max:20480',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $originalName = $value->getClientOriginalName();
                        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));

                        if (!in_array($extension, ['pdf', 'jpeg', 'png', 'jpg'])) {
                            $fail(__('validation.file_exts'));
                        }
                    }
                }
            ],
        ];

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.application_inspection_status.attributes');
    }
}
