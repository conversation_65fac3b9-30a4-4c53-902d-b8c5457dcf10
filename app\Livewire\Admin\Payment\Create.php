<?php

namespace App\Livewire\Admin\Contract;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Admin\Contract\Forms\CreateForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\CustomerService;
use App\Services\PrefService;
use Livewire\Attributes\On;
use App\Services\ToastService;

class Create extends BaseAdminPageComponent
{
    public CreateForm $createForm;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CUSTOMER;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
    }

    public function validateCreate()
    {
        $this->createForm->validate();

        $this->setDataCreateConfirmModal(getConstant('SCREENS.CUSTOMER'), 'customer-store');
    }

    #[On('customer-store')]
    public function store()
    {
        try {
            $params = $this->createForm->all();
            if(!$params['black_flag']) {
                $params['black_flag'] = 0;
            }

            $customer = app(CustomerService::class)->store($params);

            if ($customer) {
                app(ToastService::class)->createSuccess();
            } else {
                app(ToastService::class)->createError();
            }

            $this->createForm->reset();

            return redirect()->route('admin.customer.details', ['id' => $customer->id]);
        } catch (\Throwable $e) {
            logError($e);
            return redirect()->route('admin.customer.new');
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.create', [
            'listPrefs'=> app(PrefService::class)->getAllPrefs(),
        ]);
    }
}
