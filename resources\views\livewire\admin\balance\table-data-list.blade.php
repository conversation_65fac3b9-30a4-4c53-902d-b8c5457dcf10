<div>
    <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
        <table class="table table-datalist w-100">
            <thead>
                <tr>
                    <th scope="col" class="empty"></th>
                    <th scope="col">{{ trans2('screens.balance.loan_balance') }}</th>
                    <th scope="col">{{ trans2('screens.balance.loan_balance') }}<small>({{ trans2('screens.balance.principal') }}({{ trans2('screens.balance.include_tax') }}))</small></th>
                    <th scope="col">{{ trans2('screens.balance.paid_amount') }}</th>
                    <th scope="col">{{ trans2('screens.balance.principal') }}<small>({{ trans2('screens.balance.include_tax') }})</small></th>
                    <th scope="col">{{ trans2('screens.balance.fee_total') }}</th>
                    <th scope="col">{{ trans2('screens.balance.delay_damage_total') }}</th>
                    <th scope="col">{{ trans2('screens.balance.reissue_fee_total') }}</th>
                    <th scope="col">{{ trans2('screens.balance.current_month_total') }}</th>
                    <th scope="col">{{ trans2('screens.balance.current_month_total') }}<br><small>({{ trans2('screens.balance.principal') }}({{ trans2('screens.balance.include_tax') }}))</small></th>
                    <th scope="col">{{ trans2('screens.balance.cancel_amount_total') }}</th>
                    <th scope="col">{{ trans2('screens.balance.forced_cancel_amount_total') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($dataList as $data)
                    <tr>
                        <th>{{ formatDottedYearMonth(data_get($data, 'payment_month')) }}</th>
                        <td>{!! yenFormatNumber(data_get($data, 'loan_balance'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'remaining_principal_estimated'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'paid_amount'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'principal_total'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'fee_total'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'delay_damage_total'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'reissue_fee_total'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'current_month_contract_total'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'current_month_principal_total'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'cancel_amount_total'), '&yen;') !!}</td>
                        <td>{!! yenFormatNumber(data_get($data, 'forced_cancel_amount_total'), '&yen;') !!}</td>
                    </tr>
                @endforeach

                @if($dataList->isNotEmpty())
                    <tr>
                        <th>{{ trans2('screens.payment.index.total') }}</th>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'loan_balance')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'remaining_principal_estimated')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'paid_amount')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'principal_total')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'fee_total')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'delay_damage_total')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'reissue_fee_total')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'current_month_contract_total')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'current_month_principal_total')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'cancel_amount_total')) }}</td>
                        <td class="bg-caution"><span class="me-1">&yen;</span>{{ number_format(data_get($total, 'forced_cancel_amount_total')) }}</td>
                    </tr>
                @endif
            </tbody>
        </table>
        @if ($dataList->isEmpty())
            @include('components.no-result-found')
        @endif
    </div>
    {{-- <div class="text-end fc-secandary small">（更新日時：2025-02-23 13:15）</div> --}}
</div>
