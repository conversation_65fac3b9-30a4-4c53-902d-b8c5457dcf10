<?php

namespace App\Models;

use App\Enums\PaymentStatusEnum;
use App\Models\Base\CustomModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LoanPaymentAllocation extends CustomModel
{
    use HasFactory;

    protected $table = 'loan_payment_allocations';

    protected $fillable = [
        'id',
        'customer_id',
        'application_id',
        'loan_schedule_id',
        'loan_payment_id',
        'amount',
        'before_payment_status',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function loanSchedule()
    {
        return $this->belongsTo(LoanSchedule::class);
    }

    public function loanPayment()
    {
        return $this->belongsTo(LoanPayment::class);
    }

    protected $casts = [
        'before_payment_status' => PaymentStatusEnum::class,
    ];
}
