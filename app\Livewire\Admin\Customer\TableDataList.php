<?php

namespace App\Livewire\Admin\Customer;

use App\Livewire\Base\BaseDataListComponent;
use App\Services\ApplicationService;
use App\Services\CustomerService;
use App\Services\CsvService;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;

class TableDataList extends BaseDataListComponent
{
    public $keyword;
    public $birthday;
    public $tel;
    public $brand_id;
    public $shop_id;
    public $item_type_id;
    public $course_id;
    public $application_date_from;
    public $application_date_to;
    public $payment_start_month_from;
    public $payment_start_month_to;
    public $contract_date_from;
    public $contract_date_to;
    public $contract_cancel_date_from;
    public $contract_cancel_date_to;
    public $status;

    protected $listeners = ['refresh-customer' => '$refresh'];

    #[On('update-advanced-search')]
    public function getAdvancedSearchAttribute($data)
    {
        $this->brand_id = $data['brand_id'] ?? '';
        $this->shop_id = $data['shop_id'] ?? '';
        $this->item_type_id = $data['item_type_id'] ?? '';
        $this->course_id = $data['course_id'] ?? '';
        $this->application_date_from = $data['application_date_from'] ?? '';
        $this->application_date_to = $data['application_date_to'] ?? '';
        $this->payment_start_month_from = convertJapaneseYearMonthToYYYYMM($data['payment_start_month_from']) ?? '';
        $this->payment_start_month_to = convertJapaneseYearMonthToYYYYMM($data['payment_start_month_to']) ?? '';
        $this->contract_date_from = $data['contract_date_from'] ?? '';
        $this->contract_date_to = $data['contract_date_to'] ?? '';
        $this->contract_cancel_date_from = $data['contract_cancel_date_from'] ?? '';
        $this->contract_cancel_date_to = $data['contract_cancel_date_to'] ?? '';
        $this->status = $data['status'] ?? '';
    }

    #[On('update-search')]
    public function geSearchAttribute($data)
    {
        $this->keyword = $data['keyword'] ?? '';
        $this->birthday = $data['birthday'] ?? '';
        $this->tel = $data['tel'] ?? '';
    }

    #[On('export_csv')]
    public function downloadCSV()
    {
        $dataSearch = [
            'keyword' => $this->keyword,
            'tel' => $this->tel,
            'birthday' => $this->birthday,
            'brand_id' => $this->brand_id,
            'shop_id' => $this->shop_id,
            'item_type_id' => $this->item_type_id,
            'course_id' => $this->course_id,
            'application_date_from' => $this->application_date_from,
            'application_date_to' => $this->application_date_to,
            'payment_start_month_from' => $this->payment_start_month_from,
            'payment_start_month_to' => $this->payment_start_month_to,
            'contract_date_from' => $this->contract_date_from,
            'contract_date_to' => $this->contract_date_to,
            'contract_cancel_date_from' => $this->contract_cancel_date_from,
            'contract_cancel_date_to' => $this->contract_cancel_date_to,
            'status' => $this->status,
        ];

        $dataList = app(CustomerService::class)->search($dataSearch, $this->perPage, true);
        $headers = getConfig('csv.customers.header');
        $filename = getConfig('csv.customers.filename') . '.csv';

        return app(CsvService::class)->exportCsv($filename, $headers, $dataList);
    }

    public function render()
    {
        $dataSearch = [
            'keyword' => $this->keyword,
            'tel' => $this->tel,
            'birthday' => $this->birthday,
            'brand_id' => $this->brand_id,
            'shop_id' => $this->shop_id,
            'item_type_id' => $this->item_type_id,
            'course_id' => $this->course_id,
            'application_date_from' => $this->application_date_from,
            'application_date_to' => $this->application_date_to,
            'payment_start_month_from' => $this->payment_start_month_from,
            'payment_start_month_to' => $this->payment_start_month_to,
            'contract_date_from' => $this->contract_date_from,
            'contract_date_to' => $this->contract_date_to,
            'contract_cancel_date_from' => $this->contract_cancel_date_from,
            'contract_cancel_date_to' => $this->contract_cancel_date_to,
            'status' => $this->status,
        ];

        $listApplication = app(CustomerService::class)->search($dataSearch, $this->perPage);
        return $this->viewLivewireAdmin('customer.table-data-list', [
            'tableData' => $listApplication,
        ]);
    }
}
