<?php

namespace App\Livewire\Admin\Application\Identification;

use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationFileRepository;
use App\Services\FileStorageService;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use App\Services\ToastService;
use Illuminate\Support\Facades\DB;

class Delete extends BaseAdminPageComponent
{
    #[Reactive]
    public $id;

    #[Reactive]
    public $key;

    #[On('delete')]
    public function delete()
    {
        try {
            $file = app(ApplicationFileRepository::class)->findOrFail($this->id);

            if (empty($file)) {
                $messages['error'] = __('messages.no_data');
                $this->dispatch('flash-message', $messages);
                return;
            }

            DB::beginTransaction();

             // unlink file from storage
            app(FileStorageService::class)->deleteFile($file->file_url);

            // delete file url
            $file->delete();

            app(ToastService::class)->deleteSuccess();

            DB::commit();

            $this->dispatch('app-identification-reload');
        } catch (\Throwable $th) {
            DB::rollBack();
            logError($th);
            app(ToastService::class)->deleteError();
            return;
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('application.identification.delete');
    }
}
