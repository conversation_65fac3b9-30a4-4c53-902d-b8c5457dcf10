<div>
    <h1 class="application-page-title">6.お客様登録画面 / 連絡指定</h1>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">ご連絡指定<span class="required-icon">必須</span></th>
                <td>
                    <div class="d-flex">
                        <div class="form-radio me-5">
                            <label>
                                <input type="radio" name="contact" value="1" class="form-radio-input"
                                    wire:model="contactForm.contact_flag">
                                <span class="form-radio-text">自宅</span>
                            </label>
                        </div>
                        <div class="form-radio">
                            <label>
                                <input type="radio" name="contact" value="2" class="form-radio-input"
                                    wire:model="contactForm.contact_flag">
                                <span class="form-radio-text">携帯</span>
                            </label>
                        </div>
                    </div>
                    @error('contactForm.contact_flag')
                    <span class="message-error">{{ $message }}</span>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="required">第一希望<span class="required-icon">必須</span></th>
                <td>
                    <div class="row">
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control datepicker form-required w-100"
                                data-date-format="yy年m月d日" wire:model="contactForm.contact_hope_date1">
                        </div>
                        <div class="col-6 d-flex align-items-center">
                            <div class="w-50" wire:ignore>
                                <select name="" class="form-select2 form-required" data-placeholder=""
                                    wire:model="contactForm.contact_hope_start_time1" style="width:100%;">
                                    <option value=""></option>
                                    <option value="1100">11:00</option>
                                    <option value="1130">11:30</option>
                                    <option value="1200">12:00</option>
                                    <option value="1230">12:30</option>
                                    <option value="1300">13:00</option>
                                    <option value="1330">13:30</option>
                                    <option value="1400">14:00</option>
                                    <option value="1430">14:30</option>
                                    <option value="1500">15:00</option>
                                    <option value="1530">15:30</option>
                                    <option value="1600">16:00</option>
                                    <option value="1630">16:30</option>
                                    <option value="1700">17:00</option>
                                    <option value="1730">17:30</option>
                                    <option value="1800">18:00</option>
                                </select>
                            </div>
                            <div class="mx-2">〜</div>
                            <div class="w-50" wire:ignore>
                                <select name="" class="form-select2 form-required" data-placeholder=""
                                    wire:model="contactForm.contact_hope_end_time1" style="width:100%;">
                                    <option value=""></option>
                                    <option value="1100">11:00</option>
                                    <option value="1130">11:30</option>
                                    <option value="1200">12:00</option>
                                    <option value="1230">12:30</option>
                                    <option value="1300">13:00</option>
                                    <option value="1330">13:30</option>
                                    <option value="1400">14:00</option>
                                    <option value="1430">14:30</option>
                                    <option value="1500">15:00</option>
                                    <option value="1530">15:30</option>
                                    <option value="1600">16:00</option>
                                    <option value="1630">16:30</option>
                                    <option value="1700">17:00</option>
                                    <option value="1730">17:30</option>
                                    <option value="1800">18:00</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div>
                            @error('contactForm.contact_hope_date1')
                            <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                        <div>
                            @error('contactForm.contact_hope_start_time1')
                            <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                        <div>
                            @error('contactForm.contact_hope_end_time1')
                            <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">第二希望<span class="required-icon">必須</span></th>
                <td>
                    <div class="row">
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control datepicker form-required w-100"
                                data-date-format="yy年m月d日" wire:model="contactForm.contact_hope_date2">
                        </div>
                        <div class="col-6 d-flex align-items-center">
                            <div class="w-50" wire:ignore>
                                <select name="" class="form-select2 form-required" data-placeholder=""
                                    wire:model="contactForm.contact_hope_start_time2" style="width:100%;">
                                    <option value=""></option>
                                    <option value="1100">11:00</option>
                                    <option value="1130">11:30</option>
                                    <option value="1200">12:00</option>
                                    <option value="1230">12:30</option>
                                    <option value="1300">13:00</option>
                                    <option value="1330">13:30</option>
                                    <option value="1400">14:00</option>
                                    <option value="1430">14:30</option>
                                    <option value="1500">15:00</option>
                                    <option value="1530">15:30</option>
                                    <option value="1600">16:00</option>
                                    <option value="1630">16:30</option>
                                    <option value="1700">17:00</option>
                                    <option value="1730">17:30</option>
                                    <option value="1800">18:00</option>
                                </select>
                            </div>
                            <div class="mx-2">〜</div>
                            <div class="w-50" wire:ignore>
                                <select name="" class="form-select2 form-required" data-placeholder=""
                                    wire:model="contactForm.contact_hope_end_time2" style="width:100%;">
                                    <option value=""></option>
                                    <option value="1100">11:00</option>
                                    <option value="1130">11:30</option>
                                    <option value="1200">12:00</option>
                                    <option value="1230">12:30</option>
                                    <option value="1300">13:00</option>
                                    <option value="1330">13:30</option>
                                    <option value="1400">14:00</option>
                                    <option value="1430">14:30</option>
                                    <option value="1500">15:00</option>
                                    <option value="1530">15:30</option>
                                    <option value="1600">16:00</option>
                                    <option value="1630">16:30</option>
                                    <option value="1700">17:00</option>
                                    <option value="1730">17:30</option>
                                    <option value="1800">18:00</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div>
                            @error('contactForm.contact_hope_date2')
                            <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                        <div>
                            @error('contactForm.contact_hope_start_time2')
                            <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                        <div>
                            @error('contactForm.contact_hope_end_time2')
                            <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button wire:click.prevent="validateSave" class="btn btn-dark btn-large">{{ trans2('button.next') }}</button>
    </div>
</div>
@include('livewire.admin.application.customer.script')
