@php
$applicationStatusText = \App\Enums\ApplicationStatusEnum::texts();
$applicationStatusColor = \App\Enums\ApplicationStatusEnum::colors();
@endphp
<div>
    {{-- skeleton loading overlay --}}
    <div wire:loading.flex class="row">
        @include('components.loading-overlay')
    </div>
    <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false" style="margin: 0px;">
        <table class="table table-borderless table-thead-bordered table-align-middle table-database" id="dataListTable">
            <thead>
                <tr>
                    <th class="text-nowrap" scope="col">{{ transm('customer.attributes.ID') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('customer.attributes.status') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('customer.attributes.brand') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('customer.attributes.name') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('customer.attributes.name_kana') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('customer.attributes.birthday') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('customer.attributes.tel') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('customer.attributes.application_date') }}</th>
                </tr>
            </thead>
            <tbody>
                @if ($tableData->isNotEmpty())
                    @foreach($tableData as $item)
                        <tr wire:navigate href="{{ getRoute('customer.details', ['id' => $item?->id]) }}" data-link>
                            <td class="text-break">{{ $item?->id }}</td>
                            <td class="text-break">
                                @if ($item->application_status)
                                    <span class="badge badge-status rounded-pill badge-lg badge-{{ $applicationStatusColor[$item->application_status] }}">{{ $applicationStatusText[$item->application_status] }}</span>
                                @endif
                            </td>
                            <td class="text-break">{{ $item?->brand_name }}</td>
                            <td class="text-break">{{ $item?->last_name }} {{ $item?->first_name }}</td>
                            <td class="text-break">{{ $item?->last_name_kana }} {{ $item?->first_name_kana }}</td>
                            <td class="text-nowrap">{{ $item->birthday ? \Carbon\Carbon::parse($item?->birthday)->format('Y/m/d') : '' }}</td>
                            <td class="text-nowrap">{{ $item?->tel1 }}-{{ $item?->tel2 }}-{{ $item?->tel3 }}</td>
                            <td class="text-nowrap">{{ $item->application_date ? \Carbon\Carbon::parse($item->application_date)->format('Y/m/d') : '' }}</td>
                        </tr>
                    @endforeach
                @endif
            </tbody>
        </table>
        @if ($tableData->isEmpty())
            @include('components.no-result-found')
        @endif
    </div>
    <div class="text-center mt-4">
        @include('components.pagination-range-text', ['items' => $tableData])
        {{ $tableData->links('components.pagination') }}
    </div>
</div>
