<?php

namespace App\Livewire\Admin\Customer;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\CustomerService;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use App\Services\ToastService;

class Delete extends BaseAdminPageComponent
{
    #[Reactive]
    public $id;

    #[Reactive]
    public $isCanDelete;

    #[On('delete')]
    public function delete()
    {

        try {
            $customer = app(CustomerService::class)->getCustomer($this->id);

            if (empty($customer)) {
                app(ToastService::class)->error(__('messages.no_data'));
                $this->redirect(route('admin.contract.index'));
                return;
            }

            $customer->delete();

            app(ToastService::class)->deleteSuccess();

            return redirect()->route('admin.customer.index');
        } catch (\Throwable $th) {
            logError($th);
            app(ToastService::class)->deleteError();
            return;
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.delete');
    }
}
