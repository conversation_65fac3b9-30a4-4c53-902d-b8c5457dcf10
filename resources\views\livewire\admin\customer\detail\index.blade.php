@php
    $func = \App\Enums\FunctionEnum::CUSTOMER;
    $inspectionStatus = getInspectionStatusForNotify($customer?->id);
@endphp
<div id="contents" class="scroll-validate">
    <div class="contents-container">
        <div class="container-fluid">
            <div class="page-detail-header d-flex justify-content-between">
                <h1 class="page-detail-header-name">
                    <div class="name-group">
                        <div class="name-kana">{{ $customer?->last_name_kana }}</div>
                        <div class="name-field">{{ $customer?->last_name }}</div>
                    </div>
                    <div class="name-group">
                        <div class="name-kana">{{ $customer?->first_name_kana }}</div>
                        <div class="name-field">{{ $customer?->first_name }}</div>
                    </div>
                    <div class="mb-2">{{ trans2('screens.customer.detail.mr') }}</div>
                </h1>
                <div class="page-detail-header-link minw-px-120">
                    <a href="{{ getRoute('customer.index') }}" class="btn-text-prev small">{{ trans2('screens.customer.detail.back_to_list') }}</a>
                </div>
            </div>
            <div class="mb-5">
                @if ($inspectionStatus)
                    <a href="{{ getRoute('customer.application.memo.index', [$customer?->id, $inspectionStatus?->application_id]) }}" class="fc-primary btn-text-next btn-information">
                        <i class="icon">
                            <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M5.99949 0C5.52538 0 5.14234 0.391016 5.14234 0.875V1.4C3.18699 1.80469 1.71377 3.57109 1.71377 5.6875V6.20156C1.71377 7.48672 1.25038 8.72812 0.414666 9.69063L0.216452 9.91758C-0.00854843 10.1746 -0.0621199 10.5437 0.0744873 10.8582C0.211094 11.1727 0.51913 11.375 0.85663 11.375H11.1423C11.4798 11.375 11.7852 11.1727 11.9245 10.8582C12.0638 10.5437 12.0075 10.1746 11.7825 9.91758L11.5843 9.69063C10.7486 8.72812 10.2852 7.48945 10.2852 6.20156V5.6875C10.2852 3.57109 8.81199 1.80469 6.85663 1.4V0.875C6.85663 0.391016 6.47359 0 5.99949 0ZM7.21288 13.4887C7.53431 13.1605 7.71377 12.7148 7.71377 12.25H5.99949H4.2852C4.2852 12.7148 4.46467 13.1605 4.78609 13.4887C5.10752 13.8168 5.54413 14 5.99949 14C6.45484 14 6.89145 13.8168 7.21288 13.4887Z"/>
                            </svg>
                        </i>
                        {{-- show application_inspection_status.status --}}
                        {{ trans2('screens.customer.detail.sub_breadcrumb_review') }}「{{ $inspectionStatus?->status?->text ?? '' }}」{{ trans2('screens.customer.detail.sub_breadcrumb_welcome') }}
                    </a>
                @endif
            </div>

            <ul class="page-detail-tabs nav nav-tabs">
                <li class="nav-item" wire:ignore>
                    <a href="{{ getRoute('customer.details', ['id' => $customer?->id]) }}" class="nav-link {{ (request()->routeIs('admin.customer.details') || request()->routeIs('admin.customer.edit')) ? 'active' : '' }}">
                        {{ trans2('screens.customer.detail.customer_tab') }}
                    </a>
                </li>
                <li class="nav-item" wire:ignore>
                    <a href="{{ getRoute('customer.application.index', ['customer_id' => $customer?->id]) }}" class="nav-link
                    {{ (
                        request()->routeIs('admin.customer.application.index') ||
                         request()->routeIs('admin.customer.application.memo.index') ||
                          request()->routeIs('admin.customer.application.detail.index')
                          ) ? 'active' : '' }}
                    ">
                        {{ trans2('screens.customer.detail.application_tab') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ getRoute('customer.contract.index', ['customer_id' => $customer?->id]) }}" class="nav-link {{ (request()->routeIs('admin.customer.contract.index')) ? 'active' : '' }}">{{ trans2('screens.customer.detail.contract_tab') }}</a>
                </li>
            </ul>

            {{--  slot content page detail --}}
            @yield('detail-tab')

            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ getRoute('customer.index') }}" class="btn-text-prev small">{{ trans2('screens.customer.detail.back_to_list') }}</a>

                <div>
                    <livewire:admin.customer.delete :id="data_get($customer, 'id')" :isCanDelete="$isCanDelete"/>
                </div>
            </div>
        </div>
    </div>
    <livewire:common.tracking-history-modal :function="$func" :id="data_get($customer, 'id')" />
</div>
