@extends('livewire.admin.contract.detail.index')
@section('detail-tab')
    @php
        $paymentStatusText = \App\Enums\PaymentStatusEnum::texts();
        $paymentStatusColor = \App\Enums\PaymentStatusEnum::colors();
    @endphp
    <div class="card mb-3">
        <div class="card-body">
            <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
                <h2 class="heading-2 mt-4">{{ trans2('screens.contract.deposit_tab.deposit_list') }}</h2>
                <table class="table table-borderless table-thead-bordered table-align-middle table-database"
                    id="dataListTable" data-link="">
                    <thead>
                        <tr>
                            <th scope="col">{{ transm('payment.attributes.id') }}</th>
                            <th scope="col">{{ transm('payment.attributes.payment_plan_date') }}</th>
                            <th scope="col">{{ transm('payment.attributes.payment_status') }}</th>
                            <th scope="col">{{ transm('payment.attributes.deposit_amount') }}</th>
                            <th scope="col">{{ transm('payment.attributes.deposit_payment_type') }}</th>
                            <th scope="col">{{ transm('payment.attributes.balance_difference') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($loanSchedules as $loanSchedule)
                            <tr onclick="window.location='{{ getRoute('payment.details', ['id' => data_get($loanSchedule,'loan_schedule_id')])  }}'">
                                <td>{{ data_get($loanSchedule, 'loan_schedule_id') }}</td>
                                <td>{{ data_get($loanSchedule, 'payment_plan_date') }}</td>
                                <td>
                                    @if ($loanSchedule->payment_status)
                                        <span class="badge badge-status rounded-pill badge-lg badge-{{ $paymentStatusColor[$loanSchedule->payment_status?->value] }}">{{ $paymentStatusText[$loanSchedule->payment_status?->value] }}</span>
                                    @endif
                                </td>
                                <td>¥ {{ number_format(data_get($loanSchedule, 'total_amount'), 0, '.', ',') }}</td>
                                <td>{{ data_get($loanSchedule, 'payment_type.text') }}</td>
                                <td class="{{ $loanSchedule->balance_difference === null ? 'text-end' : '' }}">
                                    ¥{{ $loanSchedule->balance_difference !== null ? number_format($loanSchedule->balance_difference, 0, '.', ',') : '-' }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <h2 class="heading-2 mt-4">{{ trans2('screens.contract.deposit_tab.deposit_list') }}</h2>
            <table class="table table-deposit">
                <thead>
                <tr>
                    <th colspan="2">{{ trans2('screens.payment.index.status.waiting_for_payment') }}</th>
                    <th colspan="2">{{ trans2('screens.payment.index.status.payment_complete') }}</th>
                    <th colspan="2">{{ trans2('screens.payment.index.status.unpaid') }}</th>
                    <th colspan="2">{{ trans2('screens.payment.index.status.other') }}</th>
                    <th colspan="2">{{ trans2('screens.payment.index.total') }}</th>
                </tr>
                </thead>
                <tbody>
                    <tr>
                        <th>{{ trans2('screens.payment.index.number') }}</th>
                        <th>{{ trans2('screens.payment.index.amount') }}</th>
                        <th>{{ trans2('screens.payment.index.number') }}</th>
                        <th>{{ trans2('screens.payment.index.amount') }}</th>
                        <th>{{ trans2('screens.payment.index.number') }}</th>
                        <th>{{ trans2('screens.payment.index.amount') }}</th>
                        <th>{{ trans2('screens.payment.index.number') }}</th>
                        <th>{{ trans2('screens.payment.index.amount') }}</th>
                        <th>{{ trans2('screens.payment.index.number') }}</th>
                        <th>{{ trans2('screens.payment.index.amount') }}</th>
                    </tr>

                        <tr>
                            <td>{{ data_get($paymentDatas, 'waiting_count') ?? 0 }}{{ trans2('subject') }}</td>
                            <td><span class="me-1">¥</span>{{ number_format(data_get($paymentDatas, 'waiting_total_amount',0), 0, '.', ',') }}</td>

                            <td>{{ data_get($paymentDatas, 'deposited_count') ?? 0 }}{{ trans2('subject') }}</td>
                            <td><span class="me-1">¥</span>{{ number_format(data_get($paymentDatas, 'deposited_total_amount',0), 0, '.', ',') }}</td>

                            <td>{{ data_get($paymentDatas, 'unpaid_count') ?? 0 }}{{ trans2('subject') }}</td>
                            <td><span class="me-1">¥</span>{{ number_format(data_get($paymentDatas, 'unpaid_total_amount',0), 0, '.', ',') }}</td>

                            <td>{{ data_get($paymentDatas, 'other_count') ?? 0 }}{{ trans2('subject') }}</td>
                            <td><span class="me-1">¥</span>{{ number_format(data_get($paymentDatas, 'other_total_amount',0), 0, '.', ',') }}</td>

                            <td>{{ data_get($paymentDatas, 'grand_count') ?? 0 }}{{ trans2('subject') }}</td>
                            <td><span class="me-1">¥</span>{{ number_format(data_get($paymentDatas, 'grand_total_amount',0), 0, '.', ',') }}</td>
                        </tr>

                </tbody>
            </table>
        </div>
    </div>
@endsection
