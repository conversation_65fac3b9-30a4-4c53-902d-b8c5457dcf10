<?php

namespace App\Repositories;

use App\Models\Application;
use App\Models\ApplicationCourse;
use App\Models\Brand;
use App\Models\Course;
use App\Models\Customer;
use App\Models\CustomerBrand;
use App\Models\CustomerShop;
use App\Models\LoanSchedule;
use App\Models\ShopBrand;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CustomerRepository extends CustomRepository
{
    protected $model = Customer::class;

    public function _buildSelectForSearch()
    {

        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('first_name'),
            $this->modelField('last_name'),
            $this->modelField('first_name_kana'),
            $this->modelField('last_name_kana'),
            $this->modelField('birthday'),
            $this->modelField('tel1'),
            $this->modelField('tel2'),
            $this->modelField('tel3'),
            $this->modelField('ins_date'),
            $this->modelField('upd_date'),
            Application::field('status') . ' as application_status',
            Application::field('application_date'),
            Application::field('id') . ' as app_id',
            Brand::field('name') . ' as brand_name',
        ]);

        return $q;
    }

    public function _buildSearch($dataSearch, $q)
    {
        $keyword = data_get($dataSearch, 'keyword');
        $birthday = data_get($dataSearch, 'birthday');
        $tel = data_get($dataSearch, 'tel');
        $brandId = data_get($dataSearch, 'brand_id');
        $shopId = data_get($dataSearch, 'shop_id');
        $applicationDateFrom = data_get($dataSearch, 'application_date_from');
        $applicationDateTo = data_get($dataSearch, 'application_date_to');
        $contractDateFrom = data_get($dataSearch, 'contract_date_from');
        $contractDateTo = data_get($dataSearch, 'contract_date_to');
        $paymentStartMonthFrom = data_get($dataSearch, 'payment_start_month_from');
        $paymentStartMonthTo = data_get($dataSearch, 'payment_start_month_to');
        $contractCancelDateFrom = data_get($dataSearch, 'contract_cancel_date_from');
        $contractCancelDateTo = data_get($dataSearch, 'contract_cancel_date_to');
        $itemTypeId = data_get($dataSearch, 'item_type_id');
        $courseId = data_get($dataSearch, 'course_id');
        $status = data_get($dataSearch, 'status');


        //search
        $q->when(!blank($keyword), function ($q) use ($keyword) {
            $q->where('first_name', 'like', "%$keyword%")
                ->orWhere('last_name', 'like', "%$keyword%")
                ->orWhere('first_name_kana', 'like', "%$keyword%")
                ->orWhere('last_name_kana', 'like', "%$keyword%");
        })
        ->when($birthday && isValidDateFormat($birthday), function ($q) use ($birthday) {
            $birthday = Carbon::parse($birthday)->format('Y-m-d');
            $q->orWhere('birthday', $birthday);
        })
        ->when($tel, function ($q) use ($tel) {
            $q->orWhere(DB::raw("CONCAT(tel1, tel2, tel3)"), $tel);
        })
        //advanced search
        ->when($brandId, function ($q) use ($brandId) {
            $q->where(Application::field('brand_id'), $brandId);
        })
        ->when($shopId, function ($q) use ($shopId) {
            $q->where(Application::field('shop_id'), $shopId);
        })
        ->when($courseId, function ($q) use ($courseId) {
            $q->where(ApplicationCourse::field('course_id'), $courseId);
        })
        ->when($itemTypeId, function ($q) use ($itemTypeId) {
            $q->where(Course::field('item_type_id'), $itemTypeId);
        })

        //application_date
        ->when(!blank($applicationDateFrom) &&
            !blank($applicationDateTo) &&
            isValidDateFormat($applicationDateFrom) &&
            isValidDateFormat($applicationDateTo), function ($q) use ($applicationDateFrom, $applicationDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('application_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$applicationDateFrom, $applicationDateTo]);
            })
        ->when(!blank($applicationDateFrom) && isValidDateFormat($applicationDateFrom), function ($q) use ($applicationDateFrom) {
            $q->whereRaw("DATE_FORMAT(" . Application::field('application_date') . ", '%Y/%m/%d') >= ?", [$applicationDateFrom]);
        })
        ->when(!blank($applicationDateTo) && isValidDateFormat($applicationDateTo), function ($q) use ($applicationDateTo) {
            $q->whereRaw("DATE_FORMAT(" . Application::field('application_date') . ", '%Y/%m/%d') <= ?", [$applicationDateTo]);
        })

        //contract_date
        ->when(!blank($contractDateFrom) &&
            !blank($contractDateTo) &&
            isValidDateFormat($contractDateFrom) &&
            isValidDateFormat($contractDateTo), function ($q) use ($contractDateFrom, $contractDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('contract_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$contractDateFrom, $contractDateTo]);
            })
        ->when(!blank($contractDateFrom) && isValidDateFormat($contractDateFrom), function ($q) use ($contractDateFrom) {
            $q->whereRaw("DATE_FORMAT(" . Application::field('contract_date') . ", '%Y/%m/%d') >= ?", [$contractDateFrom]);
        })
        ->when(!blank($contractDateTo) && isValidDateFormat($contractDateTo), function ($q) use ($contractDateTo) {
            $q->whereRaw("DATE_FORMAT(" . Application::field('contract_date') . ", '%Y/%m/%d') <= ?", [$contractDateTo]);
        })

        //payment_start_month
        ->when(!blank($paymentStartMonthFrom) && !blank($paymentStartMonthTo), function ($q) use ($paymentStartMonthFrom, $paymentStartMonthTo) {
            $q->whereBetween(Application::field('payment_start_month'), [$paymentStartMonthFrom, $paymentStartMonthTo]);
        })
        ->when(!blank($paymentStartMonthFrom), function ($q) use ($paymentStartMonthFrom) {
            $q->where(Application::field('payment_start_month'), '>=', [$paymentStartMonthFrom]);
        })
        ->when(!blank($paymentStartMonthTo), function ($q) use ($paymentStartMonthTo) {
            $q->where(Application::field('payment_start_month'), '<=', [$paymentStartMonthTo]);
        })

        //contract_cancel_date
        ->when(!blank($contractCancelDateFrom) &&
            !blank($contractCancelDateTo) &&
            isValidDateFormat($contractCancelDateFrom) &&
            isValidDateFormat($contractCancelDateTo), function ($q) use ($contractCancelDateFrom, $contractCancelDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('contract_cancel_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$contractCancelDateFrom, $contractCancelDateTo]);
            })
        ->when(!blank($contractCancelDateFrom) && isValidDateFormat($contractCancelDateFrom), function ($q) use ($contractCancelDateFrom) {
            $q->whereRaw("DATE_FORMAT(" . Application::field('contract_cancel_date') . ", '%Y/%m/%d') >= ?", [$contractCancelDateFrom]);
        })
        ->when(!blank($contractCancelDateTo) && isValidDateFormat($contractCancelDateTo), function ($q) use ($contractCancelDateTo) {
            $q->whereRaw("DATE_FORMAT(" . Application::field('contract_cancel_date') . ", '%Y/%m/%d') <= ?", [$contractCancelDateTo]);
        })

        //status
        ->when(!empty($status) && array_filter($status, fn($item) => $item !== ''), function ($q) use ($status) {
            $q->whereIn(Application::field('status'), $status);
        });

        return $q;
    }

    public function getListForSearch($dataSearch, $perPage, $isCSV = false)
    {

        // select
        $q = $this->_buildSelectForSearch();
        $q->leftJoin(Application::getTableName(), $this->modelField('id'), Application::field('customer_id'))
            ->leftJoin(Brand::getTableName(), Brand::field('id'), Application::field('brand_id'))
            ->leftJoin(ApplicationCourse::getTableName(), Application::field('id'), ApplicationCourse::field('application_id'))
            ->leftJoin(Course::getTableName(), ApplicationCourse::field('course_id'), Course::field('id'));

        $q = $this->_buildSearch($dataSearch, $q);

        // if brand
        if (getCurrentUser()->isBrand()) {
            $shopIds = getCurrentUser()
            ->brands
            ->flatMap(function ($brand) {
                return $brand->shopBrands->pluck('shop_id');
            });

            $q->addSelect(CustomerBrand::field('brand_id'), CustomerShop::field('shop_id'))
                ->leftJoin(CustomerBrand::getTableName(), CustomerBrand::field('customer_id'), $this->modelField('id'))
                ->leftJoin(CustomerShop::getTableName(), CustomerShop::field('customer_id'), $this->modelField('id'))
                ->whereIn(CustomerBrand::field('brand_id'), getCurrentUser()->listBrandIds())
                ->orWhereIn(CustomerShop::field('shop_id'), $shopIds)
                ->groupBy([
                    $this->modelField('id'),
                    'app_id',
                ]);
        }

        // if shop
        if (getCurrentUser()->isStore()) {
            $q->addSelect(CustomerShop::field('shop_id'))
                ->leftJoin(CustomerShop::getTableName(), CustomerShop::field('customer_id'), $this->modelField('id'))
                ->whereIn(CustomerShop::field('shop_id'), getCurrentUser()->listShopIds())
                ->groupBy([
                    $this->modelField('id'),
                    'app_id',
                ]);
        }

        // orderBy
        $q->orderByRaw('COALESCE(applications.application_date, customers.ins_date) DESC');

        // end
        return !$isCSV ? $q->paginate($perPage) : $q->get();
    }

    public function getBasicInfoCustomer($id)
    {
        return $this->select([
            $this->modelField('id'),
            $this->modelField('first_name'),
            $this->modelField('last_name'),
            $this->modelField('first_name_kana'),
            $this->modelField('last_name_kana'),
            $this->modelField('birthday'),
            $this->modelField('tel1'),
            $this->modelField('tel2'),
            $this->modelField('tel3'),
            $this->modelField('sex'),
            $this->modelField('email'),
            $this->modelField('zip1'),
            $this->modelField('zip2'),
            $this->modelField('pref_id'),
            $this->modelField('address'),
            $this->modelField('city'),
            $this->modelField('building'),
            $this->modelField('contract_count'),
            $this->modelField('black_flag'),
            $this->modelField('ins_date'),
            $this->modelField('upd_date'),
        ])->with('applications')
            ->where('id', $id)
            ->first();
    }

    public function getListCustomerForSearch($dataSearch, $perPage = 20, $isCSV = false)
    {
        $last_name_kana = data_get($dataSearch, 'last_name_kana');
        $first_name_kana = data_get($dataSearch, 'first_name_kana');
        $birthday = data_get($dataSearch, 'birthday');
        $tel = preg_replace('/[-\s]/', '', data_get($dataSearch, 'tel'));

        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('first_name'),
            $this->modelField('last_name'),
            $this->modelField('first_name_kana'),
            $this->modelField('last_name_kana'),
            $this->modelField('birthday'),
            $this->modelField('tel1'),
            $this->modelField('tel2'),
            $this->modelField('tel3'),
        ]);

        $q->with(['applications' => fn($q) => $q->select('applications.*')]);

        $q->when(
            $last_name_kana,
            fn($q) =>
            $q->where('last_name_kana', 'like', "%{$last_name_kana}%")
        );

        $q->when(
            $first_name_kana,
            fn($q) =>
            $q->where('first_name_kana', 'like', "%{$first_name_kana}%")
        );

        $q->when(
            $birthday,
            fn($q) =>
            $q->whereDate('birthday', Carbon::parse($birthday)->format('Y-m-d'))
        );

        $q->when($tel, function ($q) use ($tel) {
            $q->whereRaw("CONCAT(tel1, tel2, tel3) LIKE ?", ["%{$tel}%"]);
        });

        return $q->paginate($perPage);
    }

    public function getBasicInfoApplicationCustomer($id)
    {
        return $this->select([
            $this->modelField('id'),
            $this->modelField('first_name'),
            $this->modelField('last_name'),
            $this->modelField('first_name_kana'),
            $this->modelField('last_name_kana'),
            $this->modelField('birthday'),
            $this->modelField('tel1'),
            $this->modelField('tel2'),
            $this->modelField('tel3'),
            $this->modelField('sex'),
            $this->modelField('email'),
            $this->modelField('zip1'),
            $this->modelField('zip2'),
            $this->modelField('pref_id'),
            $this->modelField('address'),
            $this->modelField('city'),
            $this->modelField('building'),
            $this->modelField('contract_count'),
            $this->modelField('black_flag'),
            $this->modelField('ins_date'),
            $this->modelField('upd_date'),
            $this->modelField('pref_kana_id'),
            $this->modelField('city_kana'),
            $this->modelField('address_kana'),
            $this->modelField('building_kana'),
        ])->with('applications')
            ->where('id', $id)
            ->first();
    }

    public function getInfoApplicationCustomer($id)
    {
        return $this->select(['*'])->with('applications')
            ->where('id', $id)
            ->first();
    }
}
