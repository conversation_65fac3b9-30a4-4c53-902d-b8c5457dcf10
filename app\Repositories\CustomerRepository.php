<?php

namespace App\Repositories;

use App\Models\Application;
use App\Models\ApplicationCourse;
use App\Models\Brand;
use App\Models\Course;
use App\Models\Customer;
use App\Models\CustomerBrand;
use App\Models\CustomerShop;
use App\Models\LoanSchedule;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CustomerRepository extends CustomRepository
{
    protected $model = Customer::class;

    public function getListForSearch($dataSearch, $perPage, $isCSV = false)
    {
        $keyword = data_get($dataSearch, 'keyword');
        $birthday = data_get($dataSearch, 'birthday');
        $tel = data_get($dataSearch, 'tel');
        $brandId = data_get($dataSearch, 'brand_id');
        $shopId = data_get($dataSearch, 'shop_id');
        $applicationDateFrom = data_get($dataSearch, 'application_date_from');
        $applicationDateTo = data_get($dataSearch, 'application_date_to');
        $contractDateFrom = data_get($dataSearch, 'contract_date_from');
        $contractDateTo = data_get($dataSearch, 'contract_date_to');
        $paymentStartMonthFrom = data_get($dataSearch, 'payment_start_month_from');
        $paymentStartMonthTo = data_get($dataSearch, 'payment_start_month_to');
        $contractCancelDateFrom = data_get($dataSearch, 'contract_cancel_date_from');
        $contractCancelDateTo = data_get($dataSearch, 'contract_cancel_date_to');
        $itemTypeId = data_get($dataSearch, 'item_type_id');
        $courseId = data_get($dataSearch, 'course_id');
        $status = data_get($dataSearch, 'status');

        if (!$isCSV) {
            $q = $this->select([
                $this->modelField('id'),
                $this->modelField('first_name'),
                $this->modelField('last_name'),
                $this->modelField('first_name_kana'),
                $this->modelField('last_name_kana'),
                $this->modelField('birthday'),
                $this->modelField('tel1'),
                $this->modelField('tel2'),
                $this->modelField('tel3'),
                Application::field('status') . ' as application_status',
                Application::field('application_date'),
                Brand::field('name') . ' as brand_name',
            ]);
        } else {
            $q = $this->select([
                $this->modelField('id'),
                $this->modelField('last_name'),
                $this->modelField('first_name'),
                $this->modelField('last_name_kana'),
                $this->modelField('first_name_kana'),
                $this->modelField('sex'),
                $this->modelField('birthday'),
                $this->modelField('email'),
                $this->modelField('tel1'),
                $this->modelField('tel2'),
                $this->modelField('tel3'),
                $this->modelField('zip1'),
                $this->modelField('zip2'),
                $this->modelField('city'),
                $this->modelField('address'),
                $this->modelField('building'),
                $this->modelField('pref_kana_id'),
                $this->modelField('city_kana'),
                $this->modelField('address_kana'),
                $this->modelField('building_kana'),
                $this->modelField('contract_count'),
                $this->modelField('black_flag'),
                $this->modelField('emergency_last_name'),
                $this->modelField('emergency_first_name'),
                $this->modelField('emergency_last_name_kana'),
                $this->modelField('emergency_first_name_kana'),
                $this->modelField('emergency_tel1'),
                $this->modelField('emergency_tel2'),
                $this->modelField('emergency_tel3'),
                $this->modelField('emergency_zip1'),
                $this->modelField('emergency_zip2'),
                $this->modelField('emergency_pref_id'),
                $this->modelField('emergency_city'),
                $this->modelField('emergency_address'),
                $this->modelField('emergency_building'),
                $this->modelField('relationship_flag'),
                $this->modelField('relationship_other'),
                $this->modelField('annual_income'),
                $this->modelField('company_name'),
                $this->modelField('company_name_kana'),
                $this->modelField('company_tel1'),
                $this->modelField('company_tel2'),
                $this->modelField('company_tel3'),
                $this->modelField('company_zip1'),
                $this->modelField('company_zip2'),
                $this->modelField('company_pref_id'),
                $this->modelField('company_city'),
                $this->modelField('company_address'),
                $this->modelField('company_building'),
                $this->modelField('information_input_flag'),
                $this->modelField('gw_last_name'),
                $this->modelField('gw_first_name'),
                $this->modelField('gw_last_name_kana'),
                $this->modelField('gw_first_name_kana'),
                $this->modelField('gw_sex'),
                $this->modelField('gw_birthday'),
                $this->modelField('gw_tel1'),
                $this->modelField('gw_tel2'),
                $this->modelField('gw_tel3'),
                $this->modelField('gw_zip1'),
                $this->modelField('gw_zip2'),
                $this->modelField('gw_pref_id'),
                $this->modelField('gw_city'),
                $this->modelField('gw_address'),
                $this->modelField('gw_building'),
                $this->modelField('gw_relationship_flag'),
                $this->modelField('gw_relationship_other'),
                $this->modelField('gw_company_name'),
                $this->modelField('gw_company_name_kana'),
                $this->modelField('gw_company_tel1'),
                $this->modelField('gw_company_tel2'),
                $this->modelField('gw_company_tel3'),
                $this->modelField('gw_company_zip1'),
                $this->modelField('gw_company_zip2'),
                $this->modelField('gw_company_pref_id'),
                $this->modelField('gw_company_city'),
                $this->modelField('gw_company_address'),
                $this->modelField('gw_company_building'),
                $this->modelField('bank_account_name'),
                $this->modelField('bank_account_name_kana'),
                $this->modelField('bank_flag'),
                $this->modelField('bank_account_mark1'),
                $this->modelField('bank_account_mark2'),
                $this->modelField('bank_account_mark3'),
                $this->modelField('bank_account_number'),
                $this->modelField('bank_code'),
                $this->modelField('bank_name'),
                $this->modelField('branch_code'),
                $this->modelField('branch_name'),
                $this->modelField('bank_account_type'),
                $this->modelField('contact_flag'),
                $this->modelField('contact_hope_date1'),
                $this->modelField('contact_hope_start_time1'),
                $this->modelField('contact_hope_end_time1'),
                $this->modelField('contact_hope_date2'),
                $this->modelField('contact_hope_start_time2'),
                $this->modelField('contact_hope_end_time2'),
                $this->modelField('ins_date'),
                $this->modelField('upd_date'),
            ]);
        }

        $q->leftJoin(Application::getTableName(), $this->modelField('id'), Application::field('customer_id'))
            ->leftJoin(Brand::getTableName(), Brand::field('id'), Application::field('brand_id'))
            ->leftJoin(ApplicationCourse::getTableName(), Application::field('id'), ApplicationCourse::field('application_id'))
            ->leftJoin(Course::getTableName(), ApplicationCourse::field('course_id'), Course::field('id'))
            ->when(!blank($keyword), function ($q) use ($keyword) {
                $q->where('first_name', 'like', "%$keyword%")
                    ->orWhere('last_name', 'like', "%$keyword%")
                    ->orWhere('first_name_kana', 'like', "%$keyword%")
                    ->orWhere('last_name_kana', 'like', "%$keyword%");
            })
            ->when($birthday, function ($q) use ($birthday) {
                $birthday = Carbon::parse($birthday)->format('Y-m-d');
                $q->orWhere('birthday', $birthday);
            })
            ->when($tel, function ($q) use ($tel) {
                $q->orWhere(DB::raw("CONCAT(tel1, tel2, tel3)"), $tel);
            })
            //advanced search
            ->when($brandId, function ($q) use ($brandId) {
                $q->where(Application::field('brand_id'), $brandId);
            })
            ->when($shopId, function ($q) use ($shopId) {
                $q->where(Application::field('shop_id'), $shopId);
            })
            ->when($courseId, function ($q) use ($courseId) {
                $q->where(ApplicationCourse::field('course_id'), $courseId);
            })
            ->when($itemTypeId, function ($q) use ($itemTypeId) {
                $q->where(Course::field('item_type_id'), $itemTypeId);
            })

            //application_date
            ->when(!blank($applicationDateFrom) && !blank($applicationDateTo), function ($q) use ($applicationDateFrom, $applicationDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('application_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$applicationDateFrom, $applicationDateTo]);
            })
            ->when(!blank($applicationDateFrom), function ($q) use ($applicationDateFrom) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('application_date') . ", '%Y/%m/%d') >= ?", [$applicationDateFrom]);
            })
            ->when(!blank($applicationDateTo), function ($q) use ($applicationDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('application_date') . ", '%Y/%m/%d') <= ?", [$applicationDateTo]);
            })

            //contract_date
            ->when(!blank($contractDateFrom) && !blank($contractDateTo), function ($q) use ($contractDateFrom, $contractDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('contract_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$contractDateFrom, $contractDateTo]);
            })
            ->when(!blank($contractDateFrom), function ($q) use ($contractDateFrom) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('contract_date') . ", '%Y/%m/%d') >= ?", [$contractDateFrom]);
            })
            ->when(!blank($contractDateTo), function ($q) use ($contractDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('contract_date') . ", '%Y/%m/%d') <= ?", [$contractDateTo]);
            })

            //payment_start_month
            ->when(!blank($paymentStartMonthFrom) && !blank($paymentStartMonthTo), function ($q) use ($paymentStartMonthFrom, $paymentStartMonthTo) {
                $q->whereBetween(Application::field('payment_start_month'), [$paymentStartMonthFrom, $paymentStartMonthTo]);
            })
            ->when(!blank($paymentStartMonthFrom), function ($q) use ($paymentStartMonthFrom) {
                $q->where(Application::field('payment_start_month'), '>=', [$paymentStartMonthFrom]);
            })
            ->when(!blank($paymentStartMonthTo), function ($q) use ($paymentStartMonthTo) {
                $q->where(Application::field('payment_start_month'), '<=', [$paymentStartMonthTo]);
            })

            //contract_cancel_date
            ->when(!blank($contractCancelDateFrom) && !blank($contractCancelDateTo), function ($q) use ($contractCancelDateFrom, $contractCancelDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('contract_cancel_date') . ", '%Y/%m/%d') BETWEEN ? AND ?", [$contractCancelDateFrom, $contractCancelDateTo]);
            })
            ->when(!blank($contractCancelDateFrom), function ($q) use ($contractCancelDateFrom) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('contract_cancel_date') . ", '%Y/%m/%d') >= ?", [$contractCancelDateFrom]);
            })
            ->when(!blank($contractCancelDateTo), function ($q) use ($contractCancelDateTo) {
                $q->whereRaw("DATE_FORMAT(" . Application::field('contract_cancel_date') . ", '%Y/%m/%d') <= ?", [$contractCancelDateTo]);
            })

            //status
            ->when(!empty($status) && array_filter($status, fn($item) => $item !== ''), function ($q) use ($status) {
                $q->whereIn(Application::field('status'), $status);
            })
            ->with(['pref']);

            if(!$isCSV){
                $q->orderBy(Application::field('application_date'), 'desc');
            } else {
                $q->orderBy($this->modelField('id'), 'desc');
            }

            $q->groupBy(
                $this->modelField('id'),
                $this->modelField('first_name'),
                $this->modelField('last_name'),
                $this->modelField('first_name_kana'),
                $this->modelField('last_name_kana'),
                $this->modelField('birthday'),
                $this->modelField('tel1'),
                $this->modelField('tel2'),
                $this->modelField('tel3'),
            );
        if (getCurrentUser()->isStore()) {
            $q->addSelect(CustomerShop::field('shop_id'))
                ->leftJoin(CustomerShop::getTableName(), CustomerShop::field('customer_id'), $this->modelField('id'))
                ->whereIn(CustomerShop::field('shop_id'), getCurrentUser()->listShopIds());
        }

        if (getCurrentUser()->isBrand()) {
            $q->addSelect(CustomerBrand::field('brand_id'))
                ->leftJoin(CustomerBrand::getTableName(), CustomerBrand::field('customer_id'), $this->modelField('id'))
                ->whereIn(CustomerBrand::field('brand_id'), getCurrentUser()->listBrandIds());
        }

        return !$isCSV ? $q->paginate($perPage) : $q->get();
    }

    public function getBasicInfoCustomer($id)
    {
        return $this->select([
            $this->modelField('id'),
            $this->modelField('first_name'),
            $this->modelField('last_name'),
            $this->modelField('first_name_kana'),
            $this->modelField('last_name_kana'),
            $this->modelField('birthday'),
            $this->modelField('tel1'),
            $this->modelField('tel2'),
            $this->modelField('tel3'),
            $this->modelField('sex'),
            $this->modelField('email'),
            $this->modelField('zip1'),
            $this->modelField('zip2'),
            $this->modelField('pref_id'),
            $this->modelField('address'),
            $this->modelField('city'),
            $this->modelField('building'),
            $this->modelField('contract_count'),
            $this->modelField('black_flag'),
            $this->modelField('ins_date'),
            $this->modelField('upd_date'),
        ])->with('applications')
            ->where('id', $id)
            ->first();
    }

    public function getListCustomerForSearch($dataSearch, $perPage = 20, $isCSV = false)
    {
        $last_name_kana = data_get($dataSearch, 'last_name_kana');
        $first_name_kana = data_get($dataSearch, 'first_name_kana');
        $birthday = data_get($dataSearch, 'birthday');
        $tel = preg_replace('/[-\s]/', '', data_get($dataSearch, 'tel'));

        $q = $this->select([
            $this->modelField('id'),
            $this->modelField('first_name'),
            $this->modelField('last_name'),
            $this->modelField('first_name_kana'),
            $this->modelField('last_name_kana'),
            $this->modelField('birthday'),
            $this->modelField('tel1'),
            $this->modelField('tel2'),
            $this->modelField('tel3'),
        ]);

        $q->with(['applications' => fn($q) => $q->select('applications.*')]);

        $q->when(
            $last_name_kana,
            fn($q) =>
            $q->where('last_name_kana', 'like', "%{$last_name_kana}%")
        );

        $q->when(
            $first_name_kana,
            fn($q) =>
            $q->where('first_name_kana', 'like', "%{$first_name_kana}%")
        );

        $q->when(
            $birthday,
            fn($q) =>
            $q->whereDate('birthday', Carbon::parse($birthday)->format('Y-m-d'))
        );

        $q->when($tel, function ($q) use ($tel) {
            $q->whereRaw("CONCAT(tel1, tel2, tel3) LIKE ?", ["%{$tel}%"]);
        });

        return $q->paginate($perPage);
    }

    public function getBasicInfoApplicationCustomer($id)
    {
        return $this->select([
            $this->modelField('id'),
            $this->modelField('first_name'),
            $this->modelField('last_name'),
            $this->modelField('first_name_kana'),
            $this->modelField('last_name_kana'),
            $this->modelField('birthday'),
            $this->modelField('tel1'),
            $this->modelField('tel2'),
            $this->modelField('tel3'),
            $this->modelField('sex'),
            $this->modelField('email'),
            $this->modelField('zip1'),
            $this->modelField('zip2'),
            $this->modelField('pref_id'),
            $this->modelField('address'),
            $this->modelField('city'),
            $this->modelField('building'),
            $this->modelField('contract_count'),
            $this->modelField('black_flag'),
            $this->modelField('ins_date'),
            $this->modelField('upd_date'),
            $this->modelField('pref_kana_id'),
            $this->modelField('city_kana'),
            $this->modelField('address_kana'),
            $this->modelField('building_kana'),
        ])->with('applications')
            ->where('id', $id)
            ->first();
    }

    public function getInfoApplicationCustomer ($id)
    {
        return $this->select(['*'])->with('applications')
            ->where('id', $id)
            ->first();
    }
}
