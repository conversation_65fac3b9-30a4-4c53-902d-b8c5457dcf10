<?php

use function Livewire\Volt\{state};
use function Livewire\Volt\{computed};
use App\Services\BrandService;
use App\Services\ShopService;
use App\Services\ItemTypeService;
use App\Services\CourseService;
state([
    'brand_id' => '',
    'shop_id' => '',
    'item_type_id' => '',
    'course_id' => '',
    'application_date_from' => '',
    'application_date_to' => '',
    'payment_start_month_from' => '',
    'payment_start_month_to' => '',
    'contract_date_from' => '',
    'contract_date_to' => '',
    'contract_cancel_date_from' => '',
    'contract_cancel_date_to' => '',
    'status' => [],

    'listBrands' => app(BrandService::class)->getAllBrands(),
    'listShops' => app(ShopService::class)->getAllShops(),
    'listItemTypes' => app(ItemTypeService::class)->getAllItemTypes(),
    'listCourses' => app(CourseService::class)->getAllCourses(),
]);

$search = function () {
    $this->dispatch('update-advanced-search', [
        'brand_id' => $this->brand_id,
        'shop_id' => $this->shop_id,
        'item_type_id' => $this->item_type_id,
        'course_id' => $this->course_id,
        'application_date_from' => $this->application_date_from,
        'application_date_to' => $this->application_date_to,
        'payment_start_month_from' => $this->payment_start_month_from,
        'payment_start_month_to' => $this->payment_start_month_to,
        'contract_date_from' => $this->contract_date_from,
        'contract_date_to' => $this->contract_date_to,
        'contract_cancel_date_from' => $this->contract_cancel_date_from,
        'contract_cancel_date_to' => $this->contract_cancel_date_to,
        'status' => $this->status,
    ]);
    $this->js("$('#settingSortList').modal('hide');");
};

$clearStatus = function () {
    if(in_array("", $this->status)){
        $this->status = [""];
    } else {
        $this->status = [];
    }
};

$clearNoneStatus = function () {
    if(in_array("", $this->status)){
        $this->status = array_values(array_diff($this->status, [""]));
    }
};

?>
@php
$applicationStatusText1 = \App\Enums\ApplicationStatusEnum::textList1();
$applicationStatusText2 = \App\Enums\ApplicationStatusEnum::textList2();
$applicationStatusText3 = \App\Enums\ApplicationStatusEnum::textList3();
$applicationStatusText4 = \App\Enums\ApplicationStatusEnum::textList4();
$applicationStatusColor = \App\Enums\ApplicationStatusEnum::colors();
@endphp
<div class="modal fade" id="settingSortList" tabindex="-1" aria-labelledby="settingSortListLabel" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ trans2('advanced_search.title') }}</h5>
            </div>
            <div class="modal-body">
                <form class="modal-form" role="form">
                    <div class="form-block border-bottom pt-3">
                        <div class="row">
                            <div class="form-group col-3">
                                <div class="form-label">{{ trans2('advanced_search.customers.brands') }}</div>
                                <div class="form-field" wire:ignore>
                                    <select name="" class="form-select2" style="width: 100%" data-placeholder="{{ trans2('advanced_search.not_choose') }}" wire:model="brand_id">
                                        <option value=""></option>
                                        @foreach ($listBrands as $brand)
                                            <option value="{{ data_get($brand,'id') }}">{{ data_get($brand,'name')}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group col-3">
                                <div class="form-label">{{ trans2('advanced_search.customers.shops') }}</div>
                                <div class="form-field" wire:ignore>
                                    <select name="" class="form-select2" style="width: 100%" data-placeholder="{{ trans2('advanced_search.not_choose') }}" wire:model="shop_id">
                                        <option value=""></option>
                                        @foreach ($listShops as $shop)
                                            <option value="{{ data_get($shop,'id') }}">{{ data_get($shop,'name')}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group col-3">
                                <div class="form-label">{{ trans2('advanced_search.customers.item_types') }}</div>
                                <div class="form-field" wire:ignore>
                                    <select name="" class="form-select2" style="width: 100%" data-placeholder="{{ trans2('advanced_search.not_choose') }}" wire:model="item_type_id">
                                        <option value=""></option>
                                        @foreach ($listItemTypes as $itemType)
                                            <option value="{{ data_get($itemType,'id') }}">{{ data_get($itemType,'name')}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="form-group col-3">
                                <div class="form-label">{{ trans2('advanced_search.customers.courses') }}</div>
                                <div class="form-field" wire:ignore>
                                    <select name="" class="form-select2" style="width: 100%" data-placeholder="{{ trans2('advanced_search.not_choose') }}" wire:model="course_id">
                                        <option value=""></option>
                                    @foreach ($listCourses as $course)
                                            <option value="{{ data_get($course,'id') }}">{{ data_get($course,'name_application') ?: data_get($course,'name_management')}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-block border-bottom">
                        <div class="row">
                            <div class="form-group col-6 pe-4">
                                <div class="form-label">{{ trans2('advanced_search.customers.application_date') }}</div>
                                <div class="form-field">
                                    <div class="d-flex align-items-center" wire:ignore>
                                        <input type="text" name="" value="" class="datepicker date-default datepicker-secondary flex-grow-1" readonly wire:model="application_date_from">
                                        <div class="mx-2">〜</div>
                                        <input type="text" name="" value="" class="datepicker date-default datepicker-secondary flex-grow-1" readonly wire:model="application_date_to">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-6 ps-4">
                                <div class="form-label">{{ trans2('advanced_search.customers.contract_date') }}</div>
                                <div class="form-field">
                                    <div class="d-flex align-items-center" wire:ignore>
                                        <input type="text" name="" value="" class="datepicker date-default datepicker-secondary flex-grow-1" readonly wire:model="contract_date_from">
                                        <div class="mx-2">〜</div>
                                        <input type="text" name="" value="" class="datepicker date-default datepicker-secondary flex-grow-1" readonly wire:model="contract_date_to">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-6 pe-4">
                                <div class="form-label">{{ trans2('advanced_search.customers.payment_start_month') }}</div>
                                <div class="form-field">
                                    <div class="d-flex align-items-center" wire:ignore>
                                        <input type="text" name="" value="" class="monthpicker datepicker-secondary flex-grow-1" wire:model="payment_start_month_from">
                                        <div class="mx-2">〜</div>
                                        <input type="text" name="" value="" class="monthpicker datepicker-secondary flex-grow-1" wire:model="payment_start_month_to">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-6 ps-4">
                                <div class="form-label">{{ trans2('advanced_search.customers.contract_cancel_date') }}</div>
                                <div class="form-field">
                                    <div class="d-flex align-items-center" wire:ignore>
                                        <input type="text" name="" value="" class="datepicker date-default datepicker-secondary flex-grow-1" readonly wire:model="contract_cancel_date_from">
                                        <div class="mx-2">〜</div>
                                        <input type="text" name="" value="" class="datepicker date-default datepicker-secondary flex-grow-1" readonly wire:model="contract_cancel_date_to">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-block">
                        <div class="form-group">
                            <div class="form-label">{{ trans2('advanced_search.customers.status') }}</div>
                            <div class="form-field">
                                <div class="row pt-2 justify-content-between">
                                    <div class="col-auto">
                                        <ul class="form-vertical-list">
                                            <li>
                                                <div class="form-check">
                                                    <label>
                                                        <input type="checkbox" name="" value="" class="form-check-input" data-check-group="modal-status" wire:model="status" wire:click="clearStatus">
                                                        <span class="form-check-text">{{ trans2('all') }}</span>
                                                    </label>
                                                </div>
                                            </li>
                                            @foreach ($applicationStatusText1 as $key => $text)
                                                <li>
                                                    <div class="form-check">
                                                        <label>
                                                            <input type="checkbox" name="" value="{{ $key }}" class="form-check-input all-check-item" data-check-group="modal-status" wire:model="status" wire:click="clearNoneStatus">
                                                            <span class="form-check-text"><i class="status-icon {{ $applicationStatusColor[$key] }}"></i>{{ $text }}</span>
                                                        </label>
                                                    </div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    <div class="col-auto">
                                        <ul class="form-vertical-list">
                                            @foreach ($applicationStatusText2 as $key => $text)
                                                <li>
                                                    <div class="form-check">
                                                        <label>
                                                            <input type="checkbox" name="" value="{{ $key }}" class="form-check-input all-check-item" data-check-group="modal-status" wire:model="status" wire:click="clearNoneStatus">
                                                            <span class="form-check-text"><i class="status-icon {{ $applicationStatusColor[$key] }}"></i>{{ $text }}</span>
                                                        </label>
                                                    </div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    <div class="col-auto">
                                        <ul class="form-vertical-list">
                                            @foreach ($applicationStatusText3 as $key => $text)
                                                <li>
                                                    <div class="form-check">
                                                        <label>
                                                            <input type="checkbox" name="" value="{{ $key }}" class="form-check-input all-check-item" data-check-group="modal-status" wire:model="status" wire:click="clearNoneStatus">
                                                            <span class="form-check-text"><i class="status-icon {{ $applicationStatusColor[$key] }}"></i>{{ $text }}</span>
                                                        </label>
                                                    </div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    <div class="col-auto">
                                        <ul class="form-vertical-list">
                                            @foreach ($applicationStatusText4 as $key => $text)
                                                <li>
                                                    <div class="form-check">
                                                        <label>
                                                            <input type="checkbox" name="" value="{{ $key }}" class="form-check-input all-check-item" data-check-group="modal-status" wire:model="status" wire:click="clearNoneStatus">
                                                            <span class="form-check-text"><i class="status-icon {{ $applicationStatusColor[$key] }}"></i>{{ $text }}</span>
                                                        </label>
                                                    </div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4 text-center">
                        <button wire:click.prevent="search" class="btn btn-dark btn-large">{{ trans2('advanced_search.search_button') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
