@extends('livewire.admin.customer.detail.index')
@section('detail-tab')
    @php
        $contractStatusText = \App\Enums\ContractStatusEnum::texts();
        $contractStatusColor = \App\Enums\ContractStatusEnum::colors();
    @endphp
    <div class="card mb-3">
        <div class="card-body">
            <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
                <table class="table table-borderless table-thead-bordered table-align-middle table-database"
                    id="dataListTable" data-link="">
                    <thead>
                        <tr>
                            <th class="text-nowrap" scope="col">{{ trans2('screens.customer.contract.index.contract_id') }}</th>
                            <th class="text-nowrap" scope="col">{{ trans2('screens.customer.contract.index.payment_company_flag') }}</th>
                            <th class="text-nowrap" scope="col">{{ trans2('screens.customer.contract.index.regist_number') }}</th>
                            <th class="text-nowrap" scope="col">{{ trans2('screens.customer.contract.index.contract_status') }}</th>
                            <th class="text-nowrap" scope="col">{{ trans2('screens.customer.contract.index.contract_date') }}</th>
                            <th class="text-nowrap" scope="col">{{ trans2('screens.customer.contract.index.contract_details') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($applications as $contract)
                            <tr wire:navigate
                                href="{{ getRoute('contract.details', ['id' => data_get($contract, 'id')]) }}">
                                <td class="text-nowrap">{{ data_get($contract, 'contract_id') }}</td>
                                <td class="text-nowrap">{{ data_get($contract, 'payment_company_flag.text') }}</td>
                                <td class="text-nowrap">{{ data_get($contract, 'regist_number') }}</td>
                                <td class="text-nowrap">
                                    <span
                                        class="badge badge-status rounded-pill badge-lg badge-{{ $contractStatusColor[data_get($contract, 'contract_status.value')] }}">{{ $contractStatusText[data_get($contract, 'contract_status.value')] }}
                                    </span>
                                </td>
                                <td class="text-nowrap">{{ data_get($contract, 'contract_date') }}</td>
                                <td class="text-break">
                                    @foreach ($contract->courses as $appCourse)
                                        <div>
                                            {{ joinSlash(
                                                data_get($contract, 'shopBrand.name'),
                                                data_get($appCourse, 'itemType.name'),
                                                data_get($appCourse, 'name_application'),
                                            ) }}
                                        </div>
                                    @endforeach
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
