@php
$storageFileServie = new \App\Services\FileStorageService();
@endphp
<div class="container-min">
    <h1 class="application-page-title">4.{{ trans2('screens.application.identification.title') }}</h1>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="align-top required" style="width:35%">{{ trans2('screens.application.identification.confirmation_doc_flag_label') }}<span class="required-icon">{{trans2('required')}}</span></th>
                <td class="align-top">
                    <ul class="form-vertical-list pt-3 pb-3">
                        <li>
                            <div class="form-radio">
                                <label>
                                    <input type="radio" wire:model="identificationForm.confirmation_doc_flag" name="certificate" value="1" class="form-radio-input form-disabled-control-doc-flag" data-disabled-cancel="false" data-disabled-target="other_certificate" checked>
                                    <span class="form-radio-text text-nowrap">{{ trans2('screens.application.identification.flag_driver_license') }}</span>
                                </label>
                            </div>
                        </li>
                        <li>
                            <div class="form-radio">
                                <label>
                                    <input type="radio" wire:model="identificationForm.confirmation_doc_flag" name="certificate" value="2" class="form-radio-input form-disabled-control-doc-flag" data-disabled-cancel="false" data-disabled-target="other_certificate">
                                    <span class="form-radio-text text-nowrap">{{ trans2('screens.application.identification.flag_driver_health_card') }}</span>
                                </label>
                            </div>
                        </li>
                        <li>
                            <div class="form-radio">
                                <label>
                                    <input type="radio" wire:model="identificationForm.confirmation_doc_flag" name="certificate" value="3" class="form-radio-input form-disabled-control-doc-flag" data-disabled-cancel="false" data-disabled-target="other_certificate">
                                    <span class="form-radio-text text-nowrap">{{ trans2('screens.application.identification.flag_driver_my_number_card') }}</span>
                                </label>
                            </div>
                        </li>
                        <li>
                            <div class="form-radio">
                                <label>
                                    <input type="radio" wire:model="identificationForm.confirmation_doc_flag" name="certificate" value="4" class="form-radio-input form-disabled-control-doc-flag" data-disabled-cancel="true" data-disabled-target="other_certificate">
                                    <span class="form-radio-text text-nowrap">{{ trans2('screens.application.identification.flag_other') }}</span>
                                </label>
                            </div>
                            <div class="ps-5 pt-2">
                                <input type="text" wire:model="identificationForm.confirmation_doc_other" name="" value="" class="form-control w-100 disabled-target-other_certificate"
                                    @disabled($identificationForm->confirmation_doc_flag != \App\Enums\ConfirmationDocFlagEnum::OTHER)>
                            </div>
                        </li>
                    </ul>
                    @error('identificationForm.confirmation_doc_flag')
                        <div>
                            <span class="message-error">{{ $message }}</span>
                        </div>
                    @enderror
                    @error('identificationForm.confirmation_doc_other')
                        <div>
                            <span class="message-error">{{ $message }}</span>
                        </div>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="align-top" style="width:35%">{{ trans2('screens.application.identification.file_upload_label') }}</th>
                <td class="align-top">
                    <div class="file-upload-area">
                        <div class="file-drop-zone">
                            <div>
                                <div class="drop-txt mb-2">{{ trans2('screens.application.identification.file_placeholder') }}</div>
                                <div class="text-center">
                                    <div class="me-2 d-inline align-middle">{{ trans2('screens.application.identification.file_label') }}</div>
                                    <label for="fileUploadInput_1" class="file-input-label btn btn-border">
                                        <input class="file-upload-input" id="fileUploadInput_1" type="file" wire:model="identificationForm.files" multiple />
                                        <span>{{ trans2('screens.application.identification.file_button') }}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        @error('identificationForm.files.0')
                            <div>
                                <span class="message-error">{{ $message }}</span>
                            </div>
                        @enderror
                    </div>
                    @if($application?->applicationFiles?->isNotEmpty())
                        <div class="mt-4 mb-2 fw-medium">{{ trans2('screens.application.identification.file_list') }}</div>
                        <table class="table table-border">
                            <thead>
                                <tr>
                                    <th scope="col">{{ trans2('screens.application.identification.file_name') }}</th>
                                    <th scope="col">{{ trans2('screens.application.identification.file_created') }}</th>
                                    <th scope="col"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($application?->applicationFiles as $key => $file)
                                    <tr>
                                        <td class="w-100">
                                            <a href="{{ $storageFileServie?->getFileUrl($file->file_url) }}" target="_blank" class="btn btn-text p-0 fc-link">{{ $file?->file_name }}</a>
                                        </td>
                                        <td class="text-nowrap">{{ $file?->ins_date ? \Carbon\Carbon::parse($file->ins_date)->format('Y/m/d') : '' }}</td>
                                        <td class="text-nowrap">
                                            <livewire:admin.application.identification.delete :$key :id="data_get($file, 'id')"/>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @endif
                </td>
            </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button class="btn btn-dark btn-large" wire:click.prevent="validateSave">{{ trans2('button.next') }}</button>
    </div>
</div>
