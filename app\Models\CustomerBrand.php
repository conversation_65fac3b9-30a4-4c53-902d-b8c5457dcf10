<?php

namespace App\Models;

use App\Models\Base\CustomModel;
use App\Models\Concerns\HasNameLang;
use App\Models\Presenters\PCustomerBrand;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerBrand extends CustomModel
{
    use PCustomerBrand;
    use HasNameLang;

    /**
     * The table associated with the model.
     *
     * @var string
     */

    protected $table = 'customer_brands';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'customer_id',
        'brand_id',
    ];
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class,'brand_id', 'id');
    }
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class,'customer_id', 'id');
    }

    public static function getFieldRelationshipMap() :array
    {
        return [
            'customer_id' => 'customer',
            'brand_id' => 'brand',
        ];
    }
}
