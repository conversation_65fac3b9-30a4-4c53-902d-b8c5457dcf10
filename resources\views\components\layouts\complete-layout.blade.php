<!DOCTYPE html>
<html lang="{!! getCurrentLangCode()!!}">
<head>
    @include('components.layouts.structures.head')
</head>
<body class="page-application">
    <!-- start::content -->
    <div class="application-wrapper">
        <div class="application-contents">
            <div class="container-fluid">
                @yield('content')
                {{ !empty($slot) ? $slot : '' }}
            </div>
        </div>
    </div>
    <!-- End #content -->

<livewire:common.confirm/>
<!-- Toast Messages -->
<livewire:common.toast-message/>

@livewireScripts

<!-- Event handle -->
@include('livewire.common.event-handle')
@include('components.layouts.structures.footer_js')
@stack('scripts')
</body>
</html> 