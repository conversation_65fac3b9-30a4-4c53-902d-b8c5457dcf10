<div class="container-min">
    <h1 class="application-page-title">3.{{ trans2('screens.application.service.title') }}</h1>
    <h2 class="heading-2">{{ trans2('screens.application.service.handover_start_date_heading') }}</h2>
    <table class="table table-edit">
        <tbody>
        <tr>
            <th class="required" style="width:38%">{{ trans2('screens.application.service.handover_start_date_label') }}<span class="required-icon">{{ trans2('required') }}</span></th>
            <td>
                <input type="text" wire:model="serviceForm.service_handover_date"
                       x-init="setTimeout(() => initializeDatepickers(), 100)"
                       name="" value="" class="form-control form-required datepicker w-px-200">
                @error('serviceForm.service_handover_date')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                @enderror
            </td>
        </tr>
        </tbody>
    </table>
    <h2 class="heading-2">{{ trans2('screens.application.service.service_provider_label') }}</h2>
    <table class="table table-edit">
        <tbody>
        <tr>
            <th class="required" style="width:38%">{{ trans2('screens.application.service.service_start_end_date') }}<span class="required-icon">{{ trans2('required') }}</span></th>
            <td>
                <div class="d-flex align-items-center">
                    <div class="w-50">
                        <input type="text" wire:model="serviceForm.service_start_date" name="service_handover_date"
                               x-init="setTimeout(() => initializeDatepickers(), 100)"
                               value="" class="form-control form-required datepicker w-100">
                    </div>
                    <div class="mx-3">〜</div>
                    <div class="w-50">
                        <input type="text" wire:model="serviceForm.service_end_date" name=""
                               x-init="setTimeout(() => initializeDatepickers(), 100)"
                               value="" class="form-control form-required datepicker w-100">
                    </div>
                </div>

                @if($errors->has('serviceForm.service_start_date') || $errors->has('serviceForm.service_end_date'))
                    <div>
                        <div>
                            @error('serviceForm.service_start_date')
                                <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                        <div>
                            @error('serviceForm.service_end_date')
                                <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                @endif
            </td>
        </tr>
        <tr>
            <th class="required" style="width:38%">{{ trans2('screens.application.service.service_count') }}<span class="required-icon">{{ trans2('required') }}</span></th>
            <td>
                <div class="form-parts-unit w-px-120">
                    <input type="text" wire:model="serviceForm.service_count" name="" value="" class="form-control form-required">
                    <div class="form-unit">{{ trans2('times') }}</div>
                </div>
                @error('serviceForm.service_count')
                    <div>
                        <span class="message-error">{{ $message }}</span>
                    </div>
                @enderror
            </td>
        </tr>
        <tr>
            <th class="required" style="width:38%">{{ trans2('screens.application.service.customer_flag_label') }}<span class="required-icon">{{ trans2('required') }}</span></th>
            <td>
                <div class="row align-items-center" x-data="{ customer_flag: @entangle('serviceForm.customer_flag') }">
                    <div class="col-auto">
                        <div class="form-radio">
                            <label>
                                <input type="radio" wire:model="serviceForm.customer_flag" x-model="customer_flag" name="customer" value="1" class="form-radio-input form-disabled-control-v2">
                                <span class="form-radio-text text-nowrap">{{ trans2('screens.application.service.customer_flag_herself') }}</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="flex-grow-1 d-flex align-items-center">
                            <div class="form-radio me-3 ms-4">
                                <label>
                                    <input type="radio" wire:model="serviceForm.customer_flag" x-model="customer_flag" name="customer" value="2" class="form-radio-input form-disabled-control-v2">
                                    <span class="form-radio-text text-nowrap">{{ trans2('screens.application.service.customer_flag_other') }}</span>
                                </label>
                            </div>
                            <input type="text" wire:model="serviceForm.customer_other" name="" value="" class="form-control flex-grow-1" :disabled="customer_flag != {{ \App\Enums\CustomerFlagEnum::OTHER }}">
                        </div>
                    </div>
                </div>
                @error('serviceForm.customer_other')
                <div>
                    <span class="message-error">{{ $message }}</span>
                </div>
                @enderror
            </td>
        </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button class="btn btn-dark btn-large" wire:click.prevent="validateSave">{{ trans2('button.next') }}</button>
    </div>
</div>

@push('scripts')
    <script>
        document.addEventListener('livewire:initialized', () => {
            setTimeout(() => {
                initializeDatepickers();
            }, 50);
        });

        function initializeDatepickers() {
            const childDataEl = document.getElementById('child-data');
            const selectedDatesRaw = childDataEl ? JSON.parse(childDataEl.dataset.selectedDates) : [];
            const selectedDates = selectedDatesRaw.map(item => item.holiday_date);

            document.querySelectorAll('.datepicker').forEach(function(el) {
                $(el).datepicker({
                    dateFormat: 'yy/mm/dd',
                    changeYear: true,
                    beforeShowDay: function(date) {
                        const d = $.datepicker.formatDate('yy/mm/dd', date);
                        const day = date.getDay();

                        if (selectedDates.includes(d)) {
                            return [true, 'selected-date', ''];
                        }

                        if(day === 5) {
                            return [true, ' ui-datepicker-week-end day-saturday', ''];
                        }

                        if(day === 6) {
                            return [true, ' ui-datepicker-week-end day-sunday', ''];
                        }

                        return [true, '', ''];

                    },
                    beforeShow: function(input, inst) {
                        addCustomNavButtons(inst);
                    },
                    onChangeMonthYear: function(year, month, inst) {
                        addCustomNavButtons(inst);
                    },
                }).on('change', function() {
                    const value = $(this).val();
                    const model = el.getAttribute('wire:model');
                    if (model) {
                        const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                        Livewire.find(componentId).set(model, value);
                    }
                });
            });
        }

        function addCustomNavButtons(inst) {
            setTimeout(function() {
                var buttonPanel = $(inst.dpDiv).find(".ui-datepicker-header");
                if (buttonPanel.find('.custom-nav-button').length === 0) {
                    var prevYearBtn = $('<a class="custom-nav-button prev-year" title="Prev Year"><span class="ui-icon">前の年へ</span></a>');
                    var nextYearBtn = $('<a class="custom-nav-button next-year" title="Next Year"><span class="ui-icon">次の年へ</span></a>');

                    prevYearBtn.click(function(e) {
                        $.datepicker._adjustDate(inst.input, -1, 'Y'); // 1年戻る
                        e.preventDefault();
                    });

                    nextYearBtn.click(function(e) {
                        $.datepicker._adjustDate(inst.input, +1, 'Y'); // 1年進む
                        e.preventDefault();
                    });

                    prevYearBtn.insertBefore(buttonPanel.find('.ui-datepicker-prev'));
                    nextYearBtn.insertAfter(buttonPanel.find('.ui-datepicker-next'));
                }
            }, 1);
        }
    </script>
@endpush
