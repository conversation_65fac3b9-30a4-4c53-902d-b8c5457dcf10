<?php

namespace App\Livewire\Admin\Payment;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;

class Index extends BaseAdminPageComponent
{
    public function __construct()
    {
        $this->page = SidebarMenuEnum::PAYMENT;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
    }

    public function render()
    {
        return $this->viewLivewireAdmin('payment.index');
    }
}
