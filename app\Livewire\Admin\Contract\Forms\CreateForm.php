<?php

namespace App\Livewire\Admin\Contract\Forms;

use App\Enums\BlackFlagEnum;
use App\Enums\SexEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Rules\TelValidationRule;
use App\Rules\ZipCodeValidationRule;
use Illuminate\Validation\Rule;

class CreateForm extends BaseAdminForm
{
    public $last_name = '';
    public $first_name = '';
    public $first_name_kana = '';
    public $last_name_kana = '';
    public $sex = '1';
    public $birthday = '';
    public $email = '';
    public $tel1 = '';
    public $tel2 = '';
    public $tel3 = '';
    public $zip1 = '';
    public $zip2 = '';
    public $pref_id = '';
    public $city = '';
    public $address = '';
    public $building = '';
    public $contract_count = '';
    public $black_flag = '';

    public function rules()
    {
        $rules = [
            'last_name' => 'required|string|max:128',
            'first_name' => 'required|string|max:128',
            'first_name_kana' => 'required|katakana|max:128',
            'last_name_kana' => 'required|katakana|max:128',
            'sex' => ['required', Rule::in(SexEnum::getValues())],
            'birthday' => 'required|date',
            'email' => 'nullable|email|max:128',
            'tel1' => ['required', 'max:32', new TelValidationRule()],
            'tel2' => ['required', 'max:32', new TelValidationRule()],
            'tel3' => ['required', 'max:32', new TelValidationRule()],
            'zip1' => ['required', 'max:4', new ZipCodeValidationRule()],
            'zip2' => ['required', 'max:4', new ZipCodeValidationRule()],
            'pref_id' => 'required|exists:prefs,id',
            'city' => 'required|string|max:1000',
            'address' => 'required|string|max:1000',
            'building' => 'nullable|string|max:1000',
            'contract_count' => 'nullable|integer|min:0|max:32767',
            'black_flag' => ['nullable', Rule::in(BlackFlagEnum::getValues())],
        ];

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.customer.attributes');
    }
}
