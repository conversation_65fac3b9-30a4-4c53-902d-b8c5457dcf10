<?php

namespace App\Livewire\Admin\Application\Customer;

use App\Enums\CustomerTabEnum;
use App\Livewire\Admin\Application\Forms\EmergencyForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Services\CustomerService;
use App\Services\PrefService;
use App\Services\ToastService;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use App\Traits\HandlesApplicationCustomer;

#[Layout('components.layouts.application-layout')]
class Emergency extends BaseAdminPageComponent
{
    use HandlesApplicationCustomer;

    public EmergencyForm $emergencyForm;

    public function mount($customer_id, $application_id)
    {
        $this->page = CustomerTabEnum::EMERGENCY;
        $this->pageTitle .= '｜' . trans2('project_name');
        $this->initCustomerAndApplication($customer_id, $application_id);

        if ($this->customer) {
            $this->emergencyForm->fillFromCustomer($this->customer);
        }
    }

    public function validateSave()
    {
        $this->emergencyForm->validate();
        $this->dispatch('app-emergency-store', $this->emergencyForm);
    }

    #[On('app-emergency-store')]
    public function store()
    {
        $params = $this->emergencyForm->all();

        $customer = app(CustomerService::class)->update($this->customerId, $params);

        if ($customer) {
            app(ToastService::class)->createSuccess();
            // redirect to tab work
            return redirect()->route('admin.customer.application.setup.work', [
                'application_id' => $this->applicationId,
                'customer_id' => $customer->id,
            ]);
        }

        app(ToastService::class)->createError();
    }

    public function render()
    {
        $this->dispatch('init-select2');
        return $this->viewLivewireAdmin('application.customer.emergency', [
            'listPrefs' => app(PrefService::class)->getAllPrefs(),
        ]);
    }
}
