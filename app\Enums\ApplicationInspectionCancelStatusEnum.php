<?php

namespace App\Enums;

class ApplicationInspectionCancelStatusEnum extends Enum
{
    const COOLING_OFF_REQUESTED = 1;
    const COOLING_OFF_APPROVED = 2;
    const STORE_CANCELLATION_REQUESTED = 3;
    const STORE_CANCELLATION_APPROVED = 4;

    public static function texts(): array
    {
        return [
            self::COOLING_OFF_REQUESTED => trans2('ApplicationInspectionCancelStatusEnum.COOLING_OFF_REQUESTED'),
            self::COOLING_OFF_APPROVED => trans2('ApplicationInspectionCancelStatusEnum.COOLING_OFF_APPROVED'),
            self::STORE_CANCELLATION_REQUESTED => trans2('ApplicationInspectionCancelStatusEnum.STORE_CANCELLATION_REQUESTED'),
            self::STORE_CANCELLATION_APPROVED => trans2('ApplicationInspectionCancelStatusEnum.STORE_CANCELLATION_APPROVED'),
        ];
    }
}
