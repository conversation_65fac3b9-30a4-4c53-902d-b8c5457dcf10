@php
    use \App\Enums\PaymentStatusEnum;
@endphp
<div>
    <table class="table table-deposit-details mb-5">
        <thead>
            <tr>
                <th class="title transparent"></th>
                <th colspan="2">{{ trans2('screens.payment.index.status.waiting_for_payment') }}</th>
                <th colspan="2">{{ trans2('screens.payment.index.status.payment_complete') }}</th>
                <th colspan="2">{{ trans2('screens.payment.index.status.unpaid') }}</th>
                <th colspan="2">{{ trans2('screens.payment.index.status.other') }}</th>
                <th colspan="2">{{ trans2('screens.payment.index.total') }}</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <th class="title transparent"></th>
                <th>{{ trans2('screens.payment.index.number') }}</th>
                <th>{{ trans2('screens.payment.index.amount') }}</th>
                <th>{{ trans2('screens.payment.index.number') }}</th>
                <th>{{ trans2('screens.payment.index.amount') }}</th>
                <th>{{ trans2('screens.payment.index.number') }}</th>
                <th>{{ trans2('screens.payment.index.amount') }}</th>
                <th>{{ trans2('screens.payment.index.number') }}</th>
                <th>{{ trans2('screens.payment.index.amount') }}</th>
                <th>{{ trans2('screens.payment.index.number') }}</th>
                <th>{{ trans2('screens.payment.index.amount') }}</th>
            </tr>
            @foreach ($dataList as $data)
                <tr>
                    <td class="title">{{ data_get($data, 'name') }}</td>
                    <td>
                        @if (!empty(data_get($data, 'waiting_count')))
                            <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js(PaymentStatusEnum::waitingValues()), {{ data_get($data, 'id') }})">{{ data_get($data, 'waiting_count') }}{{ trans2('subject') }}</a>
                        @else
                            {{ data_get($data, 'waiting_count') ?? 0 }}{{ trans2('subject') }}
                        @endif
                    </td>
                    <td><span class="me-1">¥</span>{{ formatNumber(data_get($data, 'waiting_total_amount')) }}</td>
                    <td>
                        @if (!empty(data_get($data, 'deposited_count')))
                            <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js(PaymentStatusEnum::depositedValues()), {{ data_get($data, 'id') }})">{{ data_get($data, 'deposited_count') }}{{ trans2('subject') }}</a>
                        @else
                            {{ data_get($data, 'deposited_count') ?? 0 }}{{ trans2('subject') }}
                        @endif
                    </td>
                    <td><span class="me-1">¥</span>{{ formatNumber(data_get($data, 'deposited_total_amount')) }}</td>
                    <td>
                        @if (!empty(data_get($data, 'unpaid_count')))
                            <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js(PaymentStatusEnum::unpaidValues()), {{ data_get($data, 'id') }})">{{ data_get($data, 'unpaid_count') }}{{ trans2('subject') }}</a>
                        @else
                            {{ data_get($data, 'unpaid_count') ?? 0 }}{{ trans2('subject') }}
                        @endif
                    </td>
                    <td><span class="me-1">¥</span>{{ formatNumber(data_get($data, 'unpaid_total_amount')) }}</td>
                    <td>
                        @if (!empty(data_get($data, 'other_count')))
                            <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js(PaymentStatusEnum::otherValues()), {{ data_get($data, 'id') }})">{{ data_get($data, 'other_count') }}{{ trans2('subject') }}</a>
                        @else
                            {{ data_get($data, 'other_count') ?? 0 }}{{ trans2('subject') }}
                        @endif
                    </td>
                    <td><span class="me-1">¥</span>{{ formatNumber(data_get($data, 'other_total_amount')) }}</td>
                    <td>
                        @if (!empty(data_get($data, 'grand_count')))
                            <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js([]),{{ data_get($data, 'id') }})">{{ data_get($data, 'grand_count') }}{{ trans2('subject') }}</a>
                        @else
                            {{ data_get($data, 'grand_count') ?? 0 }}{{ trans2('subject') }}
                        @endif
                    </td>
                    <td><span class="me-1">¥</span>{{ formatNumber(data_get($data, 'grand_total_amount')) }}</td>
                </tr>
            @endforeach
            <tr>
                <td class="title">{{ trans2('screens.payment.index.total') }}</td>
                <td>
                    @if (!empty($totals['waiting_count']))
                        <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js(PaymentStatusEnum::waitingValues()))">{{ $totals['waiting_count'] ?? 0 }}{{ trans2('subject') }}</a>
                    @else
                        {{ $totals['waiting_count'] ?? 0 }}{{ trans2('subject') }}
                    @endif
                </td>
                <td><span class="me-1">¥</span>{{ formatNumber($totals['waiting_total_amount'] ?? 0) }}</td>
                <td>
                    @if (!empty($totals['deposited_count']))
                        <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js(PaymentStatusEnum::depositedValues()))">{{ $totals['deposited_count'] ?? 0 }}{{ trans2('subject') }}</a>
                    @else
                        {{ $totals['deposited_count'] ?? 0 }}{{ trans2('subject') }}
                    @endif
                </td>
                <td><span class="me-1">¥</span>{{ formatNumber($totals['deposited_total_amount'] ?? 0) }}</td>
                <td>
                    @if (!empty($totals['unpaid_count']))
                        <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js(PaymentStatusEnum::unpaidValues()))">{{ $totals['unpaid_count'] ?? 0 }}{{ trans2('subject') }}</a>
                    @else
                        {{ $totals['unpaid_count'] ?? 0 }}{{ trans2('subject') }}
                    @endif
                </td>
                <td><span class="me-1">¥</span>{{ formatNumber($totals['unpaid_total_amount'] ?? 0) }}</td>
                <td>
                    @if (!empty($totals['other_count']))
                        <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList(@js(PaymentStatusEnum::otherValues()))">{{ $totals['other_count'] ?? 0 }}{{ trans2('subject') }}</a>
                    @else
                        {{ $totals['other_count'] ?? 0 }}{{ trans2('subject') }}
                    @endif
                </td>
                <td><span class="me-1">¥</span>{{ formatNumber($totals['other_total_amount'] ?? 0) }}</td>
                <td>
                    @if (!empty($totals['grand_count']))
                        <a id="section-payment" class="cursor-pointer" wire:click.prevent="searchDataList()">{{ $totals['grand_count'] ?? 0 }}{{ trans2('subject') }}</a>
                    @else
                        {{ $totals['grand_count'] ?? 0 }}{{ trans2('subject') }}
                    @endif
                </td>
                <td><span class="me-1">¥</span>{{ formatNumber($totals['grand_total_amount'] ?? 0) }}</td>
            </tr>
        </tbody>
    </table>
</div>
