@push('scripts')
    <script src="{{ asset('assets/admin/js/page/customer/postcode.js') }}"></script>
    <script>
        window.addEventListener('init-select2', function () {
            setTimeout(() => {
                initSelect2Binding();
            }, 50);
        });

        function initSelect2Binding() {

            document.querySelectorAll('select.form-select2').forEach(function (el) {

                if (!$(el).hasClass('select2-hidden-accessible')) {
                    $(el).select2().on('change', function (e) {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');

                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                        }
                    });
                }
            });
        }

        document.addEventListener('livewire:initialized', () => {
            initSelect2Binding();
        });
    </script>

    <script>
        document.addEventListener('livewire:initialized', () => {
            setTimeout(() => {
                initializeDatepickers();
            }, 50);
        });

        function initializeDatepickers() {
            document.querySelectorAll('.datepicker').forEach(function(el) {
                const currentYear = new Date().getFullYear();
                const minYear = currentYear - 200

                const childDataEl = document.getElementById('selected-date');
                const selectedDatesRaw = childDataEl ? JSON.parse(childDataEl.dataset.selectedDates) : [];
                const selectedDates = selectedDatesRaw.map(item => item.holiday_date);

                $(el).datepicker({
                    dateFormat: 'yy/mm/dd',
                    yearRange: `${minYear}:${currentYear}`,
                    minDate: new Date(minYear, 0, 1),
                    maxDate: new Date(),
                    beforeShowDay: function(date) {
                        const d = $.datepicker.formatDate('yy/mm/dd', date);
                        const day = date.getDay();

                        if (selectedDates.includes(d)) {
                            return [true, 'selected-date', ''];
                        }

                        if(day === 5) {
                            return [true, ' ui-datepicker-week-end day-saturday', ''];
                        }

                        if(day === 6) {
                            return [true, ' ui-datepicker-week-end day-sunday', ''];
                        }

                        return [true, '', ''];

                    },
                    beforeShow: function(input, inst) {
                        addCustomNavButtons(inst);
                    },
                    onChangeMonthYear: function(year, month, inst) {
                        addCustomNavButtons(inst);
                    },
                }).on('change', function() {
                    const value = $(this).val();
                    const model = el.getAttribute('wire:model');
                    if (model) {
                        const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                        Livewire.find(componentId).set(model, value);
                    }
                });
            });
        }

        function addCustomNavButtons(inst) {
            setTimeout(function() {
            var buttonPanel = $(inst.dpDiv).find(".ui-datepicker-header");
            if (buttonPanel.find('.custom-nav-button').length === 0) {
                var prevYearBtn = $('<a class="custom-nav-button prev-year" title="Prev Year"><span class="ui-icon">前の年へ</span></a>');
                var nextYearBtn = $('<a class="custom-nav-button next-year" title="Next Year"><span class="ui-icon">次の年へ</span></a>');

                prevYearBtn.click(function(e) {
                $.datepicker._adjustDate(inst.input, -1, 'Y'); // 1年戻る
                e.preventDefault();
                });

                nextYearBtn.click(function(e) {
                $.datepicker._adjustDate(inst.input, +1, 'Y'); // 1年進む
                e.preventDefault();
                });

                prevYearBtn.insertBefore(buttonPanel.find('.ui-datepicker-prev'));
                nextYearBtn.insertAfter(buttonPanel.find('.ui-datepicker-next'));
            }
            }, 1);
        }
    </script>

    <script>
        const zip1FormField = document.querySelector('input[name=zip1]');
        const zip2FormField = document.querySelector('input[name=zip2]');
        const addressFormField = document.querySelector('input[name=address]');
        const cityFormField = document.querySelector('input[name=city]');
        const prefFormField = document.querySelector('select[name=pref_id]');

        const onChangeZipCode = () => {
            let postcode = zip1FormField?.value + zip2FormField?.value;

            getAddressFromPostcode(postcode)
                .then(addressData => {
                    let prefCode = addressData.data?.prefCode ?? '';
                    let city = addressData.data?.city ?? '';
                    let address = addressData.data?.town ?? '';

                    // set data for form
                    cityFormField.value = city;
                    addressFormField.value = address;
                    prefFormField.value = parseInt(prefCode);
                    $(prefFormField).trigger('change');

                    // Update Livewire model
                    const prefModel = prefFormField.getAttribute('wire:model');
                    const cityModel = cityFormField.getAttribute('wire:model');
                    const addressModel = addressFormField.getAttribute('wire:model');
                    const componentId = cityFormField.closest('[wire\\:id]').getAttribute('wire:id');
                    if (cityModel) {
                        Livewire.find(componentId).set(cityModel, city);
                    }
                    if (addressModel) {
                        Livewire.find(componentId).set(addressModel, address);
                    }
                    if (prefModel) {
                        Livewire.find(componentId).set(prefModel, parseInt(prefCode));
                    }
                });
        }

        zip1FormField.addEventListener('change', onChangeZipCode);
        zip2FormField.addEventListener('change', onChangeZipCode);
    </script>
@endpush
