{{-- data fill --}}
<div class="fill-data">
    @php
        $customer = $application?->customer;
        $customerRelationshipFlag = \App\Enums\RelationshipFlagEnum::texts();
        $customerSexText = \App\Enums\SexEnum::texts();
    @endphp
        <!-- application_date -->
    <div class="data" style="
                top: 28px;
                left: 73px;
                font-size: 10px;
            ">{{ $application?->getApplicationDatePDF() }}</div>

    <!-- last_name_kana -->
    <div class="data" style="
                top: 117px;
                left: 64px;
                font-size: 9px;
                width: 251px;
                height: 18px;
            ">{{ $customer?->last_name_kana }}</div>

    <!-- first_name_kana -->
    <div class="data" style="
                top: 117px;
                left: 320px;
                font-size: 9px;
                width: 251px;
                height: 18px;
            ">{{ $customer?->first_name_kana }}</div>

    <!-- first_name -->
    <div class="data" style="
                top: 139px;
                left: 64px;
                font-size: 12px;
                width: 251px;
                height: 36px;
            ">{{ $customer?->first_name }}</div>

    <!-- last_name -->
    <div class="data" style="
                top: 139px;
                left: 320px;
                font-size: 12px;
                width: 251px;
                height: 36px;
            ">{{ $customer?->last_name }}</div>

    <!-- customers.sex -->
    <div class="data" style="
                    top: 180px;
                    left: 128px;
                    width: 251px;
                    height: 36px;
                    font-size: 14px
            ">{{ $customer?->sex?->text }}</div>

    <!-- customers.birthday_day -->
    <div class="data" style="
                    top: 180px;
                    left: 330px;
                    width: 251px;
                    height: 36px;
                    font-size: 14px
            ">{{ $customer?->birthday ? \Carbon\Carbon::parse($customer?->birthday)->format('Y/m/d') : '' }}</div>

    <!-- prefs.name_kana + city_kana + address_kana + building_kana -->
    <div class="data" style="
                    top: 216px;
                    left: 120px;
                    width: 251px;
                    height: 18px;
                    font-size: 9px;
            ">{{ $customer?->prefKana?->name_kana }} {{ $customer->city_kana }} {{ $customer->address_kana }} {{ $customer->building_kana }}</div>

    <!-- customers.zip1 - customers.zip2 -->
    <!-- prefs.name + city + address + building -->
    <div class="data" style="
                    top: 230px;
                    left: 88px;
                    width: 251px;
                    height: 18px;
                    font-size: 12px;
            ">{{ $customer?->zip1 }}-{{ $customer?->zip2 }}</div>
    <div class="data" style="
                    top: 245px;
                    left: 88px;
                    width: 251px;
                    height: 18px;
                    font-size: 9px;
            ">{{ $customer?->pref?->name }} {{ $customer?->city }} {{ $customer?->address }} {{ $customer?->building }}</div>

    <!-- customers.tel1 - customers.tel2 -->
    <div class="data" style="
                    top: 268px;
                    left: 90px;
                    width: 251px;
                    height: 18px;
                    font-size: 12px;
                    justify-content: center;
            ">{{ $customer?->tel1 }}-{{ $customer?->tel2 }}-{{ $customer?->tel3 }}</div>
    <!-- customers.annual_income -->
    <div class="data" style="
                        top: 275px;
                        left: 510px;
                        width: 251px;
                        height: 18px;
                        font-size: 12px;
            ">{{ $customer?->annual_income }}</div>

    <!-- customers.company_name_kana -->
    <!-- customers.company_name -->
    <div class="data" style="
                            top: 295px;
                            left: 130px;
                            width: 251px;
                            height: 10px;
                            font-size: 9px;
            ">{{ $customer?->company_name_kana }}</div>
    <div class="data" style="
                            top: 307px;
                            left: 130px;
                            width: 251px;
                            height: 20px;
                            font-size: 9px;
            ">{{ $customer?->company_name }}</div>

    <!-- customers.company_tel1 company_tel2 company_tel3 -->
    <div class="data" style="
                            top: 293px;
                            left: 421px;
                            width: 149px;
                            height: 35px;
                            font-size: 12px;
                            justify-content: center;
            ">{{ $customer?->company_tel1 }}-{{ $customer?->company_tel2 }}-{{ $customer?->company_tel3 }}</div>

    <!-- customers.company_zip1 company_zip2 -->
    <!-- prefs.name + company_city + company_address + company_building -->
    <div class="data" style="
                                top: 332px;
                        left: 106px;
                        width: 251px;
                        height: 10px;
                        font-size: 9px;
            ">{{ $customer?->company_zip1 }}-{{ $customer?->company_zip2 }}</div>
    <div class="data" style="
                           top: 340px;
                            left: 106px;
                            width: 251px;
                            height: 20px;
                            font-size: 9px;
            ">{{ $customer?->companyPref?->name }} {{ $customer?->company_city }} {{ $customer?->company_address }} {{ $customer?->company_building }}</div>

    <!-- customers.emergency_last_name_kana + " " + customers.emergency_first_name_kana -->
    <!-- customers.emergency_last_name + " " + customers.emergency_first_name -->
    <!-- customers.relationship_flag -->
    <div class="data" style="
                           top: 356px;
                            left: 103px;
                            width: 251px;
                            height: 20px;
                            font-size: 9px;
            ">{{ $customer?->emergency_last_name_kana }} {{ $customer?->emergency_first_name_kana }}</div>
    <div class="data" style="
                               top: 373px;
                        left: 63px;
                        width: 417px;
                        height: 30px;
                        font-size: 9px;
                        justify-content: center;
            ">{{ $customer?->emergency_last_name }} {{ $customer?->emergency_first_name }}</div>
    <div class="data" style="
                        top: 373px;
                        left: 483px;
                        width: 87px;
                        height: 30px;
                        font-size: 9px;
                        justify-content: center;
            ">
        {{ $customer?->relationship_flag?->value === \App\Enums\RelationshipFlagEnum::OTHER ? $customer?->relationship_other : $customer?->relationship_flag?->text }}
    </div>

    <!-- customers.emergency_zip1 + "-" + emergency_zip2 -->
    <!-- prefs.name + emergency_city + emergency_address + emergency_building -->
    <!-- customers.emergency_tel1 + "-" + emergency_tel2 + "-" + emergency_tel3 -->
    <div class="data" style="
                    top: 396px;
                    left: 78px;
                    width: 332px;
                    height: 30px;
                    font-size: 12px;
            ">{{ $customer?->emergency_zip1 }}-{{ $customer?->emergency_zip2 }}</div>
    <div class="data" style="
                    top: 409px;
                    left: 78px;
                    width: 332px;
                    height: 30px;
                    font-size: 9px;
            ">{{ $customer?->emergencyPref?->name }} {{ $customer?->emergency_city }} {{ $customer?->emergency_address }} {{ $customer?->emergency_building }}</div>
    <div class="data" style="
                    top: 425px;
                    left: 456px;
                    width: 114px;
                    height: 20px;
                    font-size: 12px;
                    justify-content: center;
            ">{{ $customer?->emergency_tel1 }}-{{ $customer?->emergency_tel2 }}-{{ $customer?->emergency_tel3 }}</div>

    <!-- customers.gw_last_name_kana + " " + customers.gw_first_name_kana -->
    <!-- customers.gw_last_name + " " + customers.gw_first_name -->
    <!-- gw_relationship_flag -->
    <div class="data" style="
                    top: 446px;
                    left: 102px;
                    width: 375px;
                    height: 11px;
                    font-size: 9px;
                ">{{ $customer?->gw_last_name_kana }}</div>
    <div class="data" style="
                    top: 457px;
                    left: 67px;
                    width: 409px;
                    height: 31px;
                    font-size: 9px;
                ">{{ $customer?->gw_first_name_kana }}</div>
    <div class="data" style="
                    top: 457px;
                    left: 484px;
                    width: 86px;
                    height: 32px;
                    font-size: 9px;
                    justify-content: center;
                ">
        @if($application?->customer?->gw_relationship_flag === \App\Enums\RelationshipFlagEnum::OTHER)
            {{ $application?->customer?->gw_relationship_other }}
        @else
            {{ $customerRelationshipFlag[$application?->customer?->gw_relationship_flag] ?? '' }}
        @endif
    </div>

    <!-- gw_sex -->
    <!-- gw_birthday -->
    <div class="data" style="
                    top: 489px;
                    left: 84px;
                    width: 86px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: center;
                ">{{ $customerSexText[$application?->customer?->gw_sex] ?? '' }}</div>
    <div class="data" style="
                    top: 489px;
                    left: 229px;
                    width: 339px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: center;
                ">{{ $customer?->gw_birthday ? \Carbon\Carbon::parse($customer?->birthday)->format('Y/m/d') : '' }}</div>

    <!-- GW_zip1 gw_zip2 -->
    <!-- prefs.name + gw_city + gw_address + gw_building -->
    <div class="data" style="
                        top: 522px;
                        left: 80px;
                        width: 265px;
                        height: 12px;
                        font-size: 12px;
                ">{{ $customer?->gw_zip1 }}-{{ $customer?->gw_zip2 }}</div>
    <div class="data" style="
                        top: 532px;
                        left: 80px;
                        width: 487px;
                        height: 24px;
                        font-size: 9px;
                ">{{ $customer?->gwPref?->name }} {{ $customer?->gw_city }} {{ $customer?->gw_address }} {{ $customer?->gw_building }}</div>

    <!-- customers.gw_tel1 + "-" + gw_tel2 + "-" + gw_tel3 -->
    <div class="data" style="
                        top: 559px;
                        left: 99px;
                        width: 487px;
                        height: 24px;
                        font-size: 12px;
                ">{{ $customer?->gw_tel1 }}-{{ $customer?->gw_tel2 }}-{{ $customer?->gw_tel3 }}</div>

    <!-- gw_company_name -->
    <!-- customers.gw_company_tel1 + "-" + gw_company_tel2 + "-" + gw_company_tel3 -->
    <!-- customers.gw_company_zip1 + "-" + gw_company_zip2 -->
    <!-- prefs.name + gw_company_city + gw_company_address + gw_company_building -->
    <div class="data" style="
                        top: 589px;
                        left: 81px;
                        width: 321px;
                        height: 26px;
                        font-size: 9px;
                ">{{ $customer?->gw_company_name }}</div>
    <div class="data" style="
                        top: 587px;
                        left: 421px;
                        width: 149px;
                        height: 29px;
                        font-size: 12px;
                        justify-content: center;
                ">{{ $customer?->gw_company_tel1 }}-{{ $customer?->gw_company_tel2 }}
        -{{ $customer?->gw_company_tel3 }}</div>
    <div class="data" style="
                        top: 617px;
                        left: 99px;
                        width: 467px;
                        height: 12px;
                        font-size: 12px;
                ">{{ $customer?->gw_company_zip1 }}-{{ $customer?->gw_company_zip2 }}</div>
    <div class="data" style="
                        top: 627px;
                        left: 99px;
                        width: 471px;
                        height: 22px;
                        font-size: 9px;
                ">{{ $customer?->gwCompanyPref?->name }} {{ $customer?->gw_company_city }} {{ $customer?->gw_company_address }} {{ $customer?->gw_company_building }}</div>

    <!-- bank_account_mark1 -->
    <!-- bank_account_mark2 -->
    <!-- custmers.bank_code -->
    <!-- custmers.branch_code -->
    <!-- custmers.bank_name -->
    <!-- custmers.branch_name -->
    <!-- custmers.bank_account_type -->
    <!-- custmers.bank_account_number -->
    <!-- custmers.bank_account_name_kana -->
    <!-- custmers.bank_account_name -->
    <div class="data" style="
                    top: 708px;
                    left: 50px;
                    width: 50px;
                    height: 57px;
                    font-size: 12px;
                    justify-content: center;
                ">{{ $customer?->bank_account_mark1 }}</div>
    <div class="data" style="
                    top: 708px;
                    left: 140px;
                    width: 140px;
                    height: 57px;
                    font-size: 12px;
                    justify-content: center;
                ">{{ $customer?->bank_account_mark2 }}</div>
    <div class="data" style="
                    top: 680px;
                    left: 359px;
                    width: 69px;
                    height: 13px;
                    font-size: 9px;
                    justify-content: center;
                ">{{ $customer?->bank_code }}</div>
    <div class="data" style="
                    top: 680px;
                    left: 517px;
                    width: 53px;
                    height: 13px;
                    font-size: 9px;
                    justify-content: center;
                ">{{ $customer?->branch_code }}</div>
    <div class="data" style="
                    top: 694px;
                    left: 286px;
                    width: 143px;
                    height: 41px;
                    font-size: 12px;
                    justify-content: center;
                ">{{ $customer?->bank_name }}</div>
    <div class="data" style="
                        top: 694px;
                        left: 428px;
                        width: 143px;
                        height: 41px;
                        font-size: 12px;
                        justify-content: center;
                ">{{ $customer?->branch_name }}</div>

    <div class="data" style="
                            top: 748px;
                        left: 286px;
                        width: 143px;
                        height: 20px;
                        font-size: 9px;
                        justify-content: center;
                ">{{ $customer?->bank_account_type?->text }}</div>
    <div class="data" style="
                       top: 748px;
                        left: 428px;
                        width: 143px;
                        height: 20px;
                        font-size: 9px;
                        justify-content: center;
                ">{{ $customer?->bank_account_number }}</div>

    <div class="data" style="
                       top: 769px;
                        left: 65px;
                        width: 363px;
                        height: 20px;
                        font-size: 9px;
                        justify-content: center;
                ">{{ $customer?->bank_account_name_kana }}</div>
    <div class="data" style="
                       top: 790px;
                        left: 65px;
                        width: 363px;
                        height: 58px;
                        font-size: 12px;
                        justify-content: center;
                ">{{ $customer?->bank_account_name }}</div>

    <!-- Col 2 -->
    <!-- application_date -->
    <div class="data" style="
                    top: 115px;
                    left: 755px;
                    width: 183px;
                    height: 17px;
                    font-size: 10px;
                    justify-content: center;
                ">{{ $application?->getApplicationDatePDF() }}</div>

    <!-- courses mapping -->
    @foreach($apps as $idx => $course)
        @if($idx === 0)
            <!-- item 1 -->
            <div>
                <!-- name_application -->
                <div class="data" style="
                                top: 157px;
                                left: 578px;
                                width: 166px;
                                height: 39px;
                                font-size: 10px;
                                justify-content: center;
                                ">{{ $course?->course?->name_application ?? $course?->course?->name_management }}</div>
                <!-- courses.count -->
                <div class="data" style="
                                    top: 157px;
                                    left: 746px;
                                    width: 24px;
                                    height: 39px;
                                    font-size: 12px;
                                    justify-content: center;
                                ">{{ $course->count }}</div>
                <!-- amount -->
                <div class="data" style="
                                    top: 157px;
                                    left: 770px;
                                    width: 166px;
                                    height: 39px;
                                    font-size: 12px;
                                    justify-content: end;
                                ">{{ $course?->amount ? number_format($course->amount) : '' }}</div>
            </div>
        @endif

        @if($idx === 1)
            <!-- item 2 -->
            <div>
                <!-- name_application -->
                <div class="data" style="
                            top: 198px;
                            left: 578px;
                            width: 166px;
                            height: 39px;
                            font-size: 10px;
                            justify-content: center;
                            ">{{ $course?->course?->name_application ?? $course?->course?->name_management }}</div>
                <!-- courses.count -->
                <div class="data" style="
                                top: 198px;
                                left: 746px;
                                width: 24px;
                                height: 39px;
                                font-size: 12px;
                                justify-content: center;
                            ">{{ $course->count }}</div>
                <!-- amount -->
                <div class="data" style="
                                top: 198px;
                                left: 770px;
                                width: 166px;
                                height: 39px;
                                font-size: 12px;
                                justify-content: end;
                            ">{{ $course?->amount ? number_format($course->amount) : '' }}</div>
            </div>
        @endif
        @if($idx === 2)
            <!-- item 3 -->
            <div>
                <!-- name_application -->
                <div class="data" style="
                                top: 238px;
                                left: 578px;
                                width: 166px;
                                height: 39px;
                                font-size: 10px;
                                justify-content: center;
                                ">{{ $course?->course?->name_application ?? $course?->course?->name_management }}</div>
                <!-- courses.count -->
                <div class="data" style="
                                    top: 238px;
                                    left: 746px;
                                    width: 24px;
                                    height: 39px;
                                    font-size: 12px;
                                    justify-content: center;
                                ">{{ $course->count }}</div>
                <!-- amount -->
                <div class="data" style="
                                    top: 238px;
                                    left: 770px;
                                    width: 166px;
                                    height: 39px;
                                    font-size: 12px;
                                    justify-content: end;
                                ">{{ $course?->amount ? number_format($course->amount) : '' }}</div>
            </div>
        @endif
        @if($idx === 3)
            <!-- item 4 -->
            <div>
                <!-- name_application -->
                <div class="data" style="
                                top: 278px;
                                left: 578px;
                                width: 166px;
                                height: 39px;
                                font-size: 10px;
                                justify-content: center;
                                ">{{ $course?->course?->name_application ?? $course?->course?->name_management }}</div>
                <!-- courses.count -->
                <div class="data" style="
                                    top: 278px;
                                    left: 746px;
                                    width: 24px;
                                    height: 39px;
                                    font-size: 12px;
                                    justify-content: center;
                                ">{{ $course->count }}</div>
                <!-- amount -->
                <div class="data" style="
                                    top: 278px;
                                    left: 770px;
                                    width: 166px;
                                    height: 39px;
                                    font-size: 12px;
                                    justify-content: end;
                                ">{{ $course?->amount ? number_format($course->amount) : '' }}</div>
            </div>
        @endif

        @php $idx = 0; @endphp
    @endforeach

    <!-- application sub_total_amount -->
    <!-- application deposit -->
    <!-- application remaining_amount-->
    <!-- application fee_amount-->
    <!-- application year_rate + %-->
    <!-- application fee_total_amount-->
    <!-- application total_amount-->
    <!-- application payment_count-->
    <!-- application first_payemnt_amount-->
    <!-- application second_payemnt_amount-->
    <!-- application bonus_payment_amount-->
    <!-- application bonus_payment_start_month-->
    <!-- application bonus_payment_month1-->
    <!-- application bonus_payment_month2-->
    <!-- application payment_start_month-->
    <!-- application payment_last_month-->
    <!-- application confirmation_doc_flag-->
    <div class="data" style="
                    top: 340px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->sub_total_amount) ? number_format($application?->sub_total_amount) : '' }}</div>
    <div class="data" style="
                    top: 373px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->deposit) ? number_format($application?->deposit) : '' }}</div>
    <div class="data" style="
                    top: 406px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->remaining_amount) ? number_format($application?->remaining_amount) : '' }}</div>
    <div class="data" style="
                    top: 439px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->fee_amount) ? number_format($application?->fee_amount) : '' }}</div>
    <div class="data" style="
                    top: 472px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ $application?->year_rate }}%
    </div>
    <div class="data" style="
                    top: 505px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->fee_total_amount) ? number_format($application?->fee_total_amount) : '' }}</div>
    <div class="data" style="
                    top: 538px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->total_amount) ? number_format($application?->total_amount) : '' }}</div>

    <div class="data" style="
                    top: 575px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->payment_count) ? number_format($application?->payment_count) : '' }}</div>
    <div class="data" style="
                    top: 610px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->first_month_payment_amount) ? number_format($application?->first_month_payment_amount) : '' }}</div>
    <div class="data" style="
                    top: 643px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->second_month_payment_amount) ? number_format($application?->second_month_payment_amount) : '' }}</div>
    <div class="data" style="
                    top: 676px;
                    left: 770px;
                    width: 166px;
                    height: 32px;
                    font-size: 12px;
                    justify-content: end;
                ">{{ isset($application?->bonus_payment_start_month) ? number_format($application?->bonus_payment_start_month) : '' }} </div>
    <!-- bonus_payment_amount -->
    <div class="data" style="
                    top: 676px;
                    left: 645px;
                    width: 43px;
                    height: 32px;
                    font-size: 9px;
                    justify-content: end;
                ">{{ isset($application?->bonus_payment_amount) ? number_format($application?->bonus_payment_amount) : '' }}</div>
    <!-- bonus_payment_month1 -->
    <div class="data" style="
                    top: 709px;
                    left: 668px;
                    width: 27px;
                    height: 32px;
                    font-size: 9px;
                    justify-content: center;
                ">{{ $application?->bonus_payment_month1 }}</div>
    <!-- bonus_payment_month2 -->
    <div class="data" style="
                    top: 709px;
                    left: 718px;
                    width: 27px;
                    height: 32px;
                    font-size: 9px;
                    justify-content: center;
                ">{{ $application?->bonus_payment_month2 }}</div>
    <!-- payment_start_month -->
    <div class="data" style="
                        top: 766px;
                        left: 593px;
                        width: 143px;
                        height: 32px;
                        font-size: 12px;
                        justify-content: center;
                ">{{ $application?->payment_start_month ? \Carbon\Carbon::parse($application->payment_start_month)->format('Y/m/d') : '' }}</div>
    <!-- payment_last_month -->
    <div class="data" style="
                        top: 766px;
                        left: 738px;
                        width: 143px;
                        height: 32px;
                        font-size: 12px;
                        justify-content: center;
                ">{{ $application?->payment_last_month ? \Carbon\Carbon::parse($application->payment_last_month)->format('Y/m/d') : '' }}</div>
    <!-- confirmation_doc_flag -->
    <div class="data" style="
                        top: 815px;
                        left: 622px;
                        width: 315px;
                        height: 33px;
                        font-size: 12px;
                        justify-content: center;
                ">
        {{ $application?->confirmation_doc_flag?->value == \App\Enums\ConfirmationDocFlagEnum::OTHER ? $application?->confirmation_doc_other : $application?->confirmation_doc_flag?->text }}
    </div>

    <!-- COL 3 -->
    <!-- contact_flag -->
    <div class="data" style="
                            top: 374px;
                    left: 1035px;
                    width: 205px;
                    height: 30px;
                    font-size: 12px;
                    justify-content: center;
                ">{{ $customer?->contact_flag?->text }}</div>
    <!-- contact_hop_date1 customers.contact_hope_start_time1 + "〜" + customers.contact_hope_end_time1 + "頃" -->
    <div class="data" style="
                            top: 406px;
                    left: 1035px;
                    width: 205px;
                    height: 30px;
                    font-size: 10px;
                    justify-content: center;
                ">
        {{ $customer?->getContactHopeDate1PDF() }} {{ $customer?->getContactHopeStartTime1PDF() }} {{ $customer?->getContactHopeEndTime1PDF() }}
        頃
    </div>
    <div class="data" style="
                            top: 437px;
                    left: 1035px;
                    width: 205px;
                    height: 30px;
                    font-size: 10px;
                    justify-content: center;
                ">
        {{ $customer?->getContactHopeDate2PDF() }} {{ $customer?->getContactHopeStartTime2PDF() }} {{ $customer?->getContactHopeEndTime2PDF() }}
        頃
    </div>
    <!-- service_handover_date -->
    <div class="data" style="
                    top: 473px;
                    left: 1070px;
                    width: 167px;
                    height: 27px;
                    font-size: 12px;
                    justify-content: center;
                ">{{ $application?->getServiceHandoverDatePDF() }} 頃
    </div>
    <!-- service_start_date -->
    <div class="data" style="
                    top: 515px;
                    left: 1049px;
                    width: 136px;
                    height: 15px;
                    font-size: 10px;
                    justify-content: center;
                ">{{ $application?->getServiceStartDatePDF() }}</div>
    <!-- service_end_date -->
    <div class="data" style="
                    top: 530px;
                    left: 1049px;
                    width: 136px;
                    height: 15px;
                    font-size: 10px;
                    justify-content: center;
                ">{{ $application?->getServiceEndDatePDF() }}</div>
    <!-- service_count -->
    <div class="data" style="
                    top: 529px;
                    left: 1199px;
                    width: 25px;
                    height: 15px;
                    font-size: 9px;
                    justify-content: center;
                ">{{ $application?->service_count }}</div>
    <!-- customer_flag -->
    <div class="data" style="
                    top: 546px;
                    left: 1050px;
                    width: 186px;
                    height: 28px;
                    font-size: 9px;
                    justify-content: center;
                ">{{ $application?->customer_flag?->value == \App\Enums\CustomerFlagEnum::OTHER ? $application?->customer_other : $application?->customer_flag?->text }}</div>

    <!-- shop_brands.name -->
    <div class="data" style="
                    top: 719px;
                    left: 996px;
                    width: 244px;
                    height: 32px;
                    font-size: 10px;
                    justify-content: start;
                    align-items: start;
                ">{{ $application?->shopBrand?->name }}</div>
    <!-- shops.zip1-zip2 -->
    <div class="data" style="
                    top: 754px;
                    left: 1002px;
                    width: 234px;
                    height: 12px;
                    font-size: 10px;
                    justify-content: start;
                ">{{ $application?->shop?->zip1 }}-{{ $application?->shop?->zip2 }}</div>
    <!-- shops.pref_idでprefs.nameを取得。
     prefs.name + shops.address -->
    <div class="data" style="
                    top: 765px;
                    left: 1002px;
                    width: 234px;
                    height: 12px;
                    font-size: 10px;
                    justify-content: start;
                ">{{ $application?->shop?->pref?->name }} {{ $application?->shop?->address }}</div>
    <!-- shop_brands.tel1 - tel2 - tel3 -->
    <div class="data" style="
                    top: 799px;
                    left: 1002px;
                    width: 234px;
                    height: 12px;
                    font-size: 10px;
                    justify-content: start;
                ">{{ $application?->shopBrand?->tel1 }}-{{ $application?->shopBrand?->tel2 }}
        -{{ $application?->shopBrand?->tel2 }}</div>
    <!-- staff_name -->
    <div class="data" style="
                    top: 821px;
                    left: 1031px;
                    width: 206px;
                    height: 28px;
                    font-size: 10px;
                    justify-content: start;
                ">{{ $application?->staff_name }}</div>
</div>
