<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Admin\BaseAdminController;
use App\Models\ApplicationInspectionStatus;
use Illuminate\Support\Facades\Storage;

class ApplicationInspectionStatusController extends BaseAdminController
{
    public function downloadFile($id)
    {
        try{
            $status = ApplicationInspectionStatus::findOrFail($id);
            $fileUrl = $status->file_url ?? null;
            $fileName = $status->file_name ?? basename($fileUrl);
            if (!$fileUrl) {
                abort(404);
            }
            $disk = config('filesystems.application_files_disk', 'public');
            
            $baseUrl = config('filesystems.disks.' . $disk . '.url');
            
            $filePath = $fileUrl;
            
            if ($baseUrl && str_starts_with($fileUrl, $baseUrl)) {
                $filePath = ltrim(str_replace($baseUrl, '', $fileUrl), '/');
            }
            
            if (!Storage::disk($disk)->exists($filePath)) {
                abort(404);
            }

            return Storage::disk($disk)->download($filePath, $fileName);
        } catch(\Throwable $e){
            logError($e);
            return false;
        }
    }
} 