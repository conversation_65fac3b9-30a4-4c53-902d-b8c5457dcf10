<?php

namespace App\Enums;


final class CustomerTabEnum extends Enum
{
    const BASIC = 'basic';
    const ACCOUNT = 'account';
    const CONTACT = 'contact';
    const EMERGENCY = 'emergency';
    const GUARANTOR = 'guarantor';
    const WORK = 'work';

    private static $tabOrder = [
        self::BASIC => 1,
        self::EMERGENCY => 2,
        self::WORK => 3,
        self::GUARANTOR => 4,
        self::ACCOUNT => 5,
        self::CONTACT => 6,
    ];

    private const CLASS_CURRENT = 'current';
    private const CLASS_COMPLETED = 'completed';
    private const CLASS_DEFAULT = '';

    public static function texts(): array
    {
        return [
            self::BASIC => trans2('CustomerTabEnum.BASIC'),
            self::EMERGENCY => trans2('CustomerTabEnum.EMERGENCY'),
            self::WORK => trans2('CustomerTabEnum.WORK'),
            self::GUARANTOR => trans2('CustomerTabEnum.GUARANTOR'),
            self::ACCOUNT => trans2('CustomerTabEnum.ACCOUNT'),
            self::CONTACT => trans2('CustomerTabEnum.CONTACT'),
        ];
    }

    public static function checkCurrentOrCompleteTab(string $tab): string
    {
        if (!array_key_exists($tab, self::$tabOrder)) {
            return self::CLASS_DEFAULT;
        }

        $currentStep = null;

        foreach (self::$tabOrder as $tabKey => $step) {
            if (request()->routeIs('admin.customer.application.setup.' . $tabKey . '*')) {
                $currentStep = $step;
                break;
            }
        }

        if (is_null($currentStep)) {
            $firstTabKey = array_key_first(self::$tabOrder);
            if ($tab === $firstTabKey) {
                return self::CLASS_CURRENT;
            }
            return self::CLASS_DEFAULT;
        }

        if (request()->routeIs('admin.customer.application.setup.' . $tab . '*')) {
            return self::CLASS_CURRENT;
        }

        if (self::$tabOrder[$tab] < $currentStep) {
            return self::CLASS_COMPLETED;
        }

        return self::CLASS_DEFAULT;
    }
}
