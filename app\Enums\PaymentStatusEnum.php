<?php
namespace App\Enums;

final class PaymentStatusEnum extends Enum
{
    const WAITING_FOR_PAYMENT = 1;
    const NOT_PAID_OVERDUE = 2;
    const PAID = 3;
    const PAID_DEFERRED_RESOLVED = 4;
    const PAID_LUMP_SUM = 5;
    const PAID_OVERPAYMENT_RESOLVED = 6;
    const OVERPAYMENT = 7;
    const CANCELLATION = 8;
    const UNRECOVERABLE = 9;
    const REFUND = 10;
    const PAID_PARTIAL_PAYMENT = 11;
    public static function texts(): array
    {
        return [
            self::WAITING_FOR_PAYMENT => trans2('PaymentStatusEnum.WAITING_FOR_PAYMENT'),
            self::NOT_PAID_OVERDUE => trans2('PaymentStatusEnum.NOT_PAID_OVERDUE'),
            self::PAID => trans2('PaymentStatusEnum.PAID'),
            self::PAID_DEFERRED_RESOLVED => trans2('PaymentStatusEnum.PAID_DEFERRED_RESOLVED'),
            self::PAID_LUMP_SUM => trans2('PaymentStatusEnum.PAID_LUMP_SUM'),
            self::PAID_OVERPAYMENT_RESOLVED => trans2('PaymentStatusEnum.PAID_OVERPAYMENT_RESOLVED'),
            self::OVERPAYMENT => trans2('PaymentStatusEnum.OVERPAYMENT'),
            self::CANCELLATION => trans2('PaymentStatusEnum.CANCELLATION'),
            self::UNRECOVERABLE => trans2('PaymentStatusEnum.UNRECOVERABLE'),
            self::REFUND => trans2('PaymentStatusEnum.REFUND'),
            self::PAID_PARTIAL_PAYMENT => trans2('PaymentStatusEnum.PAID_PARTIAL_PAYMENT'),
        ];
    }

    public static function waitingValues()
    {
        return [
            self::WAITING_FOR_PAYMENT,
            self::NOT_PAID_OVERDUE,
        ];
    }
    public static function depositedValues()
    {
        return [
            self::PAID,
            self::PAID_DEFERRED_RESOLVED,
            self::PAID_LUMP_SUM,
            self::PAID_OVERPAYMENT_RESOLVED,
            self::OVERPAYMENT,
        ];
    }
    public static function unpaidValues()
    {
        return [
            self::NOT_PAID_OVERDUE,
        ];
    }
    public static function otherValues()
    {
        return [
            self::CANCELLATION,
            self::UNRECOVERABLE,
        ];
    }

    public static function textList1()
    {
        return [
            self::WAITING_FOR_PAYMENT => trans2('PaymentStatusEnum.WAITING_FOR_PAYMENT'),
            self::NOT_PAID_OVERDUE => trans2('PaymentStatusEnum.NOT_PAID_OVERDUE'),
        ];
    }

    public static function textList2()
    {
        return [
            self::PAID => trans2('PaymentStatusEnum.PAID'),
            self::PAID_DEFERRED_RESOLVED => trans2('PaymentStatusEnum.PAID_DEFERRED_RESOLVED'),
            self::PAID_LUMP_SUM => trans2('PaymentStatusEnum.PAID_LUMP_SUM'),
            self::PAID_OVERPAYMENT_RESOLVED => trans2('PaymentStatusEnum.PAID_OVERPAYMENT_RESOLVED'),
        ];
    }

    public static function textList3()
    {
        return [
            self::OVERPAYMENT => trans2('PaymentStatusEnum.OVERPAYMENT'),
        ];
    }

    public static function textList4()
    {
        return [
            self::REFUND => trans2('PaymentStatusEnum.REFUND'),
            self::CANCELLATION => trans2('PaymentStatusEnum.CANCELLATION'),
            self::UNRECOVERABLE => trans2('PaymentStatusEnum.UNRECOVERABLE'),
        ];
    }

    public static function colors(): array
    {
        return [
            self::WAITING_FOR_PAYMENT => 'status-primary',
            self::NOT_PAID_OVERDUE => 'status-warning',
            self::PAID => 'status-success',
            self::PAID_DEFERRED_RESOLVED => 'status-success',
            self::PAID_LUMP_SUM => 'status-success',
            self::PAID_OVERPAYMENT_RESOLVED => 'status-success',
            self::OVERPAYMENT => 'status-warning',
            self::CANCELLATION => 'status-muted',
            self::UNRECOVERABLE => 'status-danger',
            self::REFUND => 'status-secondary',
            self::PAID_PARTIAL_PAYMENT => 'status-secondary',
        ];
    }
}
