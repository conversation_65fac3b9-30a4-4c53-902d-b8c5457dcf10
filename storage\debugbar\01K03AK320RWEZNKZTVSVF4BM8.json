{"__meta": {"id": "01K03AK320RWEZNKZTVSVF4BM8", "datetime": "2025-07-14 11:00:31", "utime": **********.553509, "method": "GET", "uri": "/management/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.128134, "end": **********.553522, "duration": 1.4253880977630615, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": **********.128134, "relative_start": 0, "end": **********.282585, "relative_end": **********.282585, "duration": 0.****************, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.282596, "relative_start": 0.*****************, "end": **********.553523, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.28942, "relative_start": 0.****************, "end": **********.291372, "relative_end": **********.291372, "duration": 0.0019521713256835938, "duration_str": "1.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.551207, "relative_start": 1.****************, "end": **********.551319, "relative_end": **********.551319, "duration": 0.00011181831359863281, "duration_str": "112μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.551866, "relative_start": 1.***************, "end": **********.551902, "relative_end": **********.551902, "duration": 3.600120544433594e-05, "duration_str": "36μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "1x livewire.admin.auth.login", "param_count": null, "params": [], "start": **********.068143, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/auth/login.blade.phplivewire.admin.auth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.auth.login"}, {"name": "1x components.loading-overlay", "param_count": null, "params": [], "start": **********.069229, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/loading-overlay.blade.phpcomponents.loading-overlay", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Floading-overlay.blade.php&line=1", "ajax": false, "filename": "loading-overlay.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.loading-overlay"}, {"name": "1x components.elements.flash_messages", "param_count": null, "params": [], "start": **********.069692, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/elements/flash_messages.blade.phpcomponents.elements.flash_messages", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Felements%2Fflash_messages.blade.php&line=1", "ajax": false, "filename": "flash_messages.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.elements.flash_messages"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.175026, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.auth", "param_count": null, "params": [], "start": **********.191089, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/auth.blade.phpcomponents.layouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.auth"}, {"name": "1x components.layouts.structures.head", "param_count": null, "params": [], "start": **********.191708, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/head.blade.phpcomponents.layouts.structures.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.head"}, {"name": "1x components.layouts.structures.footer_js", "param_count": null, "params": [], "start": **********.257265, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_js.blade.phpcomponents.layouts.structures.footer_js", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_js.blade.php&line=1", "ajax": false, "filename": "footer_js.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_js"}, {"name": "1x components.layouts.structures.footer_autoload", "param_count": null, "params": [], "start": **********.258068, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_autoload.blade.phpcomponents.layouts.structures.footer_autoload", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_autoload.blade.php&line=1", "ajax": false, "filename": "footer_autoload.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_autoload"}, {"name": "1x volt-livewire::common.toast-message", "param_count": null, "params": [], "start": **********.504902, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/toast-message.blade.phpvolt-livewire::common.toast-message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Ftoast-message.blade.php&line=1", "ajax": false, "filename": "toast-message.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.toast-message"}, {"name": "1x livewire.common.event-handle", "param_count": null, "params": [], "start": **********.505996, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/common/event-handle.blade.phplivewire.common.event-handle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fevent-handle.blade.php&line=1", "ajax": false, "filename": "event-handle.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.common.event-handle"}]}, "route": {"uri": "GET management/login", "middleware": "admin, locale, guest", "uses": "Closure() {#492\n  class: \"Livewire\\Volt\\VoltManager\"\n  this: Livewire\\Volt\\VoltManager {#493 …}\n  use: {\n    $componentName: \"App\\Livewire\\Admin\\Auth\\Login\"\n  }\n  file: \"C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\VoltManager.php\"\n  line: \"34 to 41\"\n}", "as": "admin.login", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": {"admin.auth.login #wfpuKBuVuRm5buNWeJwv": "array:4 [\n  \"data\" => array:5 [\n    \"guest\" => true\n    \"loginForm\" => App\\Livewire\\Admin\\Auth\\LoginForm {#824\n      #component: App\\Livewire\\Admin\\Auth\\Login {#803\n        #__id: \"wfpuKBuVuRm5buNWeJwv\"\n        #__name: \"admin.auth.login\"\n        #listeners: []\n        #attributes: Livewire\\Features\\SupportAttributes\\AttributeCollection {#926\n          #items: array:4 [\n            0 => Livewire\\Attributes\\Layout {#826\n              #component: App\\Livewire\\Admin\\Auth\\Login {#803}\n              #subTarget: null\n              #subName: null\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#875\n                +name: \"ROOT\"\n              }\n              #levelName: null\n              +name: \"components.layouts.auth\"\n              +params: []\n            }\n            1 => Livewire\\Attributes\\Validate {#871\n              #component: App\\Livewire\\Admin\\Auth\\Login {#803}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#824}\n              #subName: \"email\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#869\n                +name: \"PROPERTY\"\n              }\n              #levelName: \"loginForm.email\"\n              +rule: \"required|check_email\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n            2 => Livewire\\Attributes\\Validate {#872\n              #component: App\\Livewire\\Admin\\Auth\\Login {#803}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#824}\n              #subName: \"password\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#869}\n              #levelName: \"loginForm.password\"\n              +rule: \"required\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n            3 => Livewire\\Attributes\\Validate {#870\n              #component: App\\Livewire\\Admin\\Auth\\Login {#803}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#824}\n              #subName: \"remember\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#869}\n              #levelName: \"loginForm.remember\"\n              +rule: \"nullable\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n        #withValidatorCallback: null\n        #rulesFromOutside: []\n        #messagesFromOutside: []\n        #validationAttributesFromOutside: []\n        +guest: true\n        #viewData: []\n        +page: \"\"\n        +pageTitle: \"ログイン\"\n        +redirecting: false\n        +loginForm: App\\Livewire\\Admin\\Auth\\LoginForm {#824}\n      }\n      #propertyName: \"loginForm\"\n      #withValidatorCallback: null\n      #rulesFromOutside: array:3 [\n        0 => array:1 [\n          \"email\" => \"required|check_email\"\n        ]\n        1 => array:1 [\n          \"password\" => \"required\"\n        ]\n        2 => array:1 [\n          \"remember\" => \"nullable\"\n        ]\n      ]\n      #messagesFromOutside: []\n      #validationAttributesFromOutside: []\n      +email: \"\"\n      +password: \"\"\n      +remember: false\n      +disableLogin: false\n    }\n    \"page\" => \"\"\n    \"pageTitle\" => \"ログイン\"\n    \"redirecting\" => false\n  ]\n  \"name\" => \"admin.auth.login\"\n  \"component\" => \"App\\Livewire\\Admin\\Auth\\Login\"\n  \"id\" => \"wfpuKBuVuRm5buNWeJwv\"\n]", "common.toast-message #4u24AwLATKMSs0iWtVup": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.toast-message\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0264863d287fd82f4cefe2d814020f86.php:8$206\"\n  \"id\" => \"4u24AwLATKMSs0iWtVup\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KsF2SvrpMvyKYq4jjEGhUMQRwD5kBH2GRCXsEOX2", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01K03AK1BVWJJS97N7DP7WR89M\" => null\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management/login", "action_name": "admin.login", "controller_action": "Closure", "uri": "GET management/login", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>", "middleware": "admin, locale, guest", "duration": "1.43s", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-58799748 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-58799748\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1851658470 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1851658470\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1464383261 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"844 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjA4U2lRM1UzNUFLZDdJS2w0RnA5OFE9PSIsInZhbHVlIjoiQWR3bDY3VUdUNVhpZmFycGtuTUEzUzBMVzhPNkJ3ZEJHelRHOUQ0eWVkR1dGbEZ4UmZRUkhNalpPb0UzY2NqWkx4ZTNlUmpGNHZRajU2Vm9DdEhTaDdqalJQRFBFTWMrOGdpYzZ0Mkd5c3VocllmYVBhMmJpcVhiMUZrUEJDd0pVYzlLV2o5ajZyOGlFVjc0Zy9ZWHBRPT0iLCJtYWMiOiIzM2Q4NGVmZmVkNmY3OGViYTVlMTdhNDlhNDNmODRmOGM5MmNhMDE5YzhmZWU1NzcwMTdhYmJjOTY4NDlmMTFlIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InVNOXl6ZVJFNEtTTWZPd0tjN2gyamc9PSIsInZhbHVlIjoiemdPRklOVUZodE5DQjNuK3M3ai9MTkdqMUxZRWV3YnU5anQwU1I1bjhSQmpaOHlGMGVkMSs4aVdPOE9oZkRGWFgwYVpUV0ZjQ1hQTWthM0Fic00zUGlPeTQ0bDJOd1NrRUVhd0FWZjJ3aGlEcmNkRjdSZEUxOW92b3ZhL2c0ZC8iLCJtYWMiOiJiMzAyODhhMjIzNjM2NDkwN2ZiMzA5NDQ4NGJlNDU0MjNiZDA4ODg4OTY0NTdkODVjOTUwMDczMmJiMzA0MDFlIiwidGFnIjoiIn0%3D; ladybird_session=Udb6ATAhTWSFlkEWdVLpLEtjKwtLxhT3N0MOIVhZ</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464383261\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1077689371 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KsF2SvrpMvyKYq4jjEGhUMQRwD5kBH2GRCXsEOX2</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077689371\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-742875428 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 02:00:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImRUZ0xFYTc4bW1lVFdhNTd3bVc4TVE9PSIsInZhbHVlIjoia2tEOWY1NFFBWVRLbkF1dTZKSTdYNjZlTWhRWW52dXdrODZFbE9zRitEaEw2UVFobUFmbWdvZ2dJUVJXaW50SzRDeUZ2UXFWeGs5NXd6SllLc1JEVG1zejlZbU1hZTE3QjZzME1mVU80QWdKdDVXZU5TTDZac1grSzNIQW1SZ0MiLCJtYWMiOiI4YmY5OTFlOGI0OGY1Y2JjYzI4NjlkMGI3MTViNzA1MGNlNWY5NjM0N2IyMjE4N2IyZWIwZmY0MzM1ZDAzNWE4IiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 04:00:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImRUZ0xFYTc4bW1lVFdhNTd3bVc4TVE9PSIsInZhbHVlIjoia2tEOWY1NFFBWVRLbkF1dTZKSTdYNjZlTWhRWW52dXdrODZFbE9zRitEaEw2UVFobUFmbWdvZ2dJUVJXaW50SzRDeUZ2UXFWeGs5NXd6SllLc1JEVG1zejlZbU1hZTE3QjZzME1mVU80QWdKdDVXZU5TTDZac1grSzNIQW1SZ0MiLCJtYWMiOiI4YmY5OTFlOGI0OGY1Y2JjYzI4NjlkMGI3MTViNzA1MGNlNWY5NjM0N2IyMjE4N2IyZWIwZmY0MzM1ZDAzNWE4IiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 04:00:31 GMT; path=/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742875428\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-960316374 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KsF2SvrpMvyKYq4jjEGhUMQRwD5kBH2GRCXsEOX2</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K03AK1BVWJJS97N7DP7WR89M</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960316374\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management/login", "action_name": "admin.login", "controller_action": "Closure"}, "badge": null}}