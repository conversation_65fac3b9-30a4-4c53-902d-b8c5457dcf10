<?php

namespace App\Services;

use App\Enums\ApplicationInspectionStatusEnum;
use App\Enums\ApplicationStatusEnum;
use App\Enums\BonusFlagEnum;
use App\Enums\DocSendFlagEnum;
use App\Enums\FeeTypeEnum;
use App\Enums\TypeEnum;
use App\Models\Application;
use App\Repositories\ApplicationRepository;
use App\Repositories\ShopBrandRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApplicationService extends CustomService
{
    public function __construct(
        public ApplicationRepository $applicationRepository
    ) {
        parent::__construct();
    }

    public function searchContract($dataSearch, $perPage, $isCSV = false)
    {
        return $this->applicationRepository->getListForSearchContract($dataSearch, $perPage, $isCSV);
    }

    public function getApplication($id)
    {
        return app(ApplicationRepository::class)->find($id);
    }

    public function getCountAppByCustomerId($id)
    {
        return app(ApplicationRepository::class)->getCountAppByCustomerId($id);
    }

    public function getAppsByCustomerId($customerId)
    {
        return app(ApplicationRepository::class)->getApplicationsByCustomerId($customerId);
    }

    public function getApplicationForMemo($applicationId)
    {
        return app(ApplicationRepository::class)->getApplicationForMemo($applicationId);
    }

    public function getApplicationsForContractByCustomer($customerId)
    {
        return app(ApplicationRepository::class)->getApplicationsForContractByCustomer($customerId);
    }

    public function getContractByApplication($applicationId)
    {
        return app(ApplicationRepository::class)->getContractByApplication($applicationId);
    }

    public function store($params)
    {
        DB::beginTransaction();
        try {
            $this->applicationRepository->create($params);
            DB::commit();
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }

        return true;
    }

    /**
     * @param $customerId
     * @param array $body
     * @return false
     */
    public function createApplicationBrand($customerId, array $body)
    {
        try {
            DB::beginTransaction();

            $productGenerals = $body['product_general'] ?? [];
            $productOptionals = $body['product_optional'] ?? [];
            $maxSubtotalAmount = config('constant.MAX_SUBTOTAL_AMOUNT');

            // prepare data for application
            $shopBrand = app()->make(ShopBrandRepository::class)->findByIdWithRelation($body['shop_brand_id'], ['shop', 'brand']);
            $body['customer_id'] = $customerId;
            $body['shop_id'] = $shopBrand?->shop?->id;
            $body['brand_id'] = $shopBrand?->brand?->id;
            $body['sub_total_amount'] = $this->calcSubTotalAmount($productGenerals, $productOptionals);

            if((float)$body['sub_total_amount'] > $maxSubtotalAmount) {
                return false;
            }

            $body['status'] = ApplicationStatusEnum::IN_PROGRESS;
            $body['bonus_flag'] = BonusFlagEnum::ENABLED;
            $body['doc_send_flag'] = $body['doc_send_flag'] ?? DocSendFlagEnum::HOME;
            $application = app(ApplicationRepository::class)->create($body);
            $applicationId = $application->id;

            // mapping course general & course optional -> save application_courses
            foreach ($productGenerals as $general) {
                $courseType = TypeEnum::NORMAL;
                // save relation hasMany
                $application->applicationCourses()->create([
                    'application_id' => $applicationId,
                    'type' => $courseType,
                    'course_id' => $general['course_id'],
                    'count' => $general['quantity'] ?? 0,
                    'amount' => (float) $general['amount'] ?? 0,
                ]);
            }

            foreach ($productOptionals as $option) {
                if (!isset($option['course_id'])) {
                    break;
                }

                $courseType = TypeEnum::OPTIONAL;
                // save relation hasMany
                $application->applicationCourses()->create([
                    'application_id' => $applicationId,
                    'type' => $courseType,
                    'course_id' => $option['course_id'],
                    'count' => $option['quantity'] ?? 0,
                    'amount' => (float) $option['amount'] ?? 0,
                ]);
            }

            DB::commit();

            return $application;
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }
    }

    public function updateApplicationBrand(Application $application, array $body)
    {
        try {
            DB::beginTransaction();

            $productGenerals = $body['product_general'] ?? [];
            $productOptionals = $body['product_optional'] ?? [];
            $maxSubtotalAmount = config('constant.MAX_SUBTOTAL_AMOUNT');

            // prepare data for application
            $shopBrand = app()->make(ShopBrandRepository::class)->findByIdWithRelation($body['shop_brand_id'], ['shop', 'brand']);
            $body['shop_id'] = $shopBrand?->shop?->id;
            $body['brand_id'] = $shopBrand?->brand?->id;
            $body['sub_total_amount'] = $this->calcSubTotalAmount($productGenerals, $productOptionals);

            if((float)$body['sub_total_amount'] > $maxSubtotalAmount) {
                return false;
            }

            $application->fill($body);

            // del old data
            $application->applicationCourses()->delete();

            // mapping course general & course optional -> save application_courses
            foreach ($productGenerals as $general) {
                // save relation hasMany
                $application->applicationCourses()->create([
                    'type' => TypeEnum::NORMAL,
                    'course_id' => $general['course_id'],
                    'count' => $general['quantity'] ?? 0,
                    'amount' => (float) $general['amount'] ?? 0,
                ]);
            }

            foreach ($productOptionals as $option) {
                if (!isset($option['course_id'])) {
                    break;
                }

                // save relation hasMany
                $application->applicationCourses()->create([
                    'type' => TypeEnum::OPTIONAL,
                    'course_id' => $option['course_id'],
                    'count' => $option['quantity'] ?? 0,
                    'amount' => (float) $option['amount'] ?? 0,
                ]);
            }

            $application->save();

            DB::commit();

            return $application;
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }
    }

    /**
     * @param $applicationId
     * @param array $body
     * @return false
     */
    public function createApplicationPayment($applicationId, array $body)
    {
        try {
            DB::beginTransaction();
            $application = app()->make(ApplicationRepository::class)->findOrFail($applicationId);

            // calc fee_amount
            $yearRate = $application->brand?->year_rate ?? null;
            $depositAmount = $body['deposit'] ?? 0;
            $subTotalAmount = $application->sub_total_amount;
            $remainingAmount = max(0, $subTotalAmount - $depositAmount);
            $paymentStartMonthRaw = Carbon::createFromFormat('Y年n月', $body['payment_start_month'])->startOfMonth();
            $paymentStartMonth = $paymentStartMonthRaw->format('Ym');
            $paymentCount = $body['payment_count'] ?? 0;
            $bonusPaymentAmount = $body['bonus_payment_amount'] ?? 0;
            $feeAmount = $this->calcFeeAmount($application, $paymentCount, $subTotalAmount, $depositAmount);
            $bonusPaymentMonth1 = $body['bonus_payment_month1'] ?? null;
            $bonusPaymentMonth2 = $body['bonus_payment_month2'] ?? null;
            $countBonusPaymentMonth = ($bonusPaymentMonth1 && $bonusPaymentMonth2) ? 2 : 1;
            // bonusPaymentCount = （applications.payment_count / 12）* {bonus yearly}
            // $bonusPaymentCount = ($paymentCount / 12) * $countBonusPaymentMonth;
            $bonusPaymentCount = round($body['bonus_payment_count']);
            $usuallyMonthPaymentAmount = 0;
            $bonusMonthPaymentAmount = 0;

            $feeTotalAmount = floor($remainingAmount + $feeAmount);

            if ($body['bonus_flag'] == BonusFlagEnum::DISABLED) {
                // usually_month_payment_amount = ⑥分割支払金合計 / 支払回数
                $usuallyMonthPaymentAmount = $paymentCount > 0 ? $feeTotalAmount / $paymentCount : 0;
            }

            if ($body['bonus_flag'] == BonusFlagEnum::ENABLED) {
                // usually_month_payment_amount  =（⑥分割支払金合計 - ボーナス支払額）/ 支払回数)
                $usuallyMonthPaymentAmount = $paymentCount > 0 ? ($feeTotalAmount - $bonusPaymentAmount) / $paymentCount : 0;
                // bonus_month_payment_amount = ボーナス支払額 / ボーナス支払回数
                $bonusMonthPaymentAmount = $bonusPaymentCount > 0 ? $bonusPaymentAmount / $bonusPaymentCount : 0;
            }

            $body['remaining_amount'] = floor($remainingAmount);
            $body['fee_amount'] = $feeAmount;
            $body['fee_total_amount'] = floor($feeTotalAmount);
            $body['total_amount'] = floor($depositAmount + $feeTotalAmount);
            $body['year_rate'] = $yearRate;
            $body['payment_start_month'] = $paymentStartMonth;
            $body['payment_last_month'] = $paymentStartMonthRaw ? $paymentStartMonthRaw->addMonths((int)$paymentCount - 1)->format('Ym') : null;
            $body['bonus_payment_count'] = $bonusPaymentCount;
            $body['usually_month_payment_amount'] = floor($usuallyMonthPaymentAmount);
            $body['bonus_month_payment_amount'] = floor($bonusMonthPaymentAmount);
            $body['bonus_payment_start_month'] = $this->getBonusPaymentStartMonth($paymentStartMonth, $bonusPaymentMonth1, $bonusPaymentMonth2);
            $body['first_month_payment_amount'] = $body['first_month_payment_amount'] ?? 0;
            $body['second_month_payment_amount'] = $body['second_month_payment_amount'] ?? 0;

            $application->fill($body);
            $application->save();

            DB::commit();

            return $application;
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }
    }

    /**
     * Get month,year bonus_payment_start => get first month bonus in this year(year of $paymentStartMonth)
     * If not exists -> get first bonus of next year
     * @param $paymentStartMonth
     * @return string
     */
    private function getBonusPaymentStartMonth($paymentStartMonth, $firstBonusMonth, $secondBonusMonth)
    {
        try {
            // Get year and month from payment start month
            $startYear = Carbon::createFromFormat('Ym', $paymentStartMonth)->year;
            $startMonth = Carbon::createFromFormat('Ym', $paymentStartMonth)->month;

            $firstBonusDate = null;
            $bonusMonths = [$firstBonusMonth, $secondBonusMonth];

            // Check bonus months in ascending order
            foreach ($bonusMonths as $bonusMonth) {
                if ($bonusMonth >= $startMonth) {
                    // Target bonus month in the same year
                    $firstBonusDate = Carbon::create($startYear, $bonusMonth, 1);
                    break;
                }
            }

            // If no bonus month found, use the first bonus month of the next year
            if (!$firstBonusDate) {
                $nextYear = $startYear + 1;
                $firstBonusDate = Carbon::create($nextYear, $bonusMonths[0], 1);
            }

            return $firstBonusDate->format('Ym');
        } catch (\Throwable $exception) {
            logError('Error in getBonusPaymentStartMonth: ' . $exception->getMessage());
            return '';
        }
    }

    /**
     * @param $applicationId
     * @param array $body
     * @return false
     */
    public function createApplicationService($applicationId, array $body)
    {
        try {
            DB::beginTransaction();
            $application = app()->make(ApplicationRepository::class)->findOrFail($applicationId);

            $application->fill($body);
            $application->save();

            DB::commit();

            return $application;
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }
    }

    /**
     * @param $applicationId
     * @param array $body
     * @return false
     */
    public function createApplicationIdentification($applicationId, array $body)
    {
        try {
            DB::beginTransaction();
            $application = app()->make(ApplicationRepository::class)->findOrFail($applicationId);

            // Handle file uploads
            if (isset($body['files']) && is_array($body['files'])) {
                foreach ($body['files'] as $file) {
                    if ($file) {
                        $fileName = $file->getClientOriginalName();
                        $filePath = app(FileStorageService::class)->storeFile($file);

                        $application->applicationFiles()->create([
                            'file_name' => $fileName,
                            'file_url' => $filePath
                        ]);
                    }
                }
            }

            // Update application with other data
            $application->fill([
                'confirmation_doc_flag' => $body['confirmation_doc_flag'],
                'confirmation_doc_other' => $body['confirmation_doc_other'] ?? null,
            ]);
            $application->save();
            DB::commit();

            return $application;
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }
    }

    /**
     * @param $application
     * @param $paymentCount
     * @param $subTotalAmount
     * @param $depositAmount
     * @return float|int
     */
    public function calcFeeAmount($application, $paymentCount, $subTotalAmount, $depositAmount)
    {
        $yearRate = $application->brand?->year_rate;

        // case 1: REGULAR fee_type
        if ($application->fee_type->value === FeeTypeEnum::REGULAR) {
            $installmentFee = ($yearRate * $paymentCount) / 100;
            $finalFee = ($subTotalAmount - $depositAmount) * $installmentFee;
            return floor($finalFee ?? 0);
        }

        // course 2: self-paid fee_type
        $prevFee = ($subTotalAmount - $depositAmount) / (1 + 0.252); // (1 + 0.252) is fixed
        return $subTotalAmount - floor($prevFee);
    }

    /**
     * @param array $productGenerals
     * @param array $productOptionals
     * @return float|int
     */
    private function calcSubTotalAmount(array $productGenerals = [], array $productOptionals = [])
    {
        $totalAmount = 0;

        foreach ($productGenerals as $general) {
            $quantity = $general['quantity'] ?? 0;
            $amount = (float) ($general['amount'] ?? 0);
            $totalAmount += ($quantity * $amount);
        }

        foreach ($productOptionals as $optional) {
            if (!isset($optional['course_id'])) {
                continue;
            }
            $quantity = $optional['quantity'] ?? 0;
            $amount = (float) ($optional['amount'] ?? 0);
            $totalAmount += ($quantity * $amount);
        }

        return floor($totalAmount);
    }

    /**
     * @param $applicationId
     * @return false
     */
    public function updateApplicationCompleted($applicationId)
    {
        try {
            DB::beginTransaction();
            $application = app()->make(ApplicationRepository::class)->findOrFail($applicationId);
            $application->status = ApplicationStatusEnum::BEFORE_REVIEW;

            // update or create an application_inspection_statuss record
            $application->applicationInspectionStatus()->updateOrCreate(
                ['application_id' => $application->id], // Condition to find existing record
                ['status' => ApplicationInspectionStatusEnum::BEFORE_REVIEW] // Data to update or create
            );

            $application->save();
            DB::commit();

            return $application;
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }
    }

    /**
     * @param $applicationId
     * @return false
     */
    public function exportPDF($applicationId)
    {
        try {
            $application = $this->applicationRepository->findOrFail($applicationId);

            $applicationCourses = $application?->applicationCourses->isNotEmpty() ? $application?->applicationCourses->chunk(3) : [[]];

            $printPdfService = app(\App\Services\PrintPdfService::class);

            $printPdfService->uploadToS3 = false;
            $printPdfService->pdfFileName = 'application_' . uniqid() . '.pdf';
            $printPdfService->htmlFileName = 'application_' . uniqid() . '.html';
            $printPdfService->contentFile = view('admin.pdf.application.index', [
                'application' => $application,
                'applicationCourses' => $applicationCourses?->map(function ($applicationCourse) {
                    return $applicationCourse->values();
                }),
            ])->render();

            $file = $printPdfService->exec();

            return $file;

        } catch (\Throwable $exception) {
            logError($exception);
            return false;
        }
    }
}
