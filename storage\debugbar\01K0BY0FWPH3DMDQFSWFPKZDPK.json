{"__meta": {"id": "01K0BY0FWPH3DMDQFSWFPKZDPK", "datetime": "2025-07-17 19:13:49", "utime": **********.079597, "method": "GET", "uri": "/management", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[19:13:48] LOG.debug: (Time: 04.16) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.991482, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 01.06) SQL: select * from `shop_brands` where `shop_brands`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.014097, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 01.54) SQL: select * from `shops` where `shops`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.019593, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.48) SQL: select * from `shops` where `shops`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.03861, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 01.36) SQL: select * from `brands` where `brands`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.042983, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.748252, "end": **********.079616, "duration": 0.33136415481567383, "duration_str": "331ms", "measures": [{"label": "Booting", "start": **********.748252, "relative_start": 0, "end": **********.953047, "relative_end": **********.953047, "duration": 0.*****************, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.953067, "relative_start": 0.*****************, "end": **********.079617, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.962846, "relative_start": 0.*****************, "end": **********.965695, "relative_end": **********.965695, "duration": 0.0028488636016845703, "duration_str": "2.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.076742, "relative_start": 0.****************, "end": **********.076887, "relative_end": **********.076887, "duration": 0.*****************, "duration_str": "145μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.077519, "relative_start": 0.****************, "end": **********.077557, "relative_end": **********.077557, "duration": 3.814697265625e-05, "duration_str": "38μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 19, "nb_templates": 19, "templates": [{"name": "1x livewire.admin.dashboard.index", "param_count": null, "params": [], "start": **********.008032, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/dashboard/index.blade.phplivewire.admin.dashboard.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fdashboard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.dashboard.index"}, {"name": "1x volt-livewire::admin.dashboard.review-search", "param_count": null, "params": [], "start": **********.027955, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/admin/dashboard/review-search.blade.phpvolt-livewire::admin.dashboard.review-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fdashboard%2Freview-search.blade.php&line=1", "ajax": false, "filename": "review-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::admin.dashboard.review-search"}, {"name": "2x components.lazy-placeholder", "param_count": null, "params": [], "start": **********.032643, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/lazy-placeholder.blade.phpcomponents.lazy-placeholder", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flazy-placeholder.blade.php&line=1", "ajax": false, "filename": "lazy-placeholder.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.lazy-placeholder"}, {"name": "2x __components::dd35d746c9844c565a44df02d1d6acb6", "param_count": null, "params": [], "start": **********.033735, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\storage\\framework\\views/dd35d746c9844c565a44df02d1d6acb6.blade.php__components::dd35d746c9844c565a44df02d1d6acb6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fstorage%2Fframework%2Fviews%2Fdd35d746c9844c565a44df02d1d6acb6.blade.php&line=1", "ajax": false, "filename": "dd35d746c9844c565a44df02d1d6acb6.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::dd35d746c9844c565a44df02d1d6acb6"}, {"name": "1x volt-livewire::admin.dashboard.data-list-search", "param_count": null, "params": [], "start": **********.051255, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/admin/dashboard/data-list-search.blade.phpvolt-livewire::admin.dashboard.data-list-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fdashboard%2Fdata-list-search.blade.php&line=1", "ajax": false, "filename": "data-list-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::admin.dashboard.data-list-search"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.056758, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.master", "param_count": null, "params": [], "start": **********.057519, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/master.blade.phpcomponents.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.master"}, {"name": "1x components.layouts.structures.head", "param_count": null, "params": [], "start": **********.058153, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/head.blade.phpcomponents.layouts.structures.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.head"}, {"name": "1x components.layouts.structures.sidebar", "param_count": null, "params": [], "start": **********.060509, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/sidebar.blade.phpcomponents.layouts.structures.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.sidebar"}, {"name": "1x volt-livewire::common.nav-bar", "param_count": null, "params": [], "start": **********.063585, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/nav-bar.blade.phpvolt-livewire::common.nav-bar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fnav-bar.blade.php&line=1", "ajax": false, "filename": "nav-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.nav-bar"}, {"name": "1x volt-livewire::common.logout-modal", "param_count": null, "params": [], "start": **********.066077, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/logout-modal.blade.phpvolt-livewire::common.logout-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Flogout-modal.blade.php&line=1", "ajax": false, "filename": "logout-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.logout-modal"}, {"name": "1x volt-livewire::common.confirm", "param_count": null, "params": [], "start": **********.069497, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/confirm.blade.phpvolt-livewire::common.confirm", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.confirm"}, {"name": "1x volt-livewire::common.toast-message", "param_count": null, "params": [], "start": **********.071439, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/toast-message.blade.phpvolt-livewire::common.toast-message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Ftoast-message.blade.php&line=1", "ajax": false, "filename": "toast-message.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.toast-message"}, {"name": "1x components.layouts.structures.footer", "param_count": null, "params": [], "start": **********.072314, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer.blade.phpcomponents.layouts.structures.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer"}, {"name": "1x components.layouts.structures.footer_js", "param_count": null, "params": [], "start": **********.072718, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_js.blade.phpcomponents.layouts.structures.footer_js", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_js.blade.php&line=1", "ajax": false, "filename": "footer_js.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_js"}, {"name": "1x components.layouts.structures.footer_autoload", "param_count": null, "params": [], "start": **********.073513, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_autoload.blade.phpcomponents.layouts.structures.footer_autoload", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_autoload.blade.php&line=1", "ajax": false, "filename": "footer_autoload.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_autoload"}, {"name": "1x livewire.common.event-handle", "param_count": null, "params": [], "start": **********.074076, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/common/event-handle.blade.phplivewire.common.event-handle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fevent-handle.blade.php&line=1", "ajax": false, "filename": "event-handle.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.common.event-handle"}]}, "route": {"uri": "GET management", "middleware": "admin, locale, auth:admin", "uses": "Closure() {#502\n  class: \"Livewire\\Volt\\VoltManager\"\n  this: Livewire\\Volt\\VoltManager {#493 …}\n  use: {\n    $componentName: \"App\\Livewire\\Admin\\Dashboard\\Index\"\n  }\n  file: \"C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\VoltManager.php\"\n  line: \"34 to 41\"\n}", "as": "admin.home", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>"}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0086, "accumulated_duration_str": "8.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.987471, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 48.372}, {"sql": "select * from `shop_brands` where `shop_brands`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ShopBrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopBrandRepository.php", "line": 88}, {"index": 16, "namespace": null, "name": "app/Services/ShopBrandService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\ShopBrandService.php", "line": 97}, {"index": 17, "namespace": null, "name": "resources/views/livewire/admin/dashboard/review-search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\dashboard\\review-search.blade.php", "line": 12}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}], "start": **********.013143, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ShopBrandRepository.php:88", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ShopBrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopBrandRepository.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopBrandRepository.php&line=88", "ajax": false, "filename": "ShopBrandRepository.php", "line": "88"}, "connection": "local-ladybird", "explain": null, "start_percent": 48.372, "width_percent": 12.326}, {"sql": "select * from `shops` where `shops`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 43}, {"index": 19, "namespace": null, "name": "app/Services/ShopService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\ShopService.php", "line": 24}, {"index": 20, "namespace": null, "name": "resources/views/livewire/admin/dashboard/review-search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\dashboard\\review-search.blade.php", "line": 13}, {"index": 22, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}], "start": **********.018194, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "ShopRepository.php:43", "source": {"index": 18, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopRepository.php&line=43", "ajax": false, "filename": "ShopRepository.php", "line": "43"}, "connection": "local-ladybird", "explain": null, "start_percent": 60.698, "width_percent": 17.907}, {"sql": "select * from `shops` where `shops`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 61}, {"index": 16, "namespace": null, "name": "resources/views/livewire/admin/dashboard/data-list-search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\dashboard\\data-list-search.blade.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentResolver.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentResolver.php", "line": 39}], "start": **********.03825, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ShopRepository.php:61", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopRepository.php&line=61", "ajax": false, "filename": "ShopRepository.php", "line": "61"}, "connection": "local-ladybird", "explain": null, "start_percent": 78.605, "width_percent": 5.581}, {"sql": "select * from `brands` where `brands`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 54}, {"index": 16, "namespace": null, "name": "resources/views/livewire/admin/dashboard/data-list-search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\dashboard\\data-list-search.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentResolver.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentResolver.php", "line": 39}], "start": **********.041731, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "BrandRepository.php:54", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FBrandRepository.php&line=54", "ajax": false, "filename": "BrandRepository.php", "line": "54"}, "connection": "local-ladybird", "explain": null, "start_percent": 84.186, "width_percent": 15.814}]}, "models": {"data": {"App\\Models\\Brand": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Shop": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FShop.php&line=1", "ajax": false, "filename": "Shop.php", "line": "?"}}, "App\\Models\\ShopBrand": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FShopBrand.php&line=1", "ajax": false, "filename": "ShopBrand.php", "line": "?"}}, "App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 17, "is_counter": true}, "livewire": {"data": {"admin.dashboard #A3XQIfUBXrfveetEDxyP": "array:4 [\n  \"data\" => array:4 [\n    \"page\" => \"dashboard\"\n    \"pageTitle\" => \"ダッシュボード｜LadyBird\"\n    \"redirecting\" => false\n    \"guest\" => false\n  ]\n  \"name\" => \"admin.dashboard\"\n  \"component\" => \"App\\Livewire\\Admin\\Dashboard\\Index\"\n  \"id\" => \"A3XQIfUBXrfveetEDxyP\"\n]", "admin.dashboard.review-search #rSmoHQhu9IvrXkSsOxuD": "array:4 [\n  \"data\" => array:4 [\n    \"shopId\" => \"\"\n    \"shopBrandId\" => \"\"\n    \"listShopBrands\" => Illuminate\\Database\\Eloquent\\Collection {#974\n      #items: array:2 [\n        0 => App\\Models\\ShopBrand {#973\n          #connection: \"mysql\"\n          #table: \"shop_brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:14 [\n            \"id\" => 1\n            \"view_id\" => \"SB-1\"\n            \"name\" => \"aaa\"\n            \"brand_id\" => 1\n            \"shop_id\" => 2\n            \"tel1\" => \"111\"\n            \"tel2\" => \"222\"\n            \"tel3\" => \"333\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:19\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:19\"\n            \"upd_id\" => 2\n          ]\n          #original: array:14 [\n            \"id\" => 1\n            \"view_id\" => \"SB-1\"\n            \"name\" => \"aaa\"\n            \"brand_id\" => 1\n            \"shop_id\" => 2\n            \"tel1\" => \"111\"\n            \"tel2\" => \"222\"\n            \"tel3\" => \"333\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:19\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:19\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:14 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"brand_id\"\n            4 => \"shop_id\"\n            5 => \"tel1\"\n            6 => \"tel2\"\n            7 => \"tel3\"\n            8 => \"credit_flag\"\n            9 => \"del_flag\"\n            10 => \"ins_id\"\n            11 => \"ins_date\"\n            12 => \"upd_id\"\n            13 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\ShopBrand {#971\n          #connection: \"mysql\"\n          #table: \"shop_brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:14 [\n            \"id\" => 2\n            \"view_id\" => \"SB-2\"\n            \"name\" => \"bbb\"\n            \"brand_id\" => 1\n            \"shop_id\" => 1\n            \"tel1\" => \"111\"\n            \"tel2\" => \"222\"\n            \"tel3\" => \"333\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:35\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:36\"\n            \"upd_id\" => 2\n          ]\n          #original: array:14 [\n            \"id\" => 2\n            \"view_id\" => \"SB-2\"\n            \"name\" => \"bbb\"\n            \"brand_id\" => 1\n            \"shop_id\" => 1\n            \"tel1\" => \"111\"\n            \"tel2\" => \"222\"\n            \"tel3\" => \"333\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:35\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:36\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:14 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"brand_id\"\n            4 => \"shop_id\"\n            5 => \"tel1\"\n            6 => \"tel2\"\n            7 => \"tel3\"\n            8 => \"credit_flag\"\n            9 => \"del_flag\"\n            10 => \"ins_id\"\n            11 => \"ins_date\"\n            12 => \"upd_id\"\n            13 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"listShops\" => Illuminate\\Database\\Eloquent\\Collection {#985\n      #items: array:2 [\n        0 => App\\Models\\Shop {#984\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Shop {#982\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"admin.dashboard.review-search\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\b7e63f8b35eb527375f16aaabe085b4c.php:8$11d\"\n  \"id\" => \"rSmoHQhu9IvrXkSsOxuD\"\n]", "admin.dashboard.data-list-search #pyafekkWaKapsZ8iRoHZ": "array:4 [\n  \"data\" => array:7 [\n    \"authType\" => \"\"\n    \"brandId\" => \"\"\n    \"storeId\" => \"\"\n    \"from\" => \"\"\n    \"to\" => \"\"\n    \"stores\" => Illuminate\\Database\\Eloquent\\Collection {#1061\n      #items: array:2 [\n        0 => App\\Models\\Shop {#1039\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Shop {#1040\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"brands\" => Illuminate\\Database\\Eloquent\\Collection {#1366\n      #items: array:10 [\n        0 => App\\Models\\Brand {#1358\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Brand {#1367\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        2 => App\\Models\\Brand {#1396\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        3 => App\\Models\\Brand {#1368\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        4 => App\\Models\\Brand {#1379\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        5 => App\\Models\\Brand {#1370\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        6 => App\\Models\\Brand {#1359\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        7 => App\\Models\\Brand {#1360\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        8 => App\\Models\\Brand {#1385\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        9 => App\\Models\\Brand {#1363\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"admin.dashboard.data-list-search\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\befae5189d083b99513bcfed0ea24f54.php:8$132\"\n  \"id\" => \"pyafekkWaKapsZ8iRoHZ\"\n]", "common.nav-bar #JBVfu5u22irq8oyUIWXM": "array:4 [\n  \"data\" => array:2 [\n    \"breadcrumbs\" => array:1 [\n      0 => array:2 [\n        \"label\" => \"ダッシュボード\"\n        \"url\" => \"http://127.0.0.1:8000/management\"\n      ]\n    ]\n    \"isHideBreadcrumb\" => false\n  ]\n  \"name\" => \"common.nav-bar\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\common\\nav-bar.blade.php:8$13b\"\n  \"id\" => \"JBVfu5u22irq8oyUIWXM\"\n]", "common.logout-modal #pfVU4bSBbYjsegRAGa41": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.logout-modal\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\acd2c66864e16c9a44c5d2061051a042.php:8$13c\"\n  \"id\" => \"pfVU4bSBbYjsegRAGa41\"\n]", "common.confirm #gNXmRHs5MWv23iXax0Tz": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.confirm\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\21a91b4a087079d3e133812909232442.php:8$13f\"\n  \"id\" => \"gNXmRHs5MWv23iXax0Tz\"\n]", "common.toast-message #hEaWj0NSyHbKKFJ0fuJ1": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.toast-message\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0264863d287fd82f4cefe2d814020f86.php:8$140\"\n  \"id\" => \"hEaWj0NSyHbKKFJ0fuJ1\"\n]"}, "count": 7}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/management_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01K0BXQV0SJEFC3DS5EP0F8JAP&op=get\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management", "action_name": "admin.home", "controller_action": "Closure", "uri": "GET management", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>", "middleware": "admin, locale, auth:admin", "duration": "334ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-923167989 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-923167989\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-255026300 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-255026300\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1067719596 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/management/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IkV4b3ZySWVaTjI0czgxRmpycEc3MHc9PSIsInZhbHVlIjoiQUk4ak14MytpVTdFUjBubkplSWZJK25Rc0FWbXV5K2pabU5nU0tuVUNycWx3UXpoZjNqdW9NNWlzcmJ3bmsvOCIsIm1hYyI6IjI5ZjgzNWFiMDIxNTg3MDc3MGU4YzFlNDA3OGVjNDg4N2ExODA5MmU2NDJjMzMyMzdmMWMxZDVkY2RjMDA1ZDMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImlqR1hKc3kxU295eW9idG9McWtTeVE9PSIsInZhbHVlIjoiWkQ5SzNBUnA4SlhaUHRZLzk2Rm5PMTVrWm1IYUF0Y2xkanBGSWphOEhkbVZ0QUlTRzJRUG5SczFsNzZMc3VSdHlDSUtIOHdEWmRmbWV4Y2tzQWo2VzlEVVk3SEJIa2tHdjlOcDVMcWw2aXVsQ3pZOHd6R3pBRXA1SklMYzlieUZuTVU0ejVqVkRKU01iUjV5NzdTN1BnPT0iLCJtYWMiOiJmZThlMThjNTZmMjk0ZGY4NWU5NGMyZmYxMWJiMzJkMzhjNGYwMGQ4ZGRjNGU0MTlmYzc0N2RjNDM0ZTZkNmIwIiwidGFnIjoiIn0%3D; ladybird_session=tGFewrUFVXwdbXowWrlw5IwCNUgVWIGWxWStuDZg; XSRF-TOKEN=eyJpdiI6Ii9CcWd6N3VIaG96VllkRzlJd256Mmc9PSIsInZhbHVlIjoibFNINlJBNGdWdEZ0dUY4Nkk5NEdudVNUTm1JVXkrbFpzM0pGSU5La3hVTjU0RXR6SDZDc1FFZ2NRbGhlQlNLaUlVWkIrS0NWMkVrMEtkQVlyc1FNUlVIWmNHZVJJNVJVOUVvdGx4VW5xbTZaZ0xwRzVrQWJMRXpFa1JuOVVaSlQiLCJtYWMiOiIyYzYyZTk0MDA3ZDI5ZDM3M2RjYTI1ODM5MWNkNGIzYmQ0NWM3YzdiMGQwOWM2NWI2NWMyNTVkNmYzMDIyNTI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067719596\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1679804075 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679804075\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-682459234 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 10:13:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFGSEd6c2dJM2ZTajlJbWVqa0RDK0E9PSIsInZhbHVlIjoieUVHMDlyaDRRbjAvcnkzZFpSM0FRRW5oczhCNHRxK3Q4dVpYMXdGaDdOY2V5L1gxaHdrb2Ixb09KdVdkeVFpWWYvbEJaK2w3RmMzNlN6WVFab2RzL0xsOWF2YnhYbmpHRG1PRWhnVllXOG1zVm1nYW5LTkNlYTVDT1ZYMS9zU0siLCJtYWMiOiI4ZTBiZmE2ZDZlNGYxZmQ1NDI0NDMyZWEyMDczM2ZhZDMzMmU5OThlNTVjMTBkZDc1YjcyN2I3MDNhNjYzYmJlIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 12:13:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFGSEd6c2dJM2ZTajlJbWVqa0RDK0E9PSIsInZhbHVlIjoieUVHMDlyaDRRbjAvcnkzZFpSM0FRRW5oczhCNHRxK3Q4dVpYMXdGaDdOY2V5L1gxaHdrb2Ixb09KdVdkeVFpWWYvbEJaK2w3RmMzNlN6WVFab2RzL0xsOWF2YnhYbmpHRG1PRWhnVllXOG1zVm1nYW5LTkNlYTVDT1ZYMS9zU0siLCJtYWMiOiI4ZTBiZmE2ZDZlNGYxZmQ1NDI0NDMyZWEyMDczM2ZhZDMzMmU5OThlNTVjMTBkZDc1YjcyN2I3MDNhNjYzYmJlIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 12:13:49 GMT; path=/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682459234\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/management_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01K0BXQV0SJEFC3DS5EP0F8JAP&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management", "action_name": "admin.home", "controller_action": "Closure"}, "badge": null}}