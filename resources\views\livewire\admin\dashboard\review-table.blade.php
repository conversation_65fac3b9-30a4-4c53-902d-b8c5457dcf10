<div>
    <h2 class="heading-2">{{ trans2('screens.dashboard.review_table') }}</h2>
    <table class="table table-primary w-px-500">
        <tbody>
            <tr>
                <th>{{ trans2('screens.dashboard.app_in_progress') }}</th>
                <td class="w-50 fc-primary"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_in_progress_count) }}</a></td>
            </tr>
        </tbody>
    </table>
    <h2 class="heading-2">{{ trans2('screens.dashboard.review_table') }}</h2>
    <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
        <table class="table table-primary w-100">
            <tbody>
                <tr>
                    <th colspan="3">{{ trans2('screens.dashboard.app_before_review') }}</th>
                    <td class="w-25 fc-primary"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_before_review_count) }}</a></td>
                </tr>
                <tr>
                    <th colspan="3">{{ trans2('screens.dashboard.app_under_review') }}</th>
                    <td rowspan="7" class="w-25 align-middle fc-primary">
                        <a wire:navigate href="{{ getRoute('customer.index') }}" class="badge badge-bell me-3">{{ number_format($app_inspect_status_count_all) }}</a><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_under_review_count) }}</a>
                    </td>
                </tr>
                <tr>
                    <td rowspan="2" class="bg-gray">{{ trans2('screens.dashboard.check_doc') }}</td>
                    <td class="bg-gray align-middle">{{ trans2('screens.dashboard.app_incomplete') }}</td>
                    <td class="align-middle fc-primary">
                        <a wire:navigate href="{{ getRoute('customer.index') }}" class="badge badge-bell me-3">{{ number_format($app_inspect_status1_count) }}</a><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_incomplete_count) }}</a>
                    </td>
                </tr>
                <tr>
                    <td class="bg-gray border-left align-middle">{{ trans2('screens.dashboard.other_or_notification') }}</td>
                    <td class="align-middle"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_doc_check_other_count) }}</a></td>
                </tr>
                <tr>
                    <td rowspan="2" class="bg-gray">{{ trans2('screens.dashboard.identity_verification') }}</td>
                    <td class="bg-gray align-middle">{{ trans2('screens.dashboard.app_identity_verification_scheduling') }}</td>
                    <td class="align-middle fc-primary">
                        <a wire:navigate href="{{ getRoute('customer.index') }}" class="badge badge-bell me-3">{{ number_format($app_inspect_status2_count) }}</a><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_identity_verification_scheduling_count) }}</a>
                    </td>
                </tr>
                <tr>
                    <td class="bg-gray border-left align-middle">{{ trans2('screens.dashboard.other_or_notification') }}</td>
                    <td class="align-middle fc-primary"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_identity_verification_other_count) }}</a></td>
                </tr>
                <tr>
                    <td rowspan="2" class="bg-gray">{{ trans2('screens.dashboard.check_guarantor') }}</td>
                    <td class="bg-gray align-middle">{{ trans2('screens.dashboard.app_identity_verification_scheduling') }}</td>
                    <td class="align-middle fc-primary"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_guarantor_verification_scheduling_count) }}</a></td>
                </tr>
                <tr>
                    <td class="bg-gray border-left align-middle">{{ trans2('screens.dashboard.other_or_notification') }}</td>
                    <td class="align-middle fc-primary">
                        <a wire:navigate href="{{ getRoute('customer.index') }}" class="badge badge-bell me-3">{{ number_format($app_inspect_status3_count) }}</a><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_guarantor_verification_other_count) }}</a>
                    </td>
                </tr>
                <tr>
                    <th colspan="3">{{ trans2('screens.dashboard.app_approved') }}</th>
                    <td class="w-25 fc-primary"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_approved_count) }}</a></td>
                </tr>
                <tr>
                    <th colspan="3">{{ trans2('screens.dashboard.app_rejected') }}</th>
                    <td class="w-25 fc-primary"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_rejected_count) }}</a></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="row">
        <div class="col-6 pe-4">
            <h2 class="heading-2">{{ trans2('screens.dashboard.cooling_off') }}</h2>
            <table class="table table-primary w-100">
                <tbody>
                    <tr>
                        <th>{{ trans2('screens.dashboard.cooling_off_request') }}</th>
                        <td class="w-50 fc-primary"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_cooling_off_request_count) }}</a></td>
                    </tr>
                    <tr>
                        <th>{{ trans2('screens.dashboard.cooling_off_approved') }}</th>
                        <td class="w-50 fc-primary"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_cooling_off_approved_count) }}</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="col-6 ps-4">
            <h2 class="heading-2">{{ trans2('screens.dashboard.store_cancel') }}</h2>
            <table class="table table-primary w-100">
                <tbody>
                    <tr>
                        <th>{{ trans2('screens.dashboard.store_cancel_request') }}</th>
                        <td class="w-50"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_store_cancel_request_count) }}</a></td>
                    </tr>
                    <tr>
                        <th>{{ trans2('screens.dashboard.store_cancel_approved') }}</th>
                        <td class="w-50"><a wire:navigate class="text-decoration-none" href="{{ getRoute('customer.index') }}">{{ number_format($application_store_cancel_approved_count) }}</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
