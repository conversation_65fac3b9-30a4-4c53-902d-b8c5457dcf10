<?php

namespace App\Livewire\Admin\Payment\DepositList;

use App\Enums\LoanTransactionTypeEnum;
use App\Enums\TypeEnum;
use App\Livewire\Admin\Payment\Forms\CreateForm;
use App\Livewire\Admin\Payment\Forms\UpdateForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\PaymentService;
use App\Services\ToastService;
use App\Services\LoanWithdrawalService;
use App\Services\LoanPaymentService;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class UpdatePayment extends BaseAdminPageComponent
{
    public UpdateForm $updateForm;
    public $applicationId;
    public $customerId;
    public $id;

    public function __construct()
    {
        $this->dispatch('init-select2');
    }

    #[On('log-show')]
    public function show($data)
    {
        $this->resetForm('updateForm');
        $log = null;
        if($data['flag'] == LoanTransactionTypeEnum::DEPOSIT){
            $log = app(LoanPaymentService::class)->getLoanPayment($data['id']);
        } else {
            $log = app(LoanWithdrawalService::class)->getWithdrawal($data['id']);
        }

        if (empty($log)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->dispatch('refresh-deposit');
            return;
        }
        $this->id = $data['id'];
        if (data_get($data,'flag') == LoanTransactionTypeEnum::WITHDRAWAL){
            $this->updateForm->payment_date = data_get($log, 'withdrawal_date');
            $this->updateForm->payment_type = data_get($log, 'withdrawal_type');
        }

        $this->updateForm->fill($log?->toArray() ?? []);

        $this->updateForm->type = data_get($data,'flag');
        $this->updateForm->type_flag = data_get($data,'flag');
        $this->js("$('#editPaymentsModal').modal('show');");
    }

    public function validateUpdate()
    {
        $this->updateForm->validate();
        $this->js("$('#editPaymentsModal').modal('hide');");
        $this->setDataCreateConfirmModal(getConstant('SCREENS.PAYMENT'), 'update-payment', 'editPaymentsModal');
    }

    #[On('update-payment')]
    public function updatePayment()
    {
        $params = $this->updateForm->all();
        DB::beginTransaction();
        $result = app(PaymentService::class)->editPayment($this->applicationId, $this->customerId, $params, $this->id);

        if ($result['result']) {
            DB::commit();
            $this->toastSuccess(__('messages.create_success'));
            $this->dispatch('refresh-deposit');
        } else {
            DB::rollBack();
            if($result['message'] !== ""){
                $this->js("message = " . json_encode($result['message']).";
                    $('#messageBox').text(message);
                    $('#messageModal').modal('show');"
                );
            } else {
                $this->toastError(__('messages.create_failed'));
            }
        }

        $this->updateForm->reset();
    }

    public function render()
    {
        return $this->viewLivewireAdmin('payment.deposit-list.update-payment');
    }
}
