{"__meta": {"id": "01K0BXQTGB6E24GFKEK297BSVH", "datetime": "2025-07-17 19:09:05", "utime": **********.035596, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 20, "messages": [{"message": "[19:09:04] LOG.debug: (Time: 25.16) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.943356, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:04] LOG.debug: (Time: 00.65) SQL: select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.981512, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:04] LOG.debug: (Time: 00.35) SQL: select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.984479, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:04] LOG.debug: (Time: 00.34) SQL: select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.986952, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:04] LOG.debug: (Time: 00.56) SQL: select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.989991, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:04] LOG.debug: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.992708, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:04] LOG.debug: (Time: 00.36) SQL: select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.995143, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:04] LOG.debug: (Time: 00.31) SQL: select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.997533, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:04] LOG.debug: (Time: 00.33) SQL: select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.999937, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.002469, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.38) SQL: select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.005054, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.007918, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.28) SQL: select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.010267, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.26) SQL: select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.012485, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.26) SQL: select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.014687, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.26) SQL: select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.016876, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.36) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.020068, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.43) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.022618, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.38) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.025274, "xdebug_link": null, "collector": "log"}, {"message": "[19:09:05] LOG.debug: (Time: 00.28) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.027617, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.695195, "end": **********.035625, "duration": 0.34043002128601074, "duration_str": "340ms", "measures": [{"label": "Booting", "start": **********.695195, "relative_start": 0, "end": **********.879186, "relative_end": **********.879186, "duration": 0.****************, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.879262, "relative_start": 0.****************, "end": **********.035626, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.894024, "relative_start": 0.*****************, "end": **********.895836, "relative_end": **********.895836, "duration": 0.0018122196197509766, "duration_str": "1.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.0334, "relative_start": 0.*****************, "end": **********.034126, "relative_end": **********.034126, "duration": 0.0007259845733642578, "duration_str": "726μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x livewire.admin.dashboard.review-table", "param_count": null, "params": [], "start": **********.03127, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/dashboard/review-table.blade.phplivewire.admin.dashboard.review-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fdashboard%2Freview-table.blade.php&line=1", "ajax": false, "filename": "review-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.dashboard.review-table"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 20, "nb_statements": 20, "nb_visible_statements": 20, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.032200000000000006, "accumulated_duration_str": "32.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.918629, "duration": 0.025160000000000002, "duration_str": "25.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 78.137}, {"sql": "select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.9809852, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 78.137, "width_percent": 2.019}, {"sql": "select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.984237, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 80.155, "width_percent": 1.087}, {"sql": "select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [3, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.986715, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 81.242, "width_percent": 1.056}, {"sql": "select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [4, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.989594, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 82.298, "width_percent": 1.739}, {"sql": "select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.992401, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 84.037, "width_percent": 1.273}, {"sql": "select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.99489, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 85.311, "width_percent": 1.118}, {"sql": "select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.99733, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 86.429, "width_percent": 0.963}, {"sql": "select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [8, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.9997041, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 87.391, "width_percent": 1.025}, {"sql": "select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [9, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.002205, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 88.416, "width_percent": 1.211}, {"sql": "select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [10, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.004775, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 89.627, "width_percent": 1.18}, {"sql": "select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.007584, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 90.807, "width_percent": 1.398}, {"sql": "select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [12, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.010076, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 92.205, "width_percent": 0.87}, {"sql": "select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.012313, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 93.075, "width_percent": 0.807}, {"sql": "select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [14, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.0145128, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 93.882, "width_percent": 0.807}, {"sql": "select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.016702, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 94.689, "width_percent": 0.807}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, 3, 4, 5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.019799, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 95.497, "width_percent": 1.118}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, 8, 9, 10, 11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.0223298, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 96.615, "width_percent": 1.335}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, 12, 13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.025001, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 97.95, "width_percent": 1.18}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.0274272, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 99.13, "width_percent": 0.87}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.dashboard.review-table #NndlYiMWXsEzbymuZnVy": "array:4 [\n  \"data\" => array:5 [\n    \"shopId\" => \"\"\n    \"shopBrandId\" => \"\"\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.dashboard.review-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Dashboard\\ReviewTable\"\n  \"id\" => \"NndlYiMWXsEzbymuZnVy\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/management\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "343ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2059106822 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2059106822\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1732494078 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"421 characters\">{&quot;data&quot;:{&quot;shopId&quot;:&quot;&quot;,&quot;shopBrandId&quot;:&quot;&quot;,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;NndlYiMWXsEzbymuZnVy&quot;,&quot;name&quot;:&quot;admin.dashboard.review-table&quot;,&quot;path&quot;:&quot;management&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;props&quot;:[&quot;shopId&quot;,&quot;shopBrandId&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;915805471efd29d066f4f1b2708d6b9ed91cdd78f10c6b12c2352099e36c5b30&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"284 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJzaG9wQnJhbmRJZCI6IiIsInNob3BJZCI6IiJ9LHsicyI6ImFyciJ9XX0sIm1lbW8iOnsiaWQiOiJtYUhFRjFjQ1lTRTdGZHkwbG9NaiIsIm5hbWUiOiJfX21vdW50UGFyYW1zQ29udGFpbmVyIn0sImNoZWNrc3VtIjoiNTY0ZGY4NmNjM2MwYmVjM2Y1NmY2NDc1OTU0NTU5YjcwM2RkMDQ1MjQwZGU1YTUwM2VlNTM0YzA1YWNhNzljMSJ9</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732494078\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-323740311 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">924</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IkV4b3ZySWVaTjI0czgxRmpycEc3MHc9PSIsInZhbHVlIjoiQUk4ak14MytpVTdFUjBubkplSWZJK25Rc0FWbXV5K2pabU5nU0tuVUNycWx3UXpoZjNqdW9NNWlzcmJ3bmsvOCIsIm1hYyI6IjI5ZjgzNWFiMDIxNTg3MDc3MGU4YzFlNDA3OGVjNDg4N2ExODA5MmU2NDJjMzMyMzdmMWMxZDVkY2RjMDA1ZDMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImlqR1hKc3kxU295eW9idG9McWtTeVE9PSIsInZhbHVlIjoiWkQ5SzNBUnA4SlhaUHRZLzk2Rm5PMTVrWm1IYUF0Y2xkanBGSWphOEhkbVZ0QUlTRzJRUG5SczFsNzZMc3VSdHlDSUtIOHdEWmRmbWV4Y2tzQWo2VzlEVVk3SEJIa2tHdjlOcDVMcWw2aXVsQ3pZOHd6R3pBRXA1SklMYzlieUZuTVU0ejVqVkRKU01iUjV5NzdTN1BnPT0iLCJtYWMiOiJmZThlMThjNTZmMjk0ZGY4NWU5NGMyZmYxMWJiMzJkMzhjNGYwMGQ4ZGRjNGU0MTlmYzc0N2RjNDM0ZTZkNmIwIiwidGFnIjoiIn0%3D; ladybird_session=tGFewrUFVXwdbXowWrlw5IwCNUgVWIGWxWStuDZg; XSRF-TOKEN=eyJpdiI6IjR1ejBuUm83enVnTEQyd05uU1YxN2c9PSIsInZhbHVlIjoib2lIVDBFNGhvTGthZkNyV0RDQXh3M0xpVHFvVW5sc3QycnFGZVVPL1I2TlNRR1pTTHoxTDFUdDRzbEhQVkRmTDRhaVhKQUhBNUFIWUhiTnhBQUNpVWlyeit0UnY5SlQzSElaV3pRZmhpTmJQbW8zVUV6VHhRUjJCYnFQZmVNZ2MiLCJtYWMiOiIzYTE4M2NmNTJhYTIyNzBlMTJlMTQ1ZTE2NmY4ZDIzNzY4Mzc5ZjRmMDYwYjYwNjU0M2MxNGZhYjU5MmVlZjQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323740311\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1927263152 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927263152\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1048522781 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 10:09:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048522781\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-844849524 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844849524\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}