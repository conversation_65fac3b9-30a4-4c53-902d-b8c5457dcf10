{"__meta": {"id": "01JZS2FNTR2RZXWSBF1AET9ZST", "datetime": "2025-07-10 11:26:26", "utime": **********.776929, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 21, "messages": [{"message": "[11:26:26] LOG.debug: (Time: 15.08) SQL: select * from `administrators` where `id` = 2 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.294069, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.34) SQL: select * from `administrator_shops` where `administrator_shops`.`administrator_id` = 2 and `administrator_shops`.`administrator_id` is not null and `administrator_shops`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.363947, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.37) SQL: select count(*) as aggregate from `applications` where `status` = 1 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.366719, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 2 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.369733, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.86) SQL: select count(*) as aggregate from `applications` where `status` = 3 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.374413, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.33) SQL: select count(*) as aggregate from `applications` where `status` = 4 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.377834, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.30) SQL: select count(*) as aggregate from `applications` where `status` = 5 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.381004, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.31) SQL: select count(*) as aggregate from `applications` where `status` = 6 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.383882, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.33) SQL: select count(*) as aggregate from `applications` where `status` = 7 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.386821, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 8 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.390848, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.46) SQL: select count(*) as aggregate from `applications` where `status` = 9 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.393991, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 10 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.396961, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.37) SQL: select count(*) as aggregate from `applications` where `status` = 11 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.399913, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 12 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.402888, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.51) SQL: select count(*) as aggregate from `applications` where `status` = 13 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.406531, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.38) SQL: select count(*) as aggregate from `applications` where `status` = 14 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.410528, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.43) SQL: select count(*) as aggregate from `applications` where `status` = 15 and 0 = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.413818, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.27) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and 0 = 1 and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.476416, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.28) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and 0 = 1 and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.479384, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.31) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and 0 = 1 and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.482318, "xdebug_link": null, "collector": "log"}, {"message": "[11:26:26] LOG.debug: (Time: 00.32) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and 0 = 1 and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.485238, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.077811, "end": **********.77696, "duration": 0.6991488933563232, "duration_str": "699ms", "measures": [{"label": "Booting", "start": **********.077811, "relative_start": 0, "end": **********.245439, "relative_end": **********.245439, "duration": 0.*****************, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.245447, "relative_start": 0.*****************, "end": **********.776961, "relative_end": 1.1920928955078125e-06, "duration": 0.****************, "duration_str": "532ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.252216, "relative_start": 0.*****************, "end": **********.25405, "relative_end": **********.25405, "duration": 0.0018339157104492188, "duration_str": "1.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.774738, "relative_start": 0.****************, "end": **********.775357, "relative_end": **********.775357, "duration": 0.0006189346313476562, "duration_str": "619μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x livewire.admin.dashboard.review-table", "param_count": null, "params": [], "start": **********.490639, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/dashboard/review-table.blade.phplivewire.admin.dashboard.review-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fdashboard%2Freview-table.blade.php&line=1", "ajax": false, "filename": "review-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.dashboard.review-table"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 21, "nb_statements": 21, "nb_visible_statements": 21, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.022850000000000002, "accumulated_duration_str": "22.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 2 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.279253, "duration": 0.01508, "duration_str": "15.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 65.996}, {"sql": "select * from `administrator_shops` where `administrator_shops`.`administrator_id` = 2 and `administrator_shops`.`administrator_id` is not null and `administrator_shops`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Presenters/PAdministrator.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Models\\Presenters\\PAdministrator.php", "line": 100}, {"index": 21, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 412}, {"index": 22, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 23, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 30}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.363708, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PAdministrator.php:100", "source": {"index": 20, "namespace": null, "name": "app/Models/Presenters/PAdministrator.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Models\\Presenters\\PAdministrator.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FPresenters%2FPAdministrator.php&line=100", "ajax": false, "filename": "PAdministrator.php", "line": "100"}, "connection": "local-ladybird", "explain": null, "start_percent": 65.996, "width_percent": 1.488}, {"sql": "select count(*) as aggregate from `applications` where `status` = 1 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.366457, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 67.484, "width_percent": 1.619}, {"sql": "select count(*) as aggregate from `applications` where `status` = 2 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.369442, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 69.103, "width_percent": 1.707}, {"sql": "select count(*) as aggregate from `applications` where `status` = 3 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [3, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.373881, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 70.81, "width_percent": 3.764}, {"sql": "select count(*) as aggregate from `applications` where `status` = 4 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [4, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.377606, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 74.573, "width_percent": 1.444}, {"sql": "select count(*) as aggregate from `applications` where `status` = 5 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.380801, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 76.018, "width_percent": 1.313}, {"sql": "select count(*) as aggregate from `applications` where `status` = 6 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.383675, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 77.33, "width_percent": 1.357}, {"sql": "select count(*) as aggregate from `applications` where `status` = 7 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.386592, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 78.687, "width_percent": 1.444}, {"sql": "select count(*) as aggregate from `applications` where `status` = 8 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [8, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.390544, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 80.131, "width_percent": 1.794}, {"sql": "select count(*) as aggregate from `applications` where `status` = 9 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [9, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.3936322, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 81.926, "width_percent": 2.013}, {"sql": "select count(*) as aggregate from `applications` where `status` = 10 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [10, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.3966699, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 83.939, "width_percent": 1.707}, {"sql": "select count(*) as aggregate from `applications` where `status` = 11 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.399647, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 85.646, "width_percent": 1.619}, {"sql": "select count(*) as aggregate from `applications` where `status` = 12 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [12, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.4025838, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 87.265, "width_percent": 1.794}, {"sql": "select count(*) as aggregate from `applications` where `status` = 13 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.406214, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 89.059, "width_percent": 2.232}, {"sql": "select count(*) as aggregate from `applications` where `status` = 14 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [14, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.410272, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 91.291, "width_percent": 1.663}, {"sql": "select count(*) as aggregate from `applications` where `status` = 15 and 0 = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.413503, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 92.954, "width_percent": 1.882}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and 0 = 1 and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, 3, 4, 5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.4762492, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 94.836, "width_percent": 1.182}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and 0 = 1 and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, 8, 9, 10, 11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.4792092, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 96.018, "width_percent": 1.225}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and 0 = 1 and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, 12, 13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.4821072, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 97.243, "width_percent": 1.357}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and 0 = 1 and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.4850202, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 98.6, "width_percent": 1.4}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.dashboard.review-table #XZBaQDpWpbXEQNJQgurO": "array:4 [\n  \"data\" => array:5 [\n    \"shopId\" => \"\"\n    \"shopBrandId\" => \"\"\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.dashboard.review-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Dashboard\\ReviewTable\"\n  \"id\" => \"XZBaQDpWpbXEQNJQgurO\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/management\"\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "702ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2035403909 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2035403909\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"421 characters\">{&quot;data&quot;:{&quot;shopId&quot;:&quot;&quot;,&quot;shopBrandId&quot;:&quot;&quot;,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;XZBaQDpWpbXEQNJQgurO&quot;,&quot;name&quot;:&quot;admin.dashboard.review-table&quot;,&quot;path&quot;:&quot;management&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;props&quot;:[&quot;shopId&quot;,&quot;shopBrandId&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;8a27a495d46af12d7709f5b5cc501f4f606286fa3d14afe6dbb1f7b768917cd2&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"284 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJzaG9wQnJhbmRJZCI6IiIsInNob3BJZCI6IiJ9LHsicyI6ImFyciJ9XX0sIm1lbW8iOnsiaWQiOiJYZURuSHNPQkNBNEZ0ZHVnTTdJdSIsIm5hbWUiOiJfX21vdW50UGFyYW1zQ29udGFpbmVyIn0sImNoZWNrc3VtIjoiMTI3MThlNTQwMTkyNTBmZGNhODczZmEwYjM3ZDk1YTU4NGJjMTI4YjNkZmM0Zjc4ZWU3YmFkMzgyMDkxY2VhMCJ9</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-564669012 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">924</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjI3QTl0aFZTdU5Cd3o5VGd5Z24zSEE9PSIsInZhbHVlIjoibTl1VCtXWDVhL0xMaGNMc1JzeGNmWkwxQVg1VmI5K1I4TUJ2ci8xUHBXUjhwMi9YTjZjQk5TcmNjRmpKemtuL0VGOVZyYkU2ZnF6blIzWHZuV1FpdktKQnNiS0JZeldUb3pGQ3pCUWNDaEtTL1JsWk53bytUY2ZSdHY1cE12ekRlS0ppRWw2SDZVc0lsQXVyaWpGMW5BPT0iLCJtYWMiOiIyY2Y5ZTEyZmE5NjQyYmJkODE5NzIyMjFhODM3NDc5NjE2YTVlZDYxMzU1MmNkZmQ5YzJjYTI3OTA3NWE0NTA3IiwidGFnIjoiIn0%3D; ladybird_session=VYL4AtscJdT5gtzU7vz9eiCbIGhcbGn54iXnU9z5; XSRF-TOKEN=eyJpdiI6ImRuZEV1aGZwQ2J0SnhCVytNYTVuOVE9PSIsInZhbHVlIjoiNkY1RTNqeDg2OEI1NzBuSUhSelBxdFpUZUt2UkFabXJWWC94YnFacGJFbFl5RWxSbll1T2lkOHhRRXRScU1BaDA5THRhbFN5Skw0RWJrQUdjVm9uaEtFZ21PdjdPZU1yVWxvbmVzSUpQOEwyTTdhSmxDS0xlaklvMUF2eUV2QUsiLCJtYWMiOiIxZDBkOGI2MGY3MzhmNzYxYzk2ZDYyN2ZkOWJjMDRkNWVmODMzYjE0NDlmMDkwOGJjY2UyN2QxODdlOTA0M2QzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564669012\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1725994915 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">2||$2y$10$s0LI87x0oyRCf8LmrLWHVe24uMnHpU1z102HiqCMoYnnol50W9DsC</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725994915\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2120185728 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 02:26:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120185728\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1792371080 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792371080\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}