<?php

namespace App\Http\Controllers\Api;

use App\Repositories\UserRepository;
use App\Validators\UserValidator;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

class PostCodeApiController extends BaseApiController
{
    public function getAddressFromPostCode($postcode)
    {
        try {
            $url = config('config.api_postcode');
            $apiKey = config('config.api_postcode_key');

            $res = Http::withHeaders([
                'apikey' => $apiKey,
            ])->get($url . "{$postcode}");

            $result = $res->json();

            $this->setData($result[0] ?? []);

            return $this->response();
        } catch (\Throwable $exception) {
            logError($exception->getMessage() . PHP_EOL . $exception->getTraceAsString());
            $this->setStatusNG();
            $this->setCode(Response::HTTP_INTERNAL_SERVER_ERROR);
            return $this->response();
        }
    }
}
