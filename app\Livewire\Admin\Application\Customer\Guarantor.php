<?php

namespace App\Livewire\Admin\Application\Customer;

use App\Enums\CustomerTabEnum;
use App\Enums\InformationInputFlagEnum;
use App\Livewire\Admin\Application\Forms\GuarantorForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\CustomerRepository;
use App\Services\CustomerService;
use App\Services\PrefService;
use App\Services\ToastService;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use App\Traits\HandlesApplicationCustomer;

#[Layout('components.layouts.application-layout')]
class Guarantor extends BaseAdminPageComponent
{
    use HandlesApplicationCustomer;

    public GuarantorForm $guarantorForm;

    public function mount($customer_id, $application_id)
    {
        $this->page = CustomerTabEnum::GUARANTOR;
        $this->pageTitle .= '｜' . trans2('project_name');
        $this->initCustomerAndApplication($customer_id, $application_id);
        
        $customer = app(CustomerRepository::class)->getInfoApplicationCustomer($customer_id);

        if ($customer) {
            $this->guarantorForm->fillFromCustomer($customer);
        }
    }

    public function validateSave()
    {
        $this->guarantorForm->validate();
        $this->dispatch('app-guarantor-store', $this->guarantorForm);
    }

    #[On('app-guarantor-store')]
    public function store()
    {
        $params = $this->guarantorForm->all();

        if(isset($params['information_input_flag']) && $params['information_input_flag'] == InformationInputFlagEnum::DISABLED) {
            // save empty date gw
            $params = $this->clearDataGuarantorDisabled($params);
        }

        $customer = app(CustomerService::class)->update($this->customerId, $params);

        if ($customer) {
            app(ToastService::class)->createSuccess();
            // redirect to tab account
            return redirect()->route('admin.customer.application.setup.account', [
                'application_id' => $this->applicationId,
                'customer_id' => $customer->id,
            ]);
        }

        app(ToastService::class)->createError();
    }

    private function clearDataGuarantorDisabled($inputArray)
    {
        $informationInputFlag = $inputArray['information_input_flag'] ?? null;

        $result = array_fill_keys(array_keys($inputArray), null);

        $result['information_input_flag'] = $informationInputFlag;

        return $result;
    }

    public function render()
    {
        $this->dispatch('init-select2');
        return $this->viewLivewireAdmin('application.customer.guarantor', [
            'listPrefs' => app(PrefService::class)->getAllPrefs(),
        ]);
    }
}
