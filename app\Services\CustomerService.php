<?php

namespace App\Services;

use App\Models\Customer;
use App\Repositories\CustomerRepository;
use App\Repositories\CustomerShopRepository;
use Illuminate\Support\Facades\DB;

class CustomerService extends CustomService
{
    public function __construct(
        public CustomerRepository $customerRepository
    ) {
        parent::__construct();
    }

    public function search($dataSearch, $perPage, $isCSV = false)
    {
        return $this->customerRepository->getListForSearch($dataSearch, $perPage, $isCSV);
    }

    public function getCustomer($id)
    {
        return app(CustomerRepository::class)->find($id);
    }

    public function store($params)
    {
        DB::beginTransaction();
        try {
            $currentAdmin = getCurrentUser();
            $created = $this->customerRepository->create($params);

            if($currentAdmin->isStore()){
                $created->shops()->sync($currentAdmin->listShopIds());
            }
            if($currentAdmin->isBrand()){
                $created->brands()->sync($currentAdmin->listBrandIds());
            }

            DB::commit();
            return $created;
        } catch (\Exception $exception) {
            DB::rollBack();
            logError($exception->getMessage() . PHP_EOL . $exception->getTraceAsString());
            return false;
        }
    }

    public function update($id, $params)
    {
        return app(CustomerRepository::class)->update($id, $params);
    }

    public function searchCustomer($dataSearch, $perPage)
    {
        return $this->customerRepository->getListCustomerForSearch($dataSearch, $perPage);
    }
}
