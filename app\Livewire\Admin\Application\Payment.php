<?php

namespace App\Livewire\Admin\Application;

use App\Enums\ApplicationTabEnum;
use App\Enums\BonusFlagEnum;
use App\Enums\FeeTypeEnum;
use App\Livewire\Admin\Application\Forms\PaymentForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Services\ApplicationService;
use App\Services\ToastService;
use Carbon\Carbon;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;

#[Layout('components.layouts.application-layout')]
class Payment extends BaseAdminPageComponent
{

    public PaymentForm $paymentForm;

    public $applicationId;

    public $application = null;

    public $payment_start_month;

    public $remaining_amount;

    public $fee_amount;

    public $fee_total_amount;

    public $total_amount;

    public $first_month_payment_amount;

    public $second_month_payment_amount;

    public function __construct()
    {
        $this->page = ApplicationTabEnum::PAYMENT;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
        $this->dispatch('init-select2');
    }

    public function mount($application_id)
    {
        $this->applicationId = $application_id;

        $this->application = app()->make(ApplicationRepository::class)->find($application_id);

        if (empty($this->application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }

        // prepare data for edit
        $this->prepareDataUpdate();
    }

    private function prepareDataUpdate()
    {
        $this->paymentForm->fill($this->application->toArray());

        if($this->application?->bonus_payment_amount) {
            $this->paymentForm->bonus_payment_amount = format_float($this->application?->bonus_payment_amount);
        }

        if($this->application?->deposit) {
            $this->paymentForm->deposit = format_float($this->application?->deposit);
        }

        if ($this->application->fee_type?->value === FeeTypeEnum::COMPANY_PAID) {
            $this->paymentForm->payment_count = config('constant.MAX_PAYMENT_COUNT_APPLICATION');
        }

        $this->paymentForm->payment_start_month = $this->application->payment_start_month ? Carbon::parse($this->application->payment_start_month)->format('Y年n月') : null;

        if ($this->paymentForm->deposit == 0 || $this->paymentForm->deposit == '0.00') {
            $this->paymentForm->deposit = null;
        }
    }

    public function validateSave()
    {
        // prepare data for save
        if ($this->paymentForm?->bonus_payment_month2 == 0) {
            $this->paymentForm->bonus_payment_month2 = null;
        }
        if ($this->paymentForm?->bonus_flag == BonusFlagEnum::DISABLED) {
            $this->paymentForm->bonus_payment_amount = null;
            $this->paymentForm->bonus_payment_month1 = null;
            $this->paymentForm->bonus_payment_month2 = null;
            $this->paymentForm->bonus_payment_count = null;
        }
        if ($this->application->fee_type?->value === FeeTypeEnum::COMPANY_PAID) {
            $this->paymentForm->payment_count = config('constant.MAX_PAYMENT_COUNT_APPLICATION');
        }

        $this->paymentForm->validate();

        $this->store();
    }

    public function store()
    {
        $applicationId = $this->applicationId;

        $body = $this->paymentForm->toArray();
        $body['first_month_payment_amount'] = $this->first_month_payment_amount;
        $body['second_month_payment_amount'] = $this->second_month_payment_amount;

        $application = app()->make(ApplicationService::class)->createApplicationPayment($applicationId, $body);

        if ($application) {
            app(ToastService::class)->createSuccess();
            // redirect to tab service
            return redirect()->route('admin.customer.application.service', ['application_id' => $application->id]);
        }
        app(ToastService::class)->createError();
    }

    public function render()
    {
        $this->payment_start_month = $this->paymentForm->payment_start_month ?? '';

        return $this->viewLivewireAdmin('application.payment', [
            'application' => $this->application,
        ]);
    }

    /**
     * Auto set bonus_payment_month2 = bonus_payment_month1 + 6 months
     * @return void
     */
    public function setBonusPaymentSecondMonth()
    {
        if ($this->paymentForm?->bonus_payment_month1) {
            $this->paymentForm->bonus_payment_month2 = Carbon::createFromFormat('n', $this->paymentForm->bonus_payment_month1)->addMonths(6)->format('n');
        }
    }
}
