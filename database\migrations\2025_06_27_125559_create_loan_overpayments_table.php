<?php

use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('loan_overpayments', function (BlueprintCustom $table) {
            $table->id();
            $table->integer('customer_id')->comment('顧客ID')->index();
            $table->integer('application_id')->comment('申込ID')->index();
            $table->integer('loan_payment_id')->comment('ローン入金ID')->index();
            $table->decimal('amount', 10, 2)->comment('金額');
            $table->char('refunded_flag', 1)->nullable()->comment('返金済フラグ')->nullable();
            $table->date('payment_date')->comment('過入金解消日')->nullable();
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SchemaCustom::dropIfExists('loan_overpayments');
    }
};
