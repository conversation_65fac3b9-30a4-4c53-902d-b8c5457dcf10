@extends('livewire.admin.customer.detail.index')
@section('detail-tab')
@push('styles')
    <style>
        .disabled-link {
            opacity: 0.6; 
            cursor: not-allowed;
            pointer-events: none;
        }
    </style>
@endpush
    @php
        $confirmationDocFlagText = \App\Enums\ConfirmationDocFlagEnum::texts();
        $customerSexText = \App\Enums\SexEnum::texts();
        $customerRelationshipFlag = \App\Enums\RelationshipFlagEnum::texts();
    @endphp
    <div class="card mb-3">
        <div class="card-body">
            <dl class="horizon-definition-list mb-4">
                <dt class="small">{{ trans2('screens.customer.detail.application_tab') }}</dt>
                <dd class="small">{{ trans2('screens.customer.detail.index.application_id') }}
                    :{{ $application?->id }}</dd>
            </dl>
            <div class="d-flex justify-content-center mb-5">
                <ul class="page-content-tabs nav nav-tabs">
                    @if($application?->status?->value !== \App\Enums\ApplicationStatusEnum::IN_PROGRESS)
                        <li class="nav-item w-px-190">
                            <a href="{{ getRoute('customer.application.memo.index', [$customer?->id, $application?->id]) }}"
                               class="nav-link">{{ trans2('screens.customer.detail.index.tab_memo') }}</a>
                        </li>
                    @endif
                    <li class="nav-item w-px-190">
                        <a class="nav-link active">{{ trans2('screens.customer.detail.index.tab_detail') }}</a>
                    </li>
                </ul>
            </div>
            <div class="container-min">
                <div class="d-flex justify-content-end mb-4 pt-2">
                    <div class="d-flex align-items-center">
                        <a href="javascript:void(0);" id="btn-print" class="btn-print me-4"
                           data-export-url="{{ getRoute('customer.application.export-pdf', [$application->id]) }}">
                            <span class="btn-text">{{ trans2('screens.customer.detail.index.detail.btn_print') }}</span>
                            <span class="spinner-border spinner-border-sm align-middle ms-1 d-none"
                                  id="btn-print-loading" role="status" aria-hidden="true"></span>
                        </a>

                        @php
                            $isDisabled = $applicationInspectionStatuses?->first()?->status?->value === \App\Enums\ApplicationInspectionStatusEnum::APPROVED;
                        @endphp
                        <a href="{{ getRoute('customer.application.brand.edit', [$application->id]) }}"
                           class="btn btn-dark {{ $isDisabled ? 'disabled-link' : '' }}"
                           @disabled($isDisabled)
                           target="_blank">{{ trans2('screens.customer.detail.index.detail.btn_edit_application') }}</a>
                    </div>
                </div>
                <h3 class="heading-3 mt-0">{{ trans2('screens.customer.detail.index.detail.type_of_service_label') }}</h3>
                <table class="table table-border">
                    <thead>
                    <tr>
                        <th>{{ trans2('screens.customer.detail.index.detail.product_name') }}</th>
                        <th class="text-center">{{ trans2('screens.customer.detail.index.detail.quantity') }}</th>
                        <th class="w-px-100">{{ trans2('screens.customer.detail.index.detail.product_amount') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($application?->applicationCourses as $appCourse)
                        <tr>
                            <td>{{ $appCourse?->course->name_application ?? $appCourse?->course->name_management }}</td>
                            <td class="text-center">{{ $appCourse?->count }}</td>
                            <td class="text-end text-nowrap">
                                {{ formatCurrency($appCourse?->amount) }}
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.total_amount_label') }}</h3>
                <table class="table table-border mb-5">
                    <thead>
                    <tr>
                        <th>{{ trans2('screens.customer.detail.index.detail.fee') }}</th>
                        <th class="w-px-100">{{ trans2('screens.customer.detail.index.detail.product_amount') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>①{{ trans2('screens.customer.detail.index.detail.sub_total_amount') }}</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->sub_total_amount) }}
                        </td>
                    </tr>
                    <tr>
                        <td>②{{ trans2('screens.customer.detail.index.detail.deposit') }}</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->deposit) }}
                        </td>
                    </tr>
                    <tr>
                        <td>③{{ trans2('screens.customer.detail.index.detail.remaining_amount') }}（①－②）</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->remaining_amount) }}
                        </td>
                    </tr>
                    <tr>
                        <td>④{{ trans2('screens.customer.detail.index.detail.fee_amount') }}</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->fee_amount) }}
                        </td>
                    </tr>
                    <tr>
                        <td>⑤{{ trans2('screens.customer.detail.index.detail.year_rate') }}</td>
                        <td class="text-end text-nowrap">{{ $application?->year_rate ? format_float($application->year_rate) . '%' : '' }}</td>
                    </tr>
                    <tr>
                        <td>⑥{{ trans2('screens.customer.detail.index.detail.fee_total_amount') }}（③＋④）</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->fee_total_amount) }}
                        </td>
                    </tr>
                    <tr>
                        <td>⑦{{ trans2('screens.customer.detail.index.detail.total_amount') }}（②＋⑥）</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->total_amount) }}
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-6">
                        @php
                            $paymentStartMonth = $application?->payment_start_month ? \Carbon\Carbon::createFromFormat('Ym',$application->payment_start_month) : null;
                            $paymentEndMonth = $application?->payment_last_month ? \Carbon\Carbon::createFromFormat('Ym',$application->payment_last_month) : null;
                        @endphp
                        <table class="table table-details mb-5">
                            <tbody>
                            <tr>
                                <th class="w-50">{{ trans2('screens.customer.detail.index.detail.payment_start_month') }}</th>
                                <td class="text-end">{{ $paymentStartMonth ? $paymentStartMonth->format('Y') . '年' . $paymentStartMonth?->format('n') . '月' : '' }}</td>
                            </tr>
                            <tr>
                                <th class="w-50">{{ trans2('screens.customer.detail.index.detail.payment_last_month') }}</th>
                                <td class="text-end">{{ $paymentEndMonth ? $paymentEndMonth->format('Y') . '年' . $paymentEndMonth?->format('n') . '月' : '' }}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-6">
                        <table class="table table-details mb-5">
                            <tbody>
                            <tr>
                                <th class="w-50">{{ trans2('screens.customer.detail.index.detail.payment_count') }}</th>
                                <td class="text-end">{{ $application?->payment_count }}{{ trans2('times') }}</td>
                            </tr>
                            <tr>
                                <th class="w-50">{{ trans2('screens.customer.detail.index.detail.bonus_payment_count') }}</th>
                                <td class="text-end">{{ $application?->bonus_payment_count }}{{ trans2('times') }}</td>
                            </tr>
                            <tr>
                                <th>{{ trans2('screens.customer.detail.index.detail.bonus_payment_month') }}</th>
                                <td class="text-end">
                                    @if($application?->bonus_flag?->value === \App\Enums\BonusFlagEnum::ENABLED)
                                        {{ $application?->bonus_payment_month1 }}月,{{ $application?->bonus_payment_month2 ? $application?->bonus_payment_month2 . '月' : '' }}
                                    @endif
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <table class="table table-border">
                    <thead>
                    <tr>
                        <th>{{ trans2('screens.customer.detail.index.detail.fee') }}</th>
                        <th class="w-px-100">{{ trans2('screens.customer.detail.index.detail.product_amount') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>{{ trans2('screens.customer.detail.index.detail.bonus_payment_amount') }}</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->bonus_payment_amount) }}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ trans2('screens.customer.detail.index.detail.total_bonus_payment_amount') }}</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->getBonusAdditionAmountAttribute()) ?? '¥' . 0 }}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ trans2('screens.customer.detail.index.detail.first_month_payment_amount') }}</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->first_month_payment_amount) }}
                        </td>
                    </tr>
                    <tr>
                        <td>{{ trans2('screens.customer.detail.index.detail.second_month_payment_amount') }}</td>
                        <td class="text-end text-nowrap">
                            {{ formatCurrency($application?->second_month_payment_amount) }}
                        </td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.service_section') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.service_handover_date') }}</th>
                        <td>{{ $application?->service_handover_date ? \Carbon\Carbon::parse($application?->service_handover_date)->format('Y年m月d日') : '' }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.service_count') }}</th>
                        <td>{{ $application?->service_count }}{{ trans2('times') }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.service_date') }}</th>
                        <td>{{ $application?->service_start_date ? \Carbon\Carbon::parse($application?->service_start_date)->format('Y年m月d日') : '' }}
                            ～{{ $application?->service_end_date ? \Carbon\Carbon::parse($application?->service_end_date)->format('Y年m月d日') : '' }}</td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.inden_section') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.inden_confirm_doc') }}</th>
                        <td>
                            @if($application->confirmation_doc_flag?->value == \App\Enums\ConfirmationDocFlagEnum::OTHER)
                                {{ $application?->confirmation_doc_other }}
                            @else
                                {{ $application?->confirmation_doc_flag?->text }}
                            @endif
                        </td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.customer_section') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_name') }}</th>
                        <td>{{ $application?->customer?->last_name }} {{ $application?->customer?->first_name }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_name_kana') }}</th>
                        <td>{{ $application?->customer?->last_name_kana }} {{ $application?->customer?->first_name_kana }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_sex') }}</th>
                        <td>{{ $application?->customer?->sex?->text }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_birthday') }}</th>
                        <td>{{ $application?->customer?->birthday ? \Carbon\Carbon::parse($application?->customer?->birthday)->format('Y/m/d') : '' }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_email') }}</th>
                        <td>{{ $application?->customer?->email }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_tel') }}</th>
                        <td>{{ getFormatTel($application?->customer?->tel1, $application?->customer?->tel2, $application?->customer?->tel3) }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_zip_pref') }}</th>
                        <td>{{ getFormatZipCode($application?->customer?->zip1, $application?->customer?->zip2) }}
                            <br>{{ $application?->customer?->pref?->name }}{{ $application?->customer?->city }}{{ $application?->customer?->address }}{{ $application?->customer?->building }}
                        </td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.customer_emergency') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_name') }}</th>
                        <td>{{ $application?->customer?->emergency_last_name }} {{ $application?->customer?->emergency_first_name }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_name_kana') }}</th>
                        <td>{{ $application?->customer?->emergency_last_name_kana }} {{ $application?->customer?->emergency_first_name_kana }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_zip_pref') }}</th>
                        <td>{{ getFormatZipCode($application?->customer?->emergency_zip1, $application?->customer?->emergency_zip2) }}
                            <br>{{ $application?->customer?->emergencyPref?->name }}{{ $application?->customer?->emergency_city }}{{ $application?->customer?->emergency_address }}{{ $application?->customer?->emergency_building }}
                        </td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_tel') }}</th>
                        <td>{{ getFormatTel($application?->customer?->emergency_tel1, $application?->customer?->emergency_tel1, $application?->customer?->emergency_tel3) }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_emergency_relationship_flag') }}</th>
                        <td>
                            @if($application?->customer?->relationship_flag?->value == \App\Enums\RelationshipFlagEnum::OTHER)
                                {{ $application->customer->relationship_other }}
                            @else
                                {{ $application->customer->relationship_flag?->text }}
                            @endif
                        </td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.finance_status') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_income') }}</th>
                        <td>{{ isset($application?->customer?->annual_income) ? $application?->customer?->annual_income . trans2('JPY2') : '' }}</td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.place_of_work_label') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.company_name') }}</th>
                        <td>{{ $application?->customer?->company_name }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.company_name_kana') }}</th>
                        <td>{{ $application?->customer?->company_name_kana }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.company_zip_pref') }}</th>
                        <td>{{ getFormatZipCode($application?->customer?->company_zip1, $application?->customer?->company_zip2) }}
                            <br>{{ $application?->customer?->companyPref?->name }}{{ $application?->customer?->company_city }}{{ $application?->customer?->company_address }}{{ $application?->customer?->company_building }}
                        </td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_tel') }}</th>
                        <td>{{ getFormatTel($application?->customer?->company_tel1, $application?->customer?->company_tel2, $application?->customer?->company_tel3) }}</td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.guarantor_label') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.information_input_flag') }}</th>
                        <td>{{ $application?->customer?->information_input_flag?->text }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.gw_name') }}</th>
                        <td>{{ $application?->customer?->gw_last_name }} {{ $application?->customer?->gw_first_name }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.gw_name_kana') }}</th>
                        <td>{{ $application?->customer?->gw_last_name_kana }} {{ $application?->customer?->gw_first_name_kana }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.gw_sex') }}</th>
                        <td>{{ $customerSexText[$application?->customer?->gw_sex] ?? '' }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.gw_birthday') }}</th>
                        <td>{{ $application?->customer?->gw_birthday ? \Carbon\Carbon::parse($application->customer->gw_birthday)->format('Y/m/d') : '' }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.gw_tel') }}</th>
                        <td>{{ getFormatTel($application?->customer?->gw_tel1, $application?->customer?->gw_tel2, $application?->customer?->gw_tel3) }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.gw_relationship_flag') }}</th>
                        <td>
                            @if($application?->customer?->gw_relationship_flag === \App\Enums\RelationshipFlagEnum::OTHER)
                                {{ $application?->customer?->gw_relationship_other }}
                            @else
                                {{ $customerRelationshipFlag[$application?->customer?->gw_relationship_flag] ?? '' }}
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.gw_zip_pref') }}</th>
                        <td>{{ getFormatZipCode($application?->customer?->gw_zip1, $application?->customer?->gw_zip2) }}<br>
                            {{ $application?->customer?->gwPref?->name }}{{ $application?->customer?->gw_city }}{{ $application?->customer?->gw_address }}{{ $application?->customer?->gw_building }}
                        </td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.guarantor_employment_label') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.company_name') }}</th>
                        <td>{{ $application?->customer?->gw_company_name }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.company_name_kana') }}</th>
                        <td>{{ $application?->customer?->gw_company_name_kana }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_zip_pref') }}</th>
                        <td>{{ getFormatZipCode($application?->customer?->gw_company_zip1, $application?->customer?->gw_company_zip2) }}<br>
                            {{ $application?->customer?->gwCompanyPref?->name }}{{ $application?->customer?->gw_company_city }}{{ $application?->customer?->gw_company_address }}{{ $application?->customer?->gw_company_building }}
                        </td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_tel') }}</th>
                        <td>{{ getFormatTel($application?->customer?->gw_company_tel1, $application?->customer?->gw_company_tel2, $application?->customer?->gw_company_tel3) }}</td>
                    </tr>
                    </tbody>
                </table>
                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.account_information_label') }}</h3>
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.account_name') }}</th>
                        <td>{{ $application?->customer?->bank_account_name }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.account_name_kana') }}</th>
                        <td>{{ $application?->customer?->bank_account_name_kana }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.account_bank_flag') }}</th>
                        <td>{{ $application?->customer?->bank_flag?->text }}</td>
                    </tr>

                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.bank_name') }}</th>
                        <td>{{ $application?->customer?->bank_name }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.branch_name') }}</th>
                        <td>{{ $application?->customer?->branch_name }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.bank_account_type') }}</th>
                        <td>{{ $application?->customer?->bank_account_type?->text }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.bank_account_number') }}</th>
                        <td>{{ $application?->customer?->bank_account_number }}</td>
                    </tr>
                    </tbody>
                </table>

                @if($application?->bank_flag?->value === \App\Enums\BankFlagEnum::JAPAN)
                    <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.japan_bank_label') }}</h3>
                    <table class="table table-details">
                        <tbody>
                        <tr>
                            <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.bank_account_mark1') }}</th>
                            <td>{{ $application?->customer?->bank_account_mark1 }}
                                -{{ $application?->customer?->bank_account_mark2 }}</td>
                        </tr>
                        <tr>
                            <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.bank_account_mark2') }}</th>
                            <td>{{ $application?->customer?->bank_account_number }}</td>
                        </tr>
                        </tbody>
                    </table>
                @endif

                <h3 class="heading-3">{{ trans2('screens.customer.detail.index.detail.application_contact_label') }}</h3>
                <table class="table table-details mb-5">
                    <tbody>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_contact_flag') }}</th>
                        <td>{{ $application?->customer?->contact_flag?->text }}</td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_hope_date1') }}</th>
                        <td>
                            {{ $application?->customer?->contact_hope_date1 ? \Carbon\Carbon::parse($application?->customer?->contact_hope_date1)->format('Y年m月d日') : '' }}
                            {{ $application?->customer?->contact_hope_start_time1 ? formatTime($application?->customer?->contact_hope_start_time1) : '' }}
                            〜
                            {{ $application?->customer?->contact_hope_end_time1 ? formatTime($application?->customer?->contact_hope_end_time1) : '' }}
                            {{ trans2('around_times') }}
                        </td>
                    </tr>
                    <tr>
                        <th class="w-px-250">{{ trans2('screens.customer.detail.index.detail.customer_hope_date2') }}</th>
                        <td>
                            {{ $application?->customer?->contact_hope_date2 ? \Carbon\Carbon::parse($application?->customer?->contact_hope_date2)->format('Y年m月d日') : '' }}
                            {{ $application?->customer?->contact_hope_start_time2 ? formatTime($application?->customer?->contact_hope_start_time2) : '' }}
                            〜
                            {{ $application?->customer?->contact_hope_end_time2 ? formatTime($application?->customer?->contact_hope_end_time2) : '' }}
                            {{ trans2('around_times') }}
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="box mb-3">
                    <div class="box-header">
                        <h3 class="box-title">{{ trans2('screens.customer.detail.index.detail.customer_signature') }}</h3>
                    </div>
                    <div class="box-body pt-4">
                        <p class="text-center fs-20 mb-3">{{ trans2('screens.customer.detail.index.detail.customer_signature_message') }}</p>
                        <div class="customers-sign-area">
                            @if($application?->sign_image_url)
                                <img src="{{ $application?->sign_image_url }}" alt="">
                            @endif
                        </div>
                    </div>
                </div>
                <div class="text-end">
                    <span
                        class="small me-4">{{ transm('_default.attributes.ins_date') }}：{{ $application?->ins_date ? \Carbon\Carbon::parse($application->ins_date)->format('Y/m/d') : '' }}</span>
                    <span
                        class="small me-4">{{ transm('_default.attributes.upd_date') }}：{{ $application?->upd_date ? \Carbon\Carbon::parse($application->upd_date)->format('Y/m/d') : '' }}</span>
                    <button type="button" class="small btn-text fc-primary" data-bs-toggle="modal"
                            data-bs-target="#updateHistoryModal">{{ trans2('button.history') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(function () {
            $('#btn-print').on('click', function (e) {
                e.preventDefault();
                var $btn = $(this);
                var $text = $btn.find('.btn-text');
                var $spinner = $('#btn-print-loading');
                var url = $btn.data('export-url');
                $text.addClass('d-none');
                $spinner.removeClass('d-none');
                $.ajax({
                    url: url,
                    method: 'GET',
                    dataType: 'json',
                    success: function (data) {
                        if (data.url) {
                            window.open(data.url, '_blank');
                        }
                    },
                    error: function () {
                        // log error
                        if (window.notyf) {
                            window.notyf.error("{{ __('messages.export_failed') }}");
                        }
                    },
                    complete: function() {
                        $text.removeClass('d-none');
                        $spinner.addClass('d-none');
                    }
                });
            });
        });
    </script>
@endpush

