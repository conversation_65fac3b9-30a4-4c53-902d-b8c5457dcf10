<?php

namespace App\Enums;

final class LoanTransactionType<PERSON><PERSON> extends Enum
{
    const DEPOSIT    = 1; // 入金
    const WITHDRAWAL = 2; // 出金
    const CANCEL     = 9; // 取消

    public static function texts(): array
    {
        return [
            self::DEPOSIT => trans2('LoanTransactionTypeEnum.DEPOSIT'),
            self::WITHDRAWAL => trans2('LoanTransactionTypeEnum.WITHDRAWAL'),
            self::CANCEL => trans2('LoanTransactionTypeEnum.CANCEL'),
        ];
    }

    public static function getActiveTransactionTypes()
    {
        return [
            self::DEPOSIT => trans2('LoanTransactionTypeEnum.DEPOSIT'),
            self::WITHDRAWAL => trans2('LoanTransactionTypeEnum.WITHDRAWAL'),
        ];
    }

    public static function getActiveTransactionTypeValues()
    {
        return [
            self::DEPOSIT,
            self::WITHDRAWAL,
        ];
    }
}
