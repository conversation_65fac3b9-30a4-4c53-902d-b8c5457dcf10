{"__meta": {"id": "01K0BQ2A03PM54B6VB04RWGJTC", "datetime": "2025-07-17 17:12:28", "utime": **********.547823, "method": "GET", "uri": "/management/login/code", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[17:12:28] LOG.debug: (Time: 19.31) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.537897, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.320183, "end": **********.547838, "duration": 0.22765493392944336, "duration_str": "228ms", "measures": [{"label": "Booting", "start": **********.320183, "relative_start": 0, "end": **********.486789, "relative_end": **********.486789, "duration": 0.*****************, "duration_str": "167ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.486798, "relative_start": 0.*****************, "end": **********.54784, "relative_end": 2.1457672119140625e-06, "duration": 0.061042070388793945, "duration_str": "61.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.494071, "relative_start": 0.****************, "end": **********.496012, "relative_end": **********.496012, "duration": 0.0019409656524658203, "duration_str": "1.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.54569, "relative_start": 0.*****************, "end": **********.545977, "relative_end": **********.545977, "duration": 0.00028705596923828125, "duration_str": "287μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET management/login/code", "middleware": "admin, locale, guest", "uses": "Closure() {#490\n  class: \"Livewire\\Volt\\VoltManager\"\n  this: Livewire\\Volt\\VoltManager {#493 …}\n  use: {\n    $componentName: \"App\\Livewire\\Admin\\Auth\\Code\\Index\"\n  }\n  file: \"C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\VoltManager.php\"\n  line: \"34 to 41\"\n}", "as": "admin.login.code", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>"}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019309999999999997, "accumulated_duration_str": "19.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.518831, "duration": 0.019309999999999997, "duration_str": "19.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/management/login/code_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01K0BQ29C9QPVYXTKWRQCBMES7&op=get\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/management/login/code", "action_name": "admin.login.code", "controller_action": "Closure", "uri": "GET management/login/code", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>", "middleware": "admin, locale, guest", "duration": "237ms", "peak_memory": "26MB", "response": "Redirect to http://127.0.0.1:8000/management", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1985400524 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1985400524\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-677293210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-677293210\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-545457379 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/management/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1182 characters\">cookie_per_page=eyJpdiI6IkV4b3ZySWVaTjI0czgxRmpycEc3MHc9PSIsInZhbHVlIjoiQUk4ak14MytpVTdFUjBubkplSWZJK25Rc0FWbXV5K2pabU5nU0tuVUNycWx3UXpoZjNqdW9NNWlzcmJ3bmsvOCIsIm1hYyI6IjI5ZjgzNWFiMDIxNTg3MDc3MGU4YzFlNDA3OGVjNDg4N2ExODA5MmU2NDJjMzMyMzdmMWMxZDVkY2RjMDA1ZDMiLCJ0YWciOiIifQ%3D%3D; leasemart_session=w8w1HYeAl52hZGLblPhiSoOKeze2Xr5qGWmGpJqZ; XSRF-TOKEN=eyJpdiI6InBKY3ZGMjF1RitkOU5KcVRIb3ZOQVE9PSIsInZhbHVlIjoiSThYUVplTjBsZG5YZzlKRG5DdHRvSm16L2FwekdTTEZJVStxTUtQMmYrbFNuUFU2eFpybDhvbHc1UWlmcnd5K3JRSDZORjg0NlFXVytyanpZMWxweUlIZGp4bU5oMnpZMTc4cGJPbFhIMXh2blFtQWlsWlNCUlFldEIzSE50U08iLCJtYWMiOiJkNDUwYTk2ZTJlMjg4MDAzNGU1NzI2Yzg1Yjc3MGQ2ZDQzNTM0MGU3YWUwZjc1MDI0N2FiMjQzYjdjZWQ5YTM2IiwidGFnIjoiIn0%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImlqR1hKc3kxU295eW9idG9McWtTeVE9PSIsInZhbHVlIjoiWkQ5SzNBUnA4SlhaUHRZLzk2Rm5PMTVrWm1IYUF0Y2xkanBGSWphOEhkbVZ0QUlTRzJRUG5SczFsNzZMc3VSdHlDSUtIOHdEWmRmbWV4Y2tzQWo2VzlEVVk3SEJIa2tHdjlOcDVMcWw2aXVsQ3pZOHd6R3pBRXA1SklMYzlieUZuTVU0ejVqVkRKU01iUjV5NzdTN1BnPT0iLCJtYWMiOiJmZThlMThjNTZmMjk0ZGY4NWU5NGMyZmYxMWJiMzJkMzhjNGYwMGQ4ZGRjNGU0MTlmYzc0N2RjNDM0ZTZkNmIwIiwidGFnIjoiIn0%3D; ladybird_session=tGFewrUFVXwdbXowWrlw5IwCNUgVWIGWxWStuDZg</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545457379\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1005109295 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>leasemart_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005109295\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1860686496 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 08:12:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJUdzRQSmVpVVZXd1BUbFp4dTAxUnc9PSIsInZhbHVlIjoiQWlydWY2Z3d2bk8zQmxKcGZtaysyZWsrOHZHVWVFRHA0L0dOYnpPMnNZZGNOaDlFdGhUZFF6Tk9yQ0F4ZnVpeDVRV0s5ZGdsT1RQSUcvMlBQUEpaUCtLUzRkcUlzQm05QkdhMzIyODN4NEpSZkpQbUJwWEQvNXpqOW9ETW1QeTEiLCJtYWMiOiI5MDUwNjczNDJjNDI0YTEzZDVmZDI1NGRiMzg3OWI5NTAxZDRkYjkwOTlkODMyNTEzMDQ5NTkyMDY4MTNmNjAyIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 10:12:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJUdzRQSmVpVVZXd1BUbFp4dTAxUnc9PSIsInZhbHVlIjoiQWlydWY2Z3d2bk8zQmxKcGZtaysyZWsrOHZHVWVFRHA0L0dOYnpPMnNZZGNOaDlFdGhUZFF6Tk9yQ0F4ZnVpeDVRV0s5ZGdsT1RQSUcvMlBQUEpaUCtLUzRkcUlzQm05QkdhMzIyODN4NEpSZkpQbUJwWEQvNXpqOW9ETW1QeTEiLCJtYWMiOiI5MDUwNjczNDJjNDI0YTEzZDVmZDI1NGRiMzg3OWI5NTAxZDRkYjkwOTlkODMyNTEzMDQ5NTkyMDY4MTNmNjAyIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 10:12:28 GMT; path=/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860686496\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1415332420 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/management/login/code_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01K0BQ29C9QPVYXTKWRQCBMES7&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415332420\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/management/login/code", "action_name": "admin.login.code", "controller_action": "Closure"}, "badge": "302 Found"}}