<div>
    <h1 class="application-page-title">6.お客様登録画面 / 財務状況・勤務先情報</h1>
    <h3 class="heading-3">財務状況</h3>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">本人年収<span class="required-icon">必須</span></th>
                <td>
                    <div class="form-parts-unit d-inline-block">
                        <input type="text" name="annual_income" class="form-control w-px-230 form-required"
                            wire:model.lazy="workForm.annual_income" x-data x-ref="incomeInput"
                            x-on:keydown="if ($event.key === '.' || $event.key === ',') $event.preventDefault()"
                            x-on:input="$refs.incomeInput.value = $refs.incomeInput.value.replace(/[.,]/g, '')"
                            id="inputIncome" />
                        <div class="form-unit">万円</div>
                    </div>
                    <div>
                        @error('workForm.annual_income') <span class="message-error">{{ $message}}</span>@enderror
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <h3 class="heading-3 pt-4">勤務先</h3>
    <table class="table table-edit">
        <tbody>
            <tr class="check-required-row">
                <th>勤務先名称</th>
                <td>
                    <input type="text" name="" value="" class="form-control" placeholder=""
                        wire:model="workForm.company_name" maxlength="128">
                    <div>
                        @error('workForm.company_name') <span class="message-error">{{ $message}}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr class="check-required-row">
                <th>勤務先名称（フリガナ）</th>
                <td>
                    <input type="text" name="company_name_kana" value="" class="form-control" placeholder=""
                        wire:model='workForm.company_name_kana' maxlength="128">
                    <div>
                        @error('workForm.company_name_kana') <span class="message-error">{{ $message}}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr class="check-required-row">
                <th>郵便番号</th>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <input type="text" name="zip1" value="" class="form-control w-px-150" maxlength="4"
                                wire:model="workForm.company_zip1" maxlength="4">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="zip2" value="" class="form-control w-px-150" maxlength="4"
                                wire:model="workForm.company_zip2" maxlength="4">
                        </div>
                    </div>
                    <div>
                        <div>
                            @error('workForm.company_zip1') <span class="message-error">{{ $message}}</span>@enderror
                        </div>
                        <div>
                            @error('workForm.company_zip2') <span class="message-error">{{ $message}}</span>@enderror
                        </div>
                    </div>
                </td>
            </tr>
            <tr class="check-required-row">
                <th>都道府県</th>
                <td>
                    <div wire:ignore>
                        <select name="pref_id" class="form-select2 " wire:model="workForm.company_pref_id"
                            style="width:280px;" data-placeholder="{{ trans2('select_default') }}">
                            <option value=""></option>
                            @foreach ($listPrefs as $key => $pref)
                            <option value="{{ data_get($pref,'id') }}">{{ data_get($pref,'name') }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        @error('workForm.company_pref_id') <span class="message-error">{{ $message}}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr class="check-required-row">
                <th>市区町村</th>
                <td>
                    <input type="text" name="city" value="" class="form-control" wire:model="workForm.company_city"
                        maxlength="1000">
                    <div>
                        @error('workForm.company_city') <span class="message-error">{{ $message}}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr class="check-required-row">
                <th>町名・番地</th>
                <td>
                    <input type="text" name="address" value="" class="form-control"
                        wire:model="workForm.company_address" maxlength="1000">
                    <div>
                        @error('workForm.company_address') <span class="message-error">{{ $message}}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th>建物名・部屋番号</th>
                <td>
                    <input type="text" name="building" value="" class="form-control"
                        wire:model="workForm.company_building" maxlength="1000">
                    <div>
                        @error('workForm.company_building') <span class="message-error">{{ $message}}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr class="check-required-row">
                <th>電話番号</th>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <input type="text" name="" value="" class="form-control w-px-150"
                                wire:model="workForm.company_tel1" maxlength="32">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="" value="" class="form-control w-px-150"
                                wire:model="workForm.company_tel2" maxlength="32">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="" value="" class="form-control w-px-150"
                                wire:model="workForm.company_tel3" maxlength="32">
                        </div>
                    </div>
                    <div>
                        <div>
                            @error('workForm.company_tel1') <span class="message-error">{{ $message}}</span>@enderror
                        </div>
                        <div>
                            @error('workForm.company_tel2') <span class="message-error">{{ $message}}</span>@enderror
                        </div>
                        <div>
                            @error('workForm.company_tel3') <span class="message-error">{{$message}}</span>@enderror
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button wire:click.prevent="validateSave" class="btn btn-dark btn-large">{{ trans2('button.next') }}</button>
    </div>
</div>
@include('livewire.admin.application.customer.script')