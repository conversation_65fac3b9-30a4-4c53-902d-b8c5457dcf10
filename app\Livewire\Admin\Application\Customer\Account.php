<?php

namespace App\Livewire\Admin\Application\Customer;

use App\Enums\BankFlagEnum;
use App\Enums\CustomerTabEnum;
use App\Livewire\Admin\Application\Forms\AccountForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\BankBranchRepository;
use App\Repositories\BankRepository;
use App\Repositories\CustomerRepository;
use App\Services\CustomerService;
use App\Services\PrefService;
use App\Services\ToastService;
use App\Traits\HandlesApplicationCustomer;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;

#[Layout('components.layouts.application-layout')]
class Account extends BaseAdminPageComponent
{
    use HandlesApplicationCustomer;

    public AccountForm $accountForm;

    public function mount($customer_id, $application_id)
    {
        $this->page = CustomerTabEnum::ACCOUNT;
        $this->pageTitle .= '｜' . trans2('project_name');
        $this->initCustomerAndApplication($customer_id, $application_id);

        $customer = app(CustomerRepository::class)->getInfoApplicationCustomer($customer_id);

        if ($customer) {
            $this->accountForm->fillFromCustomer($customer);
        }
    }

    public function validateSave()
    {
        $this->accountForm->validate();

        if ($this->accountForm->bank_flag == BankFlagEnum::JAPAN) {
            $this->accountForm->bank_account_number = $this->accountForm->bank_account_number_1;
        } elseif ($this->accountForm->bank_flag == BankFlagEnum::OTHER) {
            $this->accountForm->bank_account_number = $this->accountForm->bank_account_number_2;
        }

        $this->dispatch('app-account-store', $this->accountForm);
    }

    #[On('app-account-store')]
    public function store()
    {
        $params = $this->accountForm->all();

        $customer = app(CustomerService::class)->update($this->customerId, $params);

        if ($customer) {
            app(ToastService::class)->createSuccess();
            // redirect to tab contact
            return redirect()->route('admin.customer.application.setup.contact', [
                'application_id' => $this->applicationId,
                'customer_id' => $customer->id,
            ]);
        }

        app(ToastService::class)->createError();
    }

    public function render()
    {
        $this->dispatch('init-select2');
        return $this->viewLivewireAdmin('application.customer.account', [
            'listPrefs' => app(PrefService::class)->getAllPrefs(),
        ]);
    }

    public function updateBankNameByCode()
    {
        if (empty($this->accountForm->bank_code)) {
            return;
        }
        $bankName = app(BankRepository::class)->getBankNameByCode($this->accountForm->bank_code);
        $this->accountForm->bank_name = $bankName['name'] ?? '';
    }

    public function updateBranchNameByCode()
    {
        if (empty($this->accountForm->bank_code) || empty($this->accountForm->branch_code)) {
            return;
        }
        $branchName = app(BankBranchRepository::class)->getbranchNameByCode($this->accountForm->bank_code, $this->accountForm->branch_code);
        $this->accountForm->branch_name = $branchName['name'] ?? '';
    }
}
