<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\FeeTypeEnum;
use App\Livewire\Base\BaseAdminForm;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Reactive;

class BrandForm extends BaseAdminForm
{
    public $shop_brand_id = '';

    public $staff_name = '';

    public $fee_type = FeeTypeEnum::COMPANY_PAID;

    public $product_general = [
        [
            'course_id' => '',
            'quantity' => 0,
            'amount' => '',
        ]
    ];

    public $product_optional = [
        [
            'course_id' => '',
            'quantity' => 0,
            'amount' => '',
        ]
    ];

    public function rules()
    {
        return [
            'shop_brand_id' => 'required|exists:shop_brands,id',
            'staff_name' => 'required|string|max:128',
            'fee_type' => ['required', Rule::in(FeeTypeEnum::getValues())],
            'product_general' => 'required|array',
            'product_general.*.course_id' => 'required:exists:courses,id',
            'product_general.*.quantity' => 'required|integer|min:0',
            'product_general.*.amount' => 'required|numeric|min:0',
            'product_optional' => 'nullable|array',
            'product_optional.*.course_id' => 'nullable:exists:courses,id',
            'product_optional.*.quantity' => 'nullable|integer|min:0',
            'product_optional.*.amount' => 'nullable|numeric|min:0',
        ];
    }

    public function getValidationAttributes()
    {
        return __('models.applications.attributes');
    }
}
