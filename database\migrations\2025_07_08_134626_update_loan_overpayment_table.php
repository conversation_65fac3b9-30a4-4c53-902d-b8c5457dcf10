<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('loan_overpayments', function (Blueprint $table) {
            $table->integer('loan_refund_id')->comment('ローン返金ID')->index()->after('loan_payment_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('loan_overpayments', function (Blueprint $table) {
            $table->dropColumn('loan_refund_id');
        });
    }
};
