<?php

namespace App\Console\Commands;

use App\Enums\PaymentStatusEnum;
use App\Jobs\CalculateDamageFees;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CalculateDelayDamageFees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:calculate-delay-damage-fees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = Carbon::now(); // 04:37 PM +07, 17/07/2025
$lastMonth = $today->subMonth()->startOfMonth(); // 00:00 AM, 01/06/2025
$currentMonth = $today->startOfMonth(); // 00:00 AM, 01/07/2025

$schedules = DB::table('loan_schedules')
    ->select('customer_id', 'application_id', 'payment_plan_date', 'amount')
    ->where('payment_status', 2) // Chưa thanh toán (đã quá hạn)
    ->where('payment_plan_date', '<', $currentMonth) // Trước tháng 07/2025
    ->where('del_flag', '0') // Chưa xóa
    ->get();

        foreach ($schedules as $schedule) {
            CalculateDamageFees::dispatch($schedule);
        }
    }
}
