<?php

namespace App\Console\Commands;

use App\Enums\PaymentStatusEnum;
use App\Jobs\CalculateDamageFees;
use App\Repositories\LoanScheduleRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CalculateDelayDamageFees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:calculate-delay-damage-fees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $loanScheduleRepository;

    public function __construct(LoanScheduleRepository $loanScheduleRepository)
    {
        parent::__construct();
        $this->loanScheduleRepository = $loanScheduleRepository;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = Carbon::now();
        $currentMonth = $today->startOfMonth();

        $schedules = $this->loanScheduleRepository
            ->select(['customer_id', 'application_id', 'payment_plan_date', 'amount', 'id'])
            ->where('payment_status', PaymentStatusEnum::NOT_PAID_OVERDUE)
            ->where('payment_plan_date', '<', $currentMonth)
            ->get();

        foreach ($schedules as $schedule) {
            CalculateDamageFees::dispatch($schedule);
        }
    }
}
