<?php

use function Livewire\Volt\{state, on};
use function Livewire\Volt\{computed};

use Carbon\Carbon;

state([
    'monthIndex' => 0,
    'fromDate' => null,
    'toDate' => null,
]);

on([
    'update-advanced-search' => function ($data) {
        $this->fromDate = isValidDateFormat($data['payment_plan_date_from']) ? Carbon::createFromFormat('Y/m/d', $data['payment_plan_date_from']) : '';
        $this->toDate = isValidDateFormat($data['payment_plan_date_to']) ? Carbon::createFromFormat('Y/m/d', $data['payment_plan_date_to']) : '';
    },
]);

$time = computed(function () {
    return Carbon::now()->addMonths($this->monthIndex);
});

$previousMonth = computed(function () {
    return $this->time->addMonths(-1);
});

$nextMonth = computed(function () {
    return $this->time->addMonths(2);
});

$search = function () {
    $this->monthIndex = trimSpace($this->monthIndex);
    $this->dispatch('update-search', [
        'monthIndex' => $this->monthIndex,
    ]);
};

$decreaseIndex = function () {
    $this->monthIndex--;
    $this->fromDate = null;
    $this->toDate = null;
    $this->search();
};

$increaseIndex = function () {
    $this->monthIndex++;
    $this->fromDate = null;
    $this->toDate = null;
    $this->search();
};

$resetIndex = function () {
    $this->monthIndex = 0;
    $this->fromDate = null;
    $this->toDate = null;
    $this->search();
};
?>
<div class="d-flex align-items-center justify-content-between mb-5 setting-form-control">
    <div class="row align-items-center">
        <div class="col-auto">
            <h2 class="h1 fw-bold mb-3">
                {{ getFromAndToDate($this->fromDate,$this->toDate,$this->time) }}{{ trans2('screens.payment.index.monthly_payment_schedule') }}
            </h2>
            <div class="d-flex align-items-cente">
                <a wire:click.prevent="decreaseIndex"
                    class="btn-text-prev small cursor-pointer">{{ $this->previousMonth->year }}{{ trans2('year') }}{{ $this->previousMonth->month }}{{ trans2('month') }}</a>
                <a wire:click.prevent="resetIndex"
                    class="btn btn-text p-0 small mx-5 cursor-pointer">{{ trans2('screens.payment.index.this_month') }}</a>
                <a wire:click.prevent="increaseIndex"
                    class="btn-text-next small cursor-pointer">{{ $this->nextMonth->year }}{{ trans2('year') }}{{ $this->nextMonth->month }}{{ trans2('month') }}</a>
            </div>
        </div>
        <div class="col-auto ps-4">
            <button type="button" class="btn-sort" data-bs-toggle="modal" data-bs-target="#settingSortList">
                <i class="icon">
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M0.0912837 1.28672C0.245971 0.958594 0.574096 0.75 0.937377 0.75H11.0624C11.4257 0.75 11.7538 0.958594 11.9085 1.28672C12.0632 1.61484 12.0163 2.00156 11.7866 2.28281L7.49988 7.52109V10.5C7.49988 10.7836 7.3405 11.0437 7.08503 11.1703C6.82956 11.2969 6.52722 11.2711 6.29988 11.1L4.79988 9.975C4.61003 9.83438 4.49988 9.61172 4.49988 9.375V7.52109L0.210815 2.28047C-0.0165287 2.00156 -0.0657475 1.6125 0.0912837 1.28672Z" />
                    </svg>
                </i>
                <span class="text">{{ trans2('screens.customer.index.advanced_search') }}</span>
            </button>
        </div>
    </div>
    <div class="ms-auto d-flex align-items-center">
        <div class="dropdown">
            <button type="button" class="btn-kebob-menu dropdown-toggle" role="button" data-bs-toggle="dropdown"
                aria-expanded="false">
                <span class="visually-hidden">Dropdown menu</span>
            </button>
            <div class="dropdown-menu">
                <dl class="cursor-pointer">
                    <dt>ジャックス</dt>
                    <dd><a class="dropdown-item"
                            wire:click.prevent="$dispatch('export_unpaid_balance_csv')">{{ trans2('screens.payment.index.csv.download.unpaid_balance_list') }}</a>
                    </dd>
                    <dd><a class="dropdown-item"
                            wire:click.prevent="$dispatch('export_deposit_data_csv')">{{ trans2('screens.payment.index.csv.download.deposit_data') }}</a>
                    </dd>
                    <dd>
                        <livewire:admin.payment.upload-csv />
                    </dd>
                </dl>
            </div>
        </div>
    </div>
</div>
