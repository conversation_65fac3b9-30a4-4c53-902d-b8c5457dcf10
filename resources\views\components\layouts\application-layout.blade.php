<!DOCTYPE html>
<html lang="{!! getCurrentLangCode()!!}">
<head>
    @include('components.layouts.structures.head')

    @stack('styles')
</head>
<body class="page-application">

    <!-- start::content -->
    <div class="application-wrapper">
        <div class="application-contents">
            <div class="container-fluid">
                <livewire:admin.application.tab />

                <div class="container-min">
                    @if (request()->routeIs('admin.customer.application.setup*'))
                        <livewire:admin.application.customer.tab-customer />
                    @endif
                </div>

                @yield('content')

                {{ !empty($slot) ? $slot : '' }}
            </div>
        </div>
    </div>
    <!-- End #content -->

<livewire:common.confirm/>

<!-- Toast Messages -->
<livewire:common.toast-message/>

<!-- Event handle -->
@include('livewire.common.event-handle')

@include('components.layouts.structures.footer_js')

@stack('scripts')

<script src="https://cdnjs.cloudflare.com/ajax/libs/jSignature/2.1.3/jSignature.min.js"></script>

</body>
</html>
