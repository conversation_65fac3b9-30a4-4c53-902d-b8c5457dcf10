<?php

namespace App\Livewire\Admin\Contract\Forms;

use App\Enums\BankAccountTypeEnum;
use App\Enums\BankFlagEnum;
use App\Enums\BlackFlagEnum;
use App\Enums\InformationInputFlagEnum;
use App\Enums\PaymentCompanyFlagEnum;
use App\Enums\SexEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Rules\TelValidationRule;
use App\Rules\ZipCodeValidationRule;
use Illuminate\Validation\Rule;
use App\Services\ShopBrandService;
use App\Services\CourseService;

class UpdateForm extends BaseAdminForm
{
    public $shop_brand_id;

    public $staff_name;
    public $application_date;
    public $contract_date;
    public $contract_cancel_date;
    public $contract_cancel_amount;
    public $regist_number;
    public $payment_company_regist_date;
    public $payment_company_flag;
    public $product_general = [
        [
            'course_id' => '',
            'count' => 0,
            'amount' => '',
        ]
    ];
    public $product_optional = [
        [
            'course_id' => '',
            'count' => 0,
            'amount' => '',
        ]
    ];
    // customer
    public $information_input_flag;
    public $bank_account_name;
    public $bank_account_name_kana;
    public $bank_flag;
    public $bank_account_mark1;
    public $bank_account_mark2;
    public $bank_account_mark3;
    public $bank_account_number;
    public $bank_code;
    public $bank_name;
    public $branch_code;
    public $branch_name;
    public $bank_account_type;
    public $contract_comment;

    public $isCheckContractStatus5;
    public $isCheckReInvoiceDocTargetFlag1;

    public function rules()
    {
        $costRule = $this->costRule();
        $rules = [
            'shop_brand_id' => [
                'required',
                Rule::in(app(ShopBrandService::class)->getAllShopBrands()->pluck('id')->toArray()),
            ],
            'staff_name' => 'nullable|string|max:128',
            'application_date' => 'required|date|date_format:Y/m/d',
            'contract_date' => 'required|date|date_format:Y/m/d',
            'contract_cancel_date' => 'nullable|date|date_format:Y/m/d',
            'contract_cancel_amount' => 'nullable|' . $costRule,
            'regist_number' => 'required|number|max:128',
            'payment_company_regist_date' => 'required|date|date_format:Y/m/d',
            'payment_company_flag' => [
                'nullable',
                Rule::in(PaymentCompanyFlagEnum::getValues()),
            ],
            'contract_comment' => 'nullable',

            'information_input_flag' => [
                'required',
                Rule::in(InformationInputFlagEnum::getValues()),
            ],
            'bank_account_name' => 'required|string|max:512',
            'bank_account_name_kana' => $this->bankAccountKanaValidationRule(),
            'bank_flag' => ['required', Rule::in(BankFlagEnum::getValues())],
            'bank_account_mark1' => 'required|number|max:32',
            'bank_account_mark2' => 'required|number|max:32',
            'bank_account_mark3' => 'required|number|max:32',
            'bank_account_number' => 'required|number|max:32',
            'bank_code' => 'required|number|max:32',
            'bank_name' => 'required|string|max:32',
            'branch_code' => 'required|number|max:32',
            'branch_name' => 'required|string|max:32',
            'bank_account_type' => ['required', Rule::in(BankAccountTypeEnum::getValues())],

            'product_general' => 'array',
            'product_general.*.course_id' => 'required:exists:courses,id',
            'product_general.*.count' => 'required|integer|min:1',
            'product_general.*.amount' => 'required|' . $costRule,
            'product_optional' => 'nullable|array',
            'product_optional.*.course_id' => 'nullable:exists:courses,id',
            'product_optional.*.count' => 'nullable|integer|min:1',
            'product_optional.*.amount' => 'nullable|' . $costRule,
        ];

        foreach ($this->product_optional as $index => $item) {
            $rules["product_optional.$index.course_id"] = 'nullable|exists:courses,id';
            $rules["product_optional.$index.count"]     = 'nullable|integer|min:1';
            $rules["product_optional.$index.amount"]    = 'nullable|' . $costRule;

            if (
                !empty($item['course_id']) ||
                isset($item['count']) && $item['count'] !== '' ||
                isset($item['amount']) && $item['amount'] !== ''
            ) {
                $rules["product_optional.$index.course_id"] = 'required|exists:courses,id';
                $rules["product_optional.$index.count"]     = 'required|integer|min:1';
                $rules["product_optional.$index.amount"]    = 'required|' . $costRule;
            }
        }
        return $rules;
    }

    public function bankAccountKanaValidationRule(): array
    {
        return [
            'required',
            'string',
            'max:512',
            function ($attribute, $value, $fail) {
                if (!preg_match('/^[ｱ-ﾝﾞﾟA-Z0-9\(\)\.\-\/ ]+$/u', $value)) {
                    $fail(__('口座名義（フリガナ）には、半角カタカナ／半角英数字（大文字）／記号を入力してください。'));
                }
            },
        ];
    }

    public function getValidationAttributes()
    {
        return __('models.contract.attributes');
    }
}
