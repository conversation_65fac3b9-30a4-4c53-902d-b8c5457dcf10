<?php

use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('loan_payments', function (BlueprintCustom $table) {
            $table->id();
            $table->integer('customer_id')->comment('顧客ID')->index();
            $table->integer('application_id')->comment('申込ID')->index();
            $table->date('payment_date')->nullable()->comment('入金日');
            $table->decimal('amount', 10, 2)->comment('金額');
            $table->char('auto_manual_flag', 1)->default('1')->comment('手動/自動フラグ 1:自動 2:手動');
            $table->char('payment_type', 1)->default('1')->comment('支払種類 1:口座振替 2:振込 3:現金');
            $table->char('payment_company_flag', 1)->default('1')->comment('収納会社 1:アプラス 2:信販 3:その他');
            $table->text('comment')->nullable()->comment('備考')->nullable();
            $table->char('result_status', 1)->nullable()->comment('結果状態')->nullable();
            $table->char('before_payment_status', 1)->default('1')->comment('入金ステータス（入金前）');
            $table->char('payment_status', 1)->default('1')->comment('入金ステータス');
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SchemaCustom::dropIfExists('loan_payments');
    }
};
