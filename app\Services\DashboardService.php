<?php

namespace App\Services;
use App\Repositories\ApplicationRepository;
use App\Repositories\ApplicationInspectionRepository;
use Carbon\Carbon;

class DashboardService extends CustomService
{
    public function getApplicationCount($status, $params)
    {
        return app(ApplicationRepository::class)->getApplicationCount($status, $params);
    }

    public function getApplicationInspectionStatusCount($status)
    {
        return app(ApplicationInspectionRepository::class)->getApplicationInspectionStatusCount($status);
    }

    public function getListBalancesForDashboard($params, $isCsv)
    {
        return app(ApplicationRepository::class)->getListBalancesForDashboard($params, $isCsv);
    }

    public function exportCsvDashboard($filename, $headers, $data = [], $isSample = false)
    {
        $handle = fopen($filename, 'w');
        if (!$isSample) {
            fwrite($handle, "\xEF\xBB\xBF");
        }
        fputcsv($handle, $headers);

        foreach ($data as $row) {
            fputcsv($handle, $this->formatCsvDashboardRow($row));
        }

        fclose($handle);

        return response()->download($filename)->deleteFileAfterSend();
    }

    public function formatCsvDashboardRow($row) 
    {
        return [
            "target_month" => $row->target_month ? Carbon::createFromFormat('Ym', $row->target_month)->format('Y/m') : '',
            "total_contracts" => $row->total_contracts ?? '',
            "new_contracts" => $row->new_contracts ?? '',
            "new_contract_amount" => $row->new_contract_amount ?? '',
            "completed_contracts" => $row->completed_contracts ?? '',
            "cancelled_contracts" => $row->cancelled_contracts ?? '',
            "cancelled_amount" => $row->cancelled_amount ?? '',
            "forced_cancelled_contracts" => $row->forced_cancelled_contracts ?? '',
            "forced_contract_cancel_amount" => $row->forced_contract_cancel_amount ?? '',
            "uncollected_contracts" => $row->uncollected_contracts ?? '',
            "uncollected_amount" => $row->uncollected_amount ?? '',
        ];
    }
}
