<?php

namespace App\Models;

use App\Models\Base\CustomModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LoanArrear extends CustomModel
{
    use HasFactory;

    /**
     * テーブル名
     */
    protected $table = 'loan_arrears';

    /**
     * タイムスタンプ自動管理無効
     */
    public $timestamps = false;

    /**
     * ホワイトリスト
     */
    protected $fillable = [
        'id',
        'customer_id',
        'application_id',
        'loan_schedule_id',
        'amount',
        'delay_damage_amount',
        'reissue_fee',
        'payment_date',
        'del_flag',
        'ins_date',
        'ins_id',
        'upd_date',
        'upd_id',
    ];

    /**
     * 関連定義
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function loanSchedule()
    {
        return $this->belongsTo(LoanSchedule::class);
    }
}