<?php

use function Livewire\Volt\{state};
use function Livewire\Volt\{computed};
state([
    'keyword' => '',
]);

$search = function () {
    $this->keyword = trimSpace($this->keyword);
    $this->dispatch('update-search', [
        'keyword' => $this->keyword,
    ]);
};
?>
<div>
    <form action="/contract" role="form">
        <div class="form-block"></div>
    </form>
    <div class="d-flex align-items-end justify-content-between mb-5 setting-form-control">
        <div class="row align-items-end">
            <div class="col-auto">
                <div class="form-parts-keyword">
                    <input type="text" class="form-control w-px-300" x-data @keydown.enter="$wire.call('search')" wire:model="keyword" wire:blur="search"
                        placeholder="{{ transm('_default.attributes.name_search_placeholder') }}">
                    <button type="button" wire:click.prevent="search"></button>
                </div>
            </div>
            <div class="col-auto ps-4">
                <button type="button" class="btn-sort" data-bs-toggle="modal" data-bs-target="#settingSortList">
                    <i class="icon">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.0912837 1.28672C0.245971 0.958594 0.574096 0.75 0.937377 0.75H11.0624C11.4257 0.75 11.7538 0.958594 11.9085 1.28672C12.0632 1.61484 12.0163 2.00156 11.7866 2.28281L7.49988 7.52109V10.5C7.49988 10.7836 7.3405 11.0437 7.08503 11.1703C6.82956 11.2969 6.52722 11.2711 6.29988 11.1L4.79988 9.975C4.61003 9.83438 4.49988 9.61172 4.49988 9.375V7.52109L0.210815 2.28047C-0.0165287 2.00156 -0.0657475 1.6125 0.0912837 1.28672Z"/>
                        </svg>
                    </i>
                    <span class="text">{{ trans2('screens.contract.index.advanced_search') }}</span>
                </button>
            </div>
        </div>
        <div class="ms-auto d-flex align-items-center">
            <button type="button" class="btn-csv-download" wire:click.prevent="$dispatch('export_csv')">{{ trans2('button.CSV_download') }}</button>
            @can('allowed-create', \App\Enums\SidebarMenuEnum::CONTRACT)
                <a href="#" class="btn btn-dark ms-4" target="_blank">{{ trans2('button.register_screen_list') }}</a>
            @endcan
        </div>
    </div>

</div>
