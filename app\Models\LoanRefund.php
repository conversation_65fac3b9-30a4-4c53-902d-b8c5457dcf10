<?php

namespace App\Models;

use App\Models\Base\CustomModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoanRefund extends CustomModel
{
    use HasFactory;

    /**
     * テーブル名
     */
    protected $table = 'loan_refunds';

    /**
     * ホワイトリスト
     */
    protected $fillable = [
        'id',
        'customer_id',
        'application_id',
        'loan_schedule_id',
        'loan_payment_id',
        'loan_withdrawal_id',
        'payment_date',
        'amount',
        'comment',
    ];

    /**
     * 関連定義
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function loanSchedule(): BelongsTo
    {
        return $this->belongsTo(LoanSchedule::class, 'loan_schedule_id', 'id');
    }

    public function loanPayment()
    {
        return $this->belongsTo(LoanPayment::class);
    }
    public function loanWithdrawal()
    {
        return $this->belongsTo(LoanWithdrawal::class);
    }
}
