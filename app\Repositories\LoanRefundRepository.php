<?php

namespace App\Repositories;

use App\Models\LoanArrear;
use App\Models\LoanPayment;
use App\Models\LoanPaymentAllocation;
use App\Models\LoanRefund;

class LoanRefundRepository extends CustomRepository
{
    protected $model = LoanRefund::class;

    public function getPaymentDatebyApplicationId($id)
    {
        return $this->model->select([
            $this->modelField('id'),
            $this->modelField('payment_date'),
        ])
            ->where('application_id', $id)
            ->get();
    }

    public function getAllByLoanWithdrawalId($loanWithdrawalId)
    {
        return $this->select([
            $this->modelField('id'),
        ])->where($this->modelField('loan_withdrawal_id'), $loanWithdrawalId)->get();
    }
}
