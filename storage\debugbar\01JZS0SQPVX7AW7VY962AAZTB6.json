{"__meta": {"id": "01JZS0SQPVX7AW7VY962AAZTB6", "datetime": "2025-07-10 10:56:59", "utime": **********.227863, "method": "GET", "uri": "/management/payments", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 7, "messages": [{"message": "[10:56:59] LOG.debug: (Time: 18.91) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.008634, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:59] LOG.debug: (Time: 00.48) SQL: select * from `brands` where `brands`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.127659, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:59] LOG.debug: (Time: 00.45) SQL: select * from `shops` where `shops`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.132101, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:59] LOG.debug: (Time: 04.06) SQL: select * from `item_types` where `item_types`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.1398, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:59] LOG.debug: (Time: 02.96) SQL: select * from `courses` where `courses`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.146432, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:59] LOG.warning: Optional parameter $dataSearch declared before required parameter $paymentStatus is implicitly treated as a required parameter in C:\\xampp\\htdocs\\ladybird\\app\\Services\\LoanScheduleService.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.184511, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:59] LOG.debug: (Time: 05.02) SQL: select `brands`.`id`, `brands`.`name`, \n                SUM(CASE WHEN ls.payment_status IN (1, 2) THEN 1 ELSE 0 END) as waiting_count,\n                SUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN 1 ELSE 0 END) as deposited_count,\n                SUM(CASE WHEN ls.payment_status = 2 THEN 1 ELSE 0 END) as unpaid_count,\n                SUM(CASE WHEN ls.payment_status IN (8,9) THEN 1 ELSE 0 END) as other_count,\n                SUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN 1 ELSE 0 END) as grand_count,\n\n                SUM(CASE WHEN ls.payment_status IN (1, 2) THEN ls.total_amount ELSE 0 END) as waiting_total_amount,\n                SUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN ls.total_amount ELSE 0 END) as deposited_total_amount,\n                SUM(CASE WHEN ls.payment_status = 2 THEN ls.total_amount ELSE 0 END) as unpaid_total_amount,\n                SUM(CASE WHEN ls.payment_status IN (8,9) THEN ls.total_amount ELSE 0 END) as other_total_amount,\n                SUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN ls.total_amount ELSE 0 END) as grand_total_amount\n             from (select `loan_schedules`.`id`, `loan_schedules`.`total_amount`, `loan_schedules`.`payment_status`, `applications`.`brand_id`, `applications`.`shop_id`, `applications`.`contract_status` from `loan_schedules` inner join `applications` on `applications`.`id` = `loan_schedules`.`application_id` left join `application_courses` on `applications`.`id` = `application_courses`.`application_id` where `loan_schedules`.`del_flag` = 0 and `applications`.`del_flag` = 0 and year(`loan_schedules`.`payment_plan_date`) = 2025 and month(`loan_schedules`.`payment_plan_date`) = '07' and `applications`.`contract_status` is not null group by `loan_schedules`.`id`, `loan_schedules`.`total_amount`, `loan_schedules`.`payment_status`) as ls inner join `brands` on `brands`.`id` = `ls`.`brand_id` inner join `shop_brands` on `shop_brands`.`shop_id` = `ls`.`shop_id` where `shop_brands`.`del_flag` = 0 and `brands`.`del_flag` = 0 group by `brands`.`id`, `brands`.`name` {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.196043, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.805587, "end": **********.227882, "duration": 0.42229485511779785, "duration_str": "422ms", "measures": [{"label": "Booting", "start": **********.805587, "relative_start": 0, "end": **********.961934, "relative_end": **********.961934, "duration": 0.*****************, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.961943, "relative_start": 0.****************, "end": **********.227883, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "266ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.969011, "relative_start": 0.*****************, "end": **********.971656, "relative_end": **********.971656, "duration": 0.0026450157165527344, "duration_str": "2.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.225623, "relative_start": 0.*****************, "end": **********.22573, "relative_end": **********.22573, "duration": 0.00010704994201660156, "duration_str": "107μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.226265, "relative_start": 0.*****************, "end": **********.2263, "relative_end": **********.2263, "duration": 3.504753112792969e-05, "duration_str": "35μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 20, "nb_templates": 20, "templates": [{"name": "1x livewire.admin.payment.index", "param_count": null, "params": [], "start": **********.022539, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/payment/index.blade.phplivewire.admin.payment.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fpayment%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.payment.index"}, {"name": "1x volt-livewire::admin.payment.search", "param_count": null, "params": [], "start": **********.043917, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/admin/payment/search.blade.phpvolt-livewire::admin.payment.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fpayment%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::admin.payment.search"}, {"name": "1x livewire.admin.payment.upload-csv", "param_count": null, "params": [], "start": **********.107837, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/payment/upload-csv.blade.phplivewire.admin.payment.upload-csv", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fpayment%2Fupload-csv.blade.php&line=1", "ajax": false, "filename": "upload-csv.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.payment.upload-csv"}, {"name": "1x volt-livewire::admin.payment.advanced-search", "param_count": null, "params": [], "start": **********.15971, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/admin/payment/advanced-search.blade.phpvolt-livewire::admin.payment.advanced-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fpayment%2Fadvanced-search.blade.php&line=1", "ajax": false, "filename": "advanced-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::admin.payment.advanced-search"}, {"name": "1x livewire.admin.payment.payment-table", "param_count": null, "params": [], "start": **********.1986, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/payment/payment-table.blade.phplivewire.admin.payment.payment-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fpayment%2Fpayment-table.blade.php&line=1", "ajax": false, "filename": "payment-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.payment.payment-table"}, {"name": "1x livewire.admin.payment.table-data-list", "param_count": null, "params": [], "start": **********.201524, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/payment/table-data-list.blade.phplivewire.admin.payment.table-data-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fpayment%2Ftable-data-list.blade.php&line=1", "ajax": false, "filename": "table-data-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.payment.table-data-list"}, {"name": "1x components.loading-overlay", "param_count": null, "params": [], "start": **********.202463, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/loading-overlay.blade.phpcomponents.loading-overlay", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Floading-overlay.blade.php&line=1", "ajax": false, "filename": "loading-overlay.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.loading-overlay"}, {"name": "1x livewire.common.csv-modal-errors", "param_count": null, "params": [], "start": **********.203586, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/common/csv-modal-errors.blade.phplivewire.common.csv-modal-errors", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fcsv-modal-errors.blade.php&line=1", "ajax": false, "filename": "csv-modal-errors.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.common.csv-modal-errors"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.206295, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.master", "param_count": null, "params": [], "start": **********.206981, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/master.blade.phpcomponents.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.master"}, {"name": "1x components.layouts.structures.head", "param_count": null, "params": [], "start": **********.207577, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/head.blade.phpcomponents.layouts.structures.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.head"}, {"name": "1x components.layouts.structures.sidebar", "param_count": null, "params": [], "start": **********.209703, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/sidebar.blade.phpcomponents.layouts.structures.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.sidebar"}, {"name": "1x volt-livewire::common.nav-bar", "param_count": null, "params": [], "start": **********.212754, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/nav-bar.blade.phpvolt-livewire::common.nav-bar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fnav-bar.blade.php&line=1", "ajax": false, "filename": "nav-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.nav-bar"}, {"name": "1x volt-livewire::common.logout-modal", "param_count": null, "params": [], "start": **********.21521, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/logout-modal.blade.phpvolt-livewire::common.logout-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Flogout-modal.blade.php&line=1", "ajax": false, "filename": "logout-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.logout-modal"}, {"name": "1x volt-livewire::common.confirm", "param_count": null, "params": [], "start": **********.21878, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/confirm.blade.phpvolt-livewire::common.confirm", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.confirm"}, {"name": "1x volt-livewire::common.toast-message", "param_count": null, "params": [], "start": **********.220726, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/toast-message.blade.phpvolt-livewire::common.toast-message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Ftoast-message.blade.php&line=1", "ajax": false, "filename": "toast-message.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.toast-message"}, {"name": "1x components.layouts.structures.footer", "param_count": null, "params": [], "start": **********.221565, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer.blade.phpcomponents.layouts.structures.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer"}, {"name": "1x components.layouts.structures.footer_js", "param_count": null, "params": [], "start": **********.221957, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_js.blade.phpcomponents.layouts.structures.footer_js", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_js.blade.php&line=1", "ajax": false, "filename": "footer_js.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_js"}, {"name": "1x components.layouts.structures.footer_autoload", "param_count": null, "params": [], "start": **********.222722, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_autoload.blade.phpcomponents.layouts.structures.footer_autoload", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_autoload.blade.php&line=1", "ajax": false, "filename": "footer_autoload.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_autoload"}, {"name": "1x livewire.common.event-handle", "param_count": null, "params": [], "start": **********.223202, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/common/event-handle.blade.phplivewire.common.event-handle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fevent-handle.blade.php&line=1", "ajax": false, "filename": "event-handle.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.common.event-handle"}]}, "route": {"uri": "GET management/payments", "middleware": "admin, locale, auth:admin", "uses": "Closure() {#614\n  class: \"Livewire\\Volt\\VoltManager\"\n  this: Livewire\\Volt\\VoltManager {#493 …}\n  use: {\n    $componentName: \"App\\Livewire\\Admin\\Payment\\Index\"\n  }\n  file: \"C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\VoltManager.php\"\n  line: \"34 to 41\"\n}", "as": "admin.payment.index", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management/payments", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>"}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03188, "accumulated_duration_str": "31.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.989828, "duration": 0.01891, "duration_str": "18.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 59.316}, {"sql": "select * from `brands` where `brands`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 43}, {"index": 19, "namespace": null, "name": "app/Services/BrandService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\BrandService.php", "line": 24}, {"index": 20, "namespace": null, "name": "resources/views/livewire/admin/payment/advanced-search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\payment\\advanced-search.blade.php", "line": 18}, {"index": 22, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}], "start": **********.1272771, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BrandRepository.php:43", "source": {"index": 18, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FBrandRepository.php&line=43", "ajax": false, "filename": "BrandRepository.php", "line": "43"}, "connection": "local-ladybird", "explain": null, "start_percent": 59.316, "width_percent": 1.506}, {"sql": "select * from `shops` where `shops`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 43}, {"index": 19, "namespace": null, "name": "app/Services/ShopService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\ShopService.php", "line": 24}, {"index": 20, "namespace": null, "name": "resources/views/livewire/admin/payment/advanced-search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\payment\\advanced-search.blade.php", "line": 19}, {"index": 22, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}], "start": **********.131751, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ShopRepository.php:43", "source": {"index": 18, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopRepository.php&line=43", "ajax": false, "filename": "ShopRepository.php", "line": "43"}, "connection": "local-ladybird", "explain": null, "start_percent": 60.822, "width_percent": 1.412}, {"sql": "select * from `item_types` where `item_types`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ItemTypeRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ItemTypeRepository.php", "line": 37}, {"index": 17, "namespace": null, "name": "app/Services/ItemTypeService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\ItemTypeService.php", "line": 23}, {"index": 18, "namespace": null, "name": "resources/views/livewire/admin/payment/advanced-search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\payment\\advanced-search.blade.php", "line": 20}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 21, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}], "start": **********.13584, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "ItemTypeRepository.php:37", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ItemTypeRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ItemTypeRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FItemTypeRepository.php&line=37", "ajax": false, "filename": "ItemTypeRepository.php", "line": "37"}, "connection": "local-ladybird", "explain": null, "start_percent": 62.233, "width_percent": 12.735}, {"sql": "select * from `courses` where `courses`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 48}, {"index": 19, "namespace": null, "name": "app/Services/CourseService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\CourseService.php", "line": 30}, {"index": 20, "namespace": null, "name": "resources/views/livewire/admin/payment/advanced-search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\payment\\advanced-search.blade.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 23, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}], "start": **********.1435761, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "CourseRepository.php:48", "source": {"index": 18, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FCourseRepository.php&line=48", "ajax": false, "filename": "CourseRepository.php", "line": "48"}, "connection": "local-ladybird", "explain": null, "start_percent": 74.969, "width_percent": 9.285}, {"sql": "select `brands`.`id`, `brands`.`name`,\nSUM(CASE WHEN ls.payment_status IN (1, 2) THEN 1 ELSE 0 END) as waiting_count,\nSUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN 1 ELSE 0 END) as deposited_count,\nSUM(CASE WHEN ls.payment_status = 2 THEN 1 ELSE 0 END) as unpaid_count,\nSUM(CASE WHEN ls.payment_status IN (8,9) THEN 1 ELSE 0 END) as other_count,\nSUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN 1 ELSE 0 END) as grand_count,\nSUM(CASE WHEN ls.payment_status IN (1, 2) THEN ls.total_amount ELSE 0 END) as waiting_total_amount,\nSUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN ls.total_amount ELSE 0 END) as deposited_total_amount,\nSUM(CASE WHEN ls.payment_status = 2 THEN ls.total_amount ELSE 0 END) as unpaid_total_amount,\nSUM(CASE WHEN ls.payment_status IN (8,9) THEN ls.total_amount ELSE 0 END) as other_total_amount,\nSUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN ls.total_amount ELSE 0 END) as grand_total_amount\nfrom (select `loan_schedules`.`id`, `loan_schedules`.`total_amount`, `loan_schedules`.`payment_status`, `applications`.`brand_id`, `applications`.`shop_id`, `applications`.`contract_status` from `loan_schedules` inner join `applications` on `applications`.`id` = `loan_schedules`.`application_id` left join `application_courses` on `applications`.`id` = `application_courses`.`application_id` where `loan_schedules`.`del_flag` = 0 and `applications`.`del_flag` = 0 and year(`loan_schedules`.`payment_plan_date`) = 2025 and month(`loan_schedules`.`payment_plan_date`) = '\\'07\\'' and `applications`.`contract_status` is not null group by `loan_schedules`.`id`, `loan_schedules`.`total_amount`, `loan_schedules`.`payment_status`) as ls inner join `brands` on `brands`.`id` = `ls`.`brand_id` inner join `shop_brands` on `shop_brands`.`shop_id` = `ls`.`shop_id` where `shop_brands`.`del_flag` = 0 and `brands`.`del_flag` = 0 group by `brands`.`id`, `brands`.`name`", "type": "query", "params": [], "bindings": [0, 0, 2025, "'07'", 0, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 271}, {"index": 14, "namespace": null, "name": "app/Services/LoanScheduleService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\LoanScheduleService.php", "line": 33}, {"index": 15, "namespace": null, "name": "app/Livewire/Admin/Payment/PaymentTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Payment\\PaymentTable.php", "line": 114}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.191121, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "LoanScheduleRepository.php:271", "source": {"index": 13, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 271}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FLoanScheduleRepository.php&line=271", "ajax": false, "filename": "LoanScheduleRepository.php", "line": "271"}, "connection": "local-ladybird", "explain": null, "start_percent": 84.253, "width_percent": 15.747}]}, "models": {"data": {"App\\Models\\Brand": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Course": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\Shop": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FShop.php&line=1", "ajax": false, "filename": "Shop.php", "line": "?"}}, "App\\Models\\ItemType": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FItemType.php&line=1", "ajax": false, "filename": "ItemType.php", "line": "?"}}, "App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 23, "is_counter": true}, "livewire": {"data": {"admin.payment #vrGh8hXrS43Uw2azugrN": "array:4 [\n  \"data\" => array:4 [\n    \"page\" => \"payment\"\n    \"pageTitle\" => \"入金管理｜LadyBird\"\n    \"redirecting\" => false\n    \"guest\" => false\n  ]\n  \"name\" => \"admin.payment\"\n  \"component\" => \"App\\Livewire\\Admin\\Payment\\Index\"\n  \"id\" => \"vrGh8hXrS43Uw2azugrN\"\n]", "admin.payment.search #6SG3A8iWPOwzLf7VR9vf": "array:4 [\n  \"data\" => array:3 [\n    \"monthIndex\" => 0\n    \"fromDate\" => null\n    \"toDate\" => null\n  ]\n  \"name\" => \"admin.payment.search\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\24febba25f98c4671b40416b9e8356b2.php:8$116c\"\n  \"id\" => \"6SG3A8iWPOwzLf7VR9vf\"\n]", "admin.payment.upload-csv #9IEMVczmFkYcjtb8wbaS": "array:4 [\n  \"data\" => array:6 [\n    \"file_url\" => null\n    \"image_url\" => null\n    \"isUploadfile\" => false\n    \"isUploadImage\" => false\n    \"tableName\" => null\n    \"guest\" => false\n  ]\n  \"name\" => \"admin.payment.upload-csv\"\n  \"component\" => \"App\\Livewire\\Admin\\Payment\\UploadCsv\"\n  \"id\" => \"9IEMVczmFkYcjtb8wbaS\"\n]", "admin.payment.advanced-search #Ux0EzAezWE4BZ6M7bumh": "array:4 [\n  \"data\" => array:11 [\n    \"brand_id\" => \"\"\n    \"shop_id\" => \"\"\n    \"item_type_id\" => \"\"\n    \"course_id\" => \"\"\n    \"payment_plan_date_from\" => \"\"\n    \"payment_plan_date_to\" => \"\"\n    \"payment_status\" => []\n    \"listBrands\" => Illuminate\\Database\\Eloquent\\Collection {#1022\n      #items: array:10 [\n        0 => App\\Models\\Brand {#1167\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Brand {#1122\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        2 => App\\Models\\Brand {#1013\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        3 => App\\Models\\Brand {#1231\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        4 => App\\Models\\Brand {#1119\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        5 => App\\Models\\Brand {#1037\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        6 => App\\Models\\Brand {#1058\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        7 => App\\Models\\Brand {#1072\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        8 => App\\Models\\Brand {#1083\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        9 => App\\Models\\Brand {#1084\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"listShops\" => Illuminate\\Database\\Eloquent\\Collection {#1016\n      #items: array:2 [\n        0 => App\\Models\\Shop {#1137\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Shop {#1133\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"listItemTypes\" => Illuminate\\Database\\Eloquent\\Collection {#1199\n      #items: array:2 [\n        0 => App\\Models\\ItemType {#1198\n          #connection: \"mysql\"\n          #table: \"item_types\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:8 [\n            \"id\" => 1\n            \"view_id\" => \"P-1\"\n            \"name\" => \"aaa\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:43\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:43\"\n            \"upd_id\" => 1\n          ]\n          #original: array:8 [\n            \"id\" => 1\n            \"view_id\" => \"P-1\"\n            \"name\" => \"aaa\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:43\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:43\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:8 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"del_flag\"\n            4 => \"ins_id\"\n            5 => \"ins_date\"\n            6 => \"upd_id\"\n            7 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\ItemType {#1014\n          #connection: \"mysql\"\n          #table: \"item_types\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:8 [\n            \"id\" => 2\n            \"view_id\" => \"P-2\"\n            \"name\" => \"bbb\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:50\"\n            \"upd_id\" => 1\n          ]\n          #original: array:8 [\n            \"id\" => 2\n            \"view_id\" => \"P-2\"\n            \"name\" => \"bbb\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:50\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:8 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"del_flag\"\n            4 => \"ins_id\"\n            5 => \"ins_date\"\n            6 => \"upd_id\"\n            7 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"listCourses\" => Illuminate\\Database\\Eloquent\\Collection {#1218\n      #items: array:8 [\n        0 => App\\Models\\Course {#1219\n          #connection: \"mysql\"\n          #table: \"courses\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:17 [\n            \"id\" => 7\n            \"view_id\" => \"C-7\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"aaaa\"\n            \"name_application\" => \"bbbb\"\n            \"unit_price\" => \"1.00\"\n            \"principal_price\" => \"1.00\"\n            \"type\" => \"1\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"0\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-24 18:37:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-24 18:37:27\"\n            \"upd_id\" => 1\n          ]\n          #original: array:17 [\n            \"id\" => 7\n            \"view_id\" => \"C-7\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"aaaa\"\n            \"name_application\" => \"bbbb\"\n            \"unit_price\" => \"1.00\"\n            \"principal_price\" => \"1.00\"\n            \"type\" => \"1\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"0\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-24 18:37:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-24 18:37:27\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"type\" => \"App\\Enums\\TypeEnum\"\n            \"split_fee_flag\" => \"App\\Enums\\SplitFeeFlagEnum\"\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:17 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"item_type_id\"\n            3 => \"name_management\"\n            4 => \"name_application\"\n            5 => \"unit_price\"\n            6 => \"principal_price\"\n            7 => \"type\"\n            8 => \"sort_management\"\n            9 => \"sort_application\"\n            10 => \"split_fee_flag\"\n            11 => \"credit_flag\"\n            12 => \"del_flag\"\n            13 => \"ins_id\"\n            14 => \"ins_date\"\n            15 => \"upd_id\"\n            16 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Course {#1203\n          #connection: \"mysql\"\n          #table: \"courses\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:17 [\n            \"id\" => 8\n            \"view_id\" => \"C-8\"\n            \"item_type_id\" => 2\n            \"name_management\" => \"cccc\"\n            \"name_application\" => \"aaa\"\n            \"unit_price\" => \"0.00\"\n            \"principal_price\" => \"0.00\"\n            \"type\" => \"2\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"1\"\n            \"credit_flag\" => \"1\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-24 18:37:43\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-24 18:37:43\"\n            \"upd_id\" => 1\n          ]\n          #original: array:17 [\n            \"id\" => 8\n            \"view_id\" => \"C-8\"\n            \"item_type_id\" => 2\n            \"name_management\" => \"cccc\"\n            \"name_application\" => \"aaa\"\n            \"unit_price\" => \"0.00\"\n            \"principal_price\" => \"0.00\"\n            \"type\" => \"2\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"1\"\n            \"credit_flag\" => \"1\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-24 18:37:43\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-24 18:37:43\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"type\" => \"App\\Enums\\TypeEnum\"\n            \"split_fee_flag\" => \"App\\Enums\\SplitFeeFlagEnum\"\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:17 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"item_type_id\"\n            3 => \"name_management\"\n            4 => \"name_application\"\n            5 => \"unit_price\"\n            6 => \"principal_price\"\n            7 => \"type\"\n            8 => \"sort_management\"\n            9 => \"sort_application\"\n            10 => \"split_fee_flag\"\n            11 => \"credit_flag\"\n            12 => \"del_flag\"\n            13 => \"ins_id\"\n            14 => \"ins_date\"\n            15 => \"upd_id\"\n            16 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        2 => App\\Models\\Course {#1206\n          #connection: \"mysql\"\n          #table: \"courses\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:17 [\n            \"id\" => 9\n            \"view_id\" => \"C-9\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"adasd\"\n            \"name_application\" => \"aaaaa\"\n            \"unit_price\" => \"1.00\"\n            \"principal_price\" => \"1.00\"\n            \"type\" => \"1\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"1\"\n            \"credit_flag\" => \"1\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-24 18:37:58\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:43:22\"\n            \"upd_id\" => 1\n          ]\n          #original: array:17 [\n            \"id\" => 9\n            \"view_id\" => \"C-9\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"adasd\"\n            \"name_application\" => \"aaaaa\"\n            \"unit_price\" => \"1.00\"\n            \"principal_price\" => \"1.00\"\n            \"type\" => \"1\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"1\"\n            \"credit_flag\" => \"1\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-24 18:37:58\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:43:22\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"type\" => \"App\\Enums\\TypeEnum\"\n            \"split_fee_flag\" => \"App\\Enums\\SplitFeeFlagEnum\"\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:17 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"item_type_id\"\n            3 => \"name_management\"\n            4 => \"name_application\"\n            5 => \"unit_price\"\n            6 => \"principal_price\"\n            7 => \"type\"\n            8 => \"sort_management\"\n            9 => \"sort_application\"\n            10 => \"split_fee_flag\"\n            11 => \"credit_flag\"\n            12 => \"del_flag\"\n            13 => \"ins_id\"\n            14 => \"ins_date\"\n            15 => \"upd_id\"\n            16 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        3 => App\\Models\\Course {#1207\n          #connection: \"mysql\"\n          #table: \"courses\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:17 [\n            \"id\" => 10\n            \"view_id\" => \"C-10\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"sdfs\"\n            \"name_application\" => \"dfsdfsdf\"\n            \"unit_price\" => \"0.00\"\n            \"principal_price\" => \"0.00\"\n            \"type\" => \"2\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"0\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-24 18:38:38\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-24 18:38:38\"\n            \"upd_id\" => 1\n          ]\n          #original: array:17 [\n            \"id\" => 10\n            \"view_id\" => \"C-10\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"sdfs\"\n            \"name_application\" => \"dfsdfsdf\"\n            \"unit_price\" => \"0.00\"\n            \"principal_price\" => \"0.00\"\n            \"type\" => \"2\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"0\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-24 18:38:38\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-24 18:38:38\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"type\" => \"App\\Enums\\TypeEnum\"\n            \"split_fee_flag\" => \"App\\Enums\\SplitFeeFlagEnum\"\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:17 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"item_type_id\"\n            3 => \"name_management\"\n            4 => \"name_application\"\n            5 => \"unit_price\"\n            6 => \"principal_price\"\n            7 => \"type\"\n            8 => \"sort_management\"\n            9 => \"sort_application\"\n            10 => \"split_fee_flag\"\n            11 => \"credit_flag\"\n            12 => \"del_flag\"\n            13 => \"ins_id\"\n            14 => \"ins_date\"\n            15 => \"upd_id\"\n            16 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        4 => App\\Models\\Course {#1208\n          #connection: \"mysql\"\n          #table: \"courses\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:17 [\n            \"id\" => 11\n            \"view_id\" => \"C-11\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"sadasd\"\n            \"name_application\" => \"dasdsa\"\n            \"unit_price\" => \"0.00\"\n            \"principal_price\" => \"0.00\"\n            \"type\" => \"2\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"0\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-25 10:40:14\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:43:13\"\n            \"upd_id\" => 1\n          ]\n          #original: array:17 [\n            \"id\" => 11\n            \"view_id\" => \"C-11\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"sadasd\"\n            \"name_application\" => \"dasdsa\"\n            \"unit_price\" => \"0.00\"\n            \"principal_price\" => \"0.00\"\n            \"type\" => \"2\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"0\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-25 10:40:14\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:43:13\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"type\" => \"App\\Enums\\TypeEnum\"\n            \"split_fee_flag\" => \"App\\Enums\\SplitFeeFlagEnum\"\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:17 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"item_type_id\"\n            3 => \"name_management\"\n            4 => \"name_application\"\n            5 => \"unit_price\"\n            6 => \"principal_price\"\n            7 => \"type\"\n            8 => \"sort_management\"\n            9 => \"sort_application\"\n            10 => \"split_fee_flag\"\n            11 => \"credit_flag\"\n            12 => \"del_flag\"\n            13 => \"ins_id\"\n            14 => \"ins_date\"\n            15 => \"upd_id\"\n            16 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        5 => App\\Models\\Course {#1209\n          #connection: \"mysql\"\n          #table: \"courses\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:17 [\n            \"id\" => 12\n            \"view_id\" => \"C-12\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"sadsa\"\n            \"name_application\" => \"dasdasd\"\n            \"unit_price\" => \"0.00\"\n            \"principal_price\" => \"0.00\"\n            \"type\" => \"2\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"1\"\n            \"credit_flag\" => \"1\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-25 10:40:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:40:27\"\n            \"upd_id\" => 1\n          ]\n          #original: array:17 [\n            \"id\" => 12\n            \"view_id\" => \"C-12\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"sadsa\"\n            \"name_application\" => \"dasdasd\"\n            \"unit_price\" => \"0.00\"\n            \"principal_price\" => \"0.00\"\n            \"type\" => \"2\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"1\"\n            \"credit_flag\" => \"1\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-25 10:40:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:40:27\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"type\" => \"App\\Enums\\TypeEnum\"\n            \"split_fee_flag\" => \"App\\Enums\\SplitFeeFlagEnum\"\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:17 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"item_type_id\"\n            3 => \"name_management\"\n            4 => \"name_application\"\n            5 => \"unit_price\"\n            6 => \"principal_price\"\n            7 => \"type\"\n            8 => \"sort_management\"\n            9 => \"sort_application\"\n            10 => \"split_fee_flag\"\n            11 => \"credit_flag\"\n            12 => \"del_flag\"\n            13 => \"ins_id\"\n            14 => \"ins_date\"\n            15 => \"upd_id\"\n            16 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        6 => App\\Models\\Course {#1210\n          #connection: \"mysql\"\n          #table: \"courses\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:17 [\n            \"id\" => 13\n            \"view_id\" => \"C-13\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"sadsadas\"\n            \"name_application\" => \"sadsad1\"\n            \"unit_price\" => \"1.00\"\n            \"principal_price\" => \"1.00\"\n            \"type\" => \"1\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"0\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-25 10:40:43\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:40:43\"\n            \"upd_id\" => 1\n          ]\n          #original: array:17 [\n            \"id\" => 13\n            \"view_id\" => \"C-13\"\n            \"item_type_id\" => 1\n            \"name_management\" => \"sadsadas\"\n            \"name_application\" => \"sadsad1\"\n            \"unit_price\" => \"1.00\"\n            \"principal_price\" => \"1.00\"\n            \"type\" => \"1\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"0\"\n            \"credit_flag\" => \"0\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-25 10:40:43\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:40:43\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"type\" => \"App\\Enums\\TypeEnum\"\n            \"split_fee_flag\" => \"App\\Enums\\SplitFeeFlagEnum\"\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:17 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"item_type_id\"\n            3 => \"name_management\"\n            4 => \"name_application\"\n            5 => \"unit_price\"\n            6 => \"principal_price\"\n            7 => \"type\"\n            8 => \"sort_management\"\n            9 => \"sort_application\"\n            10 => \"split_fee_flag\"\n            11 => \"credit_flag\"\n            12 => \"del_flag\"\n            13 => \"ins_id\"\n            14 => \"ins_date\"\n            15 => \"upd_id\"\n            16 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        7 => App\\Models\\Course {#1211\n          #connection: \"mysql\"\n          #table: \"courses\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:17 [\n            \"id\" => 14\n            \"view_id\" => \"C-14\"\n            \"item_type_id\" => 2\n            \"name_management\" => \"sdfsdf\"\n            \"name_application\" => \"\"\n            \"unit_price\" => \"1.00\"\n            \"principal_price\" => \"1.00\"\n            \"type\" => \"1\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"1\"\n            \"credit_flag\" => \"1\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-25 10:40:56\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:40:56\"\n            \"upd_id\" => 1\n          ]\n          #original: array:17 [\n            \"id\" => 14\n            \"view_id\" => \"C-14\"\n            \"item_type_id\" => 2\n            \"name_management\" => \"sdfsdf\"\n            \"name_application\" => \"\"\n            \"unit_price\" => \"1.00\"\n            \"principal_price\" => \"1.00\"\n            \"type\" => \"1\"\n            \"sort_management\" => null\n            \"sort_application\" => null\n            \"split_fee_flag\" => \"1\"\n            \"credit_flag\" => \"1\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-25 10:40:56\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-25 10:40:56\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"type\" => \"App\\Enums\\TypeEnum\"\n            \"split_fee_flag\" => \"App\\Enums\\SplitFeeFlagEnum\"\n            \"credit_flag\" => \"App\\Enums\\CreditFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:17 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"item_type_id\"\n            3 => \"name_management\"\n            4 => \"name_application\"\n            5 => \"unit_price\"\n            6 => \"principal_price\"\n            7 => \"type\"\n            8 => \"sort_management\"\n            9 => \"sort_application\"\n            10 => \"split_fee_flag\"\n            11 => \"credit_flag\"\n            12 => \"del_flag\"\n            13 => \"ins_id\"\n            14 => \"ins_date\"\n            15 => \"upd_id\"\n            16 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"admin.payment.advanced-search\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\2e2045f24a3744b3974c54dde1d674a5.php:8$1183\"\n  \"id\" => \"Ux0EzAezWE4BZ6M7bumh\"\n]", "admin.payment.payment-table #EEyPYfbgUEZn0EFg9hZr": "array:4 [\n  \"data\" => array:11 [\n    \"monthIndex\" => null\n    \"brand_id\" => null\n    \"shop_id\" => null\n    \"item_type_id\" => null\n    \"course_id\" => null\n    \"payment_plan_date_from\" => null\n    \"payment_plan_date_to\" => null\n    \"payment_status\" => null\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.payment.payment-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Payment\\PaymentTable\"\n  \"id\" => \"EEyPYfbgUEZn0EFg9hZr\"\n]", "admin.payment.table-data-list #XUtNDm7sOc3QXgK5Y4wG": "array:4 [\n  \"data\" => array:13 [\n    \"monthIndex\" => null\n    \"brand_id\" => null\n    \"shop_id\" => null\n    \"item_type_id\" => null\n    \"course_id\" => null\n    \"payment_plan_date_from\" => null\n    \"payment_plan_date_to\" => null\n    \"payment_status\" => null\n    \"focus_brand_id\" => null\n    \"isSearch\" => null\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.payment.table-data-list\"\n  \"component\" => \"App\\Livewire\\Admin\\Payment\\TableDataList\"\n  \"id\" => \"XUtNDm7sOc3QXgK5Y4wG\"\n]", "common.nav-bar #iHKxU3ebkGwuPOzQSgUR": "array:4 [\n  \"data\" => array:2 [\n    \"breadcrumbs\" => array:2 [\n      0 => array:2 [\n        \"label\" => \"ダッシュボード\"\n        \"url\" => \"http://127.0.0.1:8000/management\"\n      ]\n      1 => array:2 [\n        \"label\" => \"入金管理\"\n        \"url\" => \"http://127.0.0.1:8000/management/payments\"\n      ]\n    ]\n    \"isHideBreadcrumb\" => false\n  ]\n  \"name\" => \"common.nav-bar\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\common\\nav-bar.blade.php:8$119c\"\n  \"id\" => \"iHKxU3ebkGwuPOzQSgUR\"\n]", "common.logout-modal #RMe8rzJsBLoshYxHVv0Y": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.logout-modal\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\acd2c66864e16c9a44c5d2061051a042.php:8$119d\"\n  \"id\" => \"RMe8rzJsBLoshYxHVv0Y\"\n]", "common.confirm #dFXAs0dddpcDUHgheiND": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.confirm\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\21a91b4a087079d3e133812909232442.php:8$11a0\"\n  \"id\" => \"dFXAs0dddpcDUHgheiND\"\n]", "common.toast-message #LF556LUMGg7D5xocxJsj": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.toast-message\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0264863d287fd82f4cefe2d814020f86.php:8$11a1\"\n  \"id\" => \"LF556LUMGg7D5xocxJsj\"\n]"}, "count": 10}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01JZS0SMKMS857Z8GPJBSGPWFJ&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/management/payments_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management/payments", "action_name": "admin.payment.index", "controller_action": "Closure", "uri": "GET management/payments", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management/payments", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>", "middleware": "admin, locale, auth:admin", "duration": "425ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1785751517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1785751517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-847215004 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-847215004\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2053096674 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjliemhKQ2NSQmVpOWwrb2VBQnhKaVE9PSIsInZhbHVlIjoiSXhDNmJqdmtHU3Uvbm8ybWVHYzdoSFU1Ym1qZWF0ai9lRnlRb3JiMDFISVQ4N0dYTFJkcUNZL1M5OVJiVmRhaTJwclM3SXNjOStSRnpFZzcvb3BoUkY5RHZHQTZkdkJ1czZUaGNBZmQ5S0hpZEFrUGp5MTlJSU1aWnNUNWRtVjVvQTRLK1M4NG5JaHBpUzRWdE1vc2tBPT0iLCJtYWMiOiI0YzdhMTM3MWFlNWUxODUwNTJiOWM3NDkzMWU5NDY2NGI5MjljYmU0ZTgxN2QxYjVkYmY1OTg1ZTY5YmM5ZTJkIiwidGFnIjoiIn0%3D; ladybird_session=wlXlRruX2PfaGJavVPNjRis2N3hxByorNqLQ96CL; XSRF-TOKEN=eyJpdiI6IlByWnVQMDE1d1Y0Vk5hWThKZjdYUHc9PSIsInZhbHVlIjoiN2JqT1NjREFzSVVrVXVSYUlrSXRiVFkwdUZSdjlNeDdVZ0VocXFsVXVqbXZNSThiVnE3ajZtS3A4VEVBYXRyaXZHWmszaHVTL3J3dHlYTldPdTN5YmpTQitNaUQ1Z1Bqb01nSmpDUDhKTjlCeEUyelpiK1lEUjh2bzNtVTJsZUsiLCJtYWMiOiI5ZWExNDIwNmE3NTk0MDE1YTQ3ZTA2YWEyMWM5MjVlODQ3NGI4ZTNmY2U5OTk3MzBjMGQwMWQxYzcwODk5YWNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053096674\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-778186634 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-778186634\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-758048287 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 01:56:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImNrV3I2a2FScVdOQWZOa3p4RTVhSGc9PSIsInZhbHVlIjoicjhQSW9CVXJrYnJ1eXVDZ2dPZ3lscCtpdTZmSkttQnRlTnh6bE1ZTHNZcFhTTUJKKzRWa0pkNUZnL3lFVGp6dFp1ZG9KeGZhSEVDazR1UThpNUtZa1BFSk55QTdPWUxhK2U4ZTZLb1dTQSt0QytCa2ZMbEhYQXd5Z3hVRmFUaEoiLCJtYWMiOiJjNmJlZmIxYzI0NjBjMmMxM2RlNmJkNzFkYWI5Yzg1ZWUwNTFkZjNmMTVjNDE2OTI1ZTc2NjNiMjcyYmJlYmM1IiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 03:56:59 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImNrV3I2a2FScVdOQWZOa3p4RTVhSGc9PSIsInZhbHVlIjoicjhQSW9CVXJrYnJ1eXVDZ2dPZ3lscCtpdTZmSkttQnRlTnh6bE1ZTHNZcFhTTUJKKzRWa0pkNUZnL3lFVGp6dFp1ZG9KeGZhSEVDazR1UThpNUtZa1BFSk55QTdPWUxhK2U4ZTZLb1dTQSt0QytCa2ZMbEhYQXd5Z3hVRmFUaEoiLCJtYWMiOiJjNmJlZmIxYzI0NjBjMmMxM2RlNmJkNzFkYWI5Yzg1ZWUwNTFkZjNmMTVjNDE2OTI1ZTc2NjNiMjcyYmJlYmM1IiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 03:56:59 GMT; path=/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-758048287\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1961571344 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01JZS0SMKMS857Z8GPJBSGPWFJ&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/management/payments_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961571344\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management/payments", "action_name": "admin.payment.index", "controller_action": "Closure"}, "badge": null}}