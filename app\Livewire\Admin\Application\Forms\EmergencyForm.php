<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\RelationshipFlagEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Models\Customer;
use App\Rules\TelValidationRule;
use App\Rules\ZipCodeValidationRule;
use Illuminate\Validation\Rule;

class EmergencyForm extends BaseAdminForm
{
    public $emergency_last_name = '';
    public $emergency_first_name = '';
    public $emergency_first_name_kana = '';
    public $emergency_last_name_kana = '';
    public $emergency_zip1 = '';
    public $emergency_zip2 = '';
    public $emergency_pref_id = '';
    public $emergency_city = '';
    public $emergency_address = '';
    public $emergency_building = '';
    public $emergency_tel1 = '';
    public $emergency_tel2 = '';
    public $emergency_tel3 = '';
    public $relationship_flag = RelationshipFlagEnum::SPOUSE;
    public $relationship_other = '';

    public function rules()
    {
        $rules = [
            'emergency_last_name' => 'nullable|string|max:128',
            'emergency_first_name' => 'nullable|string|max:128',
            'emergency_first_name_kana' => 'nullable|katakana|max:128',
            'emergency_last_name_kana' => 'nullable|katakana|max:128',
            'emergency_zip1' => ['nullable', 'max:4', new ZipCodeValidationRule()],
            'emergency_zip2' => ['nullable', 'max:4', new ZipCodeValidationRule()],
            'emergency_pref_id' => 'nullable|exists:prefs,id',
            'emergency_city' => 'nullable|string|max:1000',
            'emergency_address' => 'nullable|string|max:1000',
            'emergency_building' => 'nullable|string|max:1000',
            'emergency_tel1' => ['nullable', 'max:32', new TelValidationRule()],
            'emergency_tel2' => ['nullable', 'max:32', new TelValidationRule()],
            'emergency_tel3' => ['nullable', 'max:32', new TelValidationRule()],
            'relationship_flag' => ['nullable', Rule::in(RelationshipFlagEnum::getValues())],
            'relationship_other' => 'nullable|string|max:512',
        ];

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.emergency.attributes');
    }

    public function fillFromCustomer(Customer $customer): void
    {
        $this->emergency_last_name = $customer->emergency_last_name ?? '';
        $this->emergency_first_name = $customer->emergency_first_name ?? '';
        $this->emergency_first_name_kana = $customer->emergency_first_name_kana ?? '';
        $this->emergency_last_name_kana = $customer->emergency_last_name_kana ?? '';
        $this->emergency_tel1 = $customer->emergency_tel1 ?? '';
        $this->emergency_tel2 = $customer->emergency_tel2 ?? '';
        $this->emergency_tel3 = $customer->emergency_tel3 ?? '';
        $this->emergency_zip1 = $customer->emergency_zip1 ?? '';
        $this->emergency_zip2 = $customer->emergency_zip2 ?? '';
        $this->emergency_pref_id = $customer->emergency_pref_id ?? '';
        $this->emergency_city = $customer->emergency_city ?? '';
        $this->emergency_address = $customer->emergency_address ?? '';
        $this->emergency_building = $customer->emergency_building ?? '';
        $this->relationship_flag = $customer->relationship_flag?->value ?? (string) RelationshipFlagEnum::SPOUSE;
        $this->relationship_other = $customer->relationship_other ?? '';
    }
}
