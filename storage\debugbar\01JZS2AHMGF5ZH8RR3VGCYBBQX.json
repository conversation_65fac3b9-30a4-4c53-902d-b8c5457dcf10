{"__meta": {"id": "01JZS2AHMGF5ZH8RR3VGCYBBQX", "datetime": "2025-07-10 11:23:38", "utime": **********.640955, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[11:23:38] LOG.debug: (Time: 02.08) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.601903, "xdebug_link": null, "collector": "log"}, {"message": "[11:23:38] LOG.debug: (Time: 01.04) SQL: select DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') AS payment_month,\n\n            SUM(loan_schedules.total_amount - loan_schedules.amount_paid) AS loan_balance,\n\n            SUM(loan_schedules.amount_paid) AS paid_amount,\n\n            SUM(IFNULL(loan_arrears.delay_damage_amount, 0)) AS delay_damage_total,\n            SUM(IFNULL(loan_arrears.reissue_fee, 0)) AS reissue_fee_total,\n\n            SUM(DISTINCT IFNULL(applications.sub_total_amount, 0)) AS principal_total,\n            SUM(DISTINCT IFNULL(applications.fee_amount, 0)) AS fee_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.total_amount, 0), 0\n            )) AS current_month_contract_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.sub_total_amount, 0), 0\n            )) AS current_month_principal_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.contract_cancel_amount, 0), 0\n            )) AS cancel_amount_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.forced_contract_cancel_amount, 0), 0\n            )) AS forced_cancel_amount_total,\n\n            SUM(\n                (loan_schedules.total_amount - loan_schedules.amount_paid)\n                * (IFNULL(applications.sub_total_amount, 0) / NULLIF(applications.total_amount, 0))\n            ) AS remaining_principal_estimated from `loan_schedules` left join `loan_arrears` on `loan_arrears`.`loan_schedule_id` = `loan_schedules`.`id` and `loan_arrears`.`del_flag` = 0 left join `applications` on `loan_schedules`.`application_id` = `applications`.`id` and `applications`.`del_flag` = 0 where `loan_schedules`.`del_flag` = '0' group by DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') order by `payment_month` asc {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.629081, "xdebug_link": null, "collector": "log"}, {"message": "[11:23:38] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\088b009d0fa183a9dc89417d41ff2ebb.php on line 25", "message_html": null, "is_string": false, "label": "warning", "time": **********.636111, "xdebug_link": null, "collector": "log"}, {"message": "[11:23:38] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\088b009d0fa183a9dc89417d41ff2ebb.php on line 25", "message_html": null, "is_string": false, "label": "warning", "time": **********.636388, "xdebug_link": null, "collector": "log"}, {"message": "[11:23:38] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\088b009d0fa183a9dc89417d41ff2ebb.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.636624, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.356606, "end": **********.640987, "duration": 0.2843809127807617, "duration_str": "284ms", "measures": [{"label": "Booting", "start": **********.356606, "relative_start": 0, "end": **********.55629, "relative_end": **********.55629, "duration": 0.*****************, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.556306, "relative_start": 0.*****************, "end": **********.640989, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "84.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.565727, "relative_start": 0.****************, "end": **********.568526, "relative_end": **********.568526, "duration": 0.0027990341186523438, "duration_str": "2.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.637981, "relative_start": 0.****************, "end": **********.639092, "relative_end": **********.639092, "duration": 0.0011110305786132812, "duration_str": "1.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x livewire.admin.balance.table-data-list", "param_count": null, "params": [], "start": **********.635041, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/balance/table-data-list.blade.phplivewire.admin.balance.table-data-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fbalance%2Ftable-data-list.blade.php&line=1", "ajax": false, "filename": "table-data-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.balance.table-data-list"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0031200000000000004, "accumulated_duration_str": "3.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5999541, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 66.667}, {"sql": "select DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') AS payment_month,\nSUM(loan_schedules.total_amount - loan_schedules.amount_paid) AS loan_balance,\nSUM(loan_schedules.amount_paid) AS paid_amount,\nSUM(IFNULL(loan_arrears.delay_damage_amount, 0)) AS delay_damage_total,\nSUM(IFNULL(loan_arrears.reissue_fee, 0)) AS reissue_fee_total,\nSUM(DISTINCT IFNULL(applications.sub_total_amount, 0)) AS principal_total,\nSUM(DISTINCT IFNULL(applications.fee_amount, 0)) AS fee_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.total_amount, 0), 0\n)) AS current_month_contract_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.sub_total_amount, 0), 0\n)) AS current_month_principal_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.contract_cancel_amount, 0), 0\n)) AS cancel_amount_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.forced_contract_cancel_amount, 0), 0\n)) AS forced_cancel_amount_total,\nSUM(\n(loan_schedules.total_amount - loan_schedules.amount_paid)\n* (IFNULL(applications.sub_total_amount, 0) / NULLIF(applications.total_amount, 0))\n) AS remaining_principal_estimated from `loan_schedules` left join `loan_arrears` on `loan_arrears`.`loan_schedule_id` = `loan_schedules`.`id` and `loan_arrears`.`del_flag` = 0 left join `applications` on `loan_schedules`.`application_id` = `applications`.`id` and `applications`.`del_flag` = 0 where `loan_schedules`.`del_flag` = '\\'0\\'' group by DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') order by `payment_month` asc", "type": "query", "params": [], "bindings": [0, 0, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 689}, {"index": 16, "namespace": null, "name": "app/Services/BalanceService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\BalanceService.php", "line": 12}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Balance/TableDataList.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Balance\\TableDataList.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.628159, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "LoanScheduleRepository.php:689", "source": {"index": 15, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 689}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FLoanScheduleRepository.php&line=689", "ajax": false, "filename": "LoanScheduleRepository.php", "line": "689"}, "connection": "local-ladybird", "explain": null, "start_percent": 66.667, "width_percent": 33.333}]}, "models": {"data": {"App\\Models\\LoanSchedule": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FLoanSchedule.php&line=1", "ajax": false, "filename": "LoanSchedule.php", "line": "?"}}, "App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"admin.balance.table-data-list #hnohDmhyUY7AzzGHN8ip": "array:4 [\n  \"data\" => array:8 [\n    \"authType\" => \"\"\n    \"from\" => \"\"\n    \"to\" => \"\"\n    \"brandId\" => \"\"\n    \"storeId\" => \"\"\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.balance.table-data-list\"\n  \"component\" => \"App\\Livewire\\Admin\\Balance\\TableDataList\"\n  \"id\" => \"hnohDmhyUY7AzzGHN8ip\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/management/balances\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "286ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-878669651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-878669651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1184622613 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"481 characters\">{&quot;data&quot;:{&quot;authType&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;to&quot;:&quot;&quot;,&quot;brandId&quot;:&quot;&quot;,&quot;storeId&quot;:&quot;&quot;,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;hnohDmhyUY7AzzGHN8ip&quot;,&quot;name&quot;:&quot;admin.balance.table-data-list&quot;,&quot;path&quot;:&quot;management\\/balances&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;props&quot;:[&quot;authType&quot;,&quot;from&quot;,&quot;to&quot;,&quot;brandId&quot;,&quot;storeId&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;e0dfcefcbf29f65c7895230a9e4d1b1359dade0335b3f599bba319f1defed0b3&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"324 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJhdXRoVHlwZSI6IiIsImZyb20iOiIiLCJ0byI6IiIsImJyYW5kSWQiOiIiLCJzdG9yZUlkIjoiIn0seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IjN2VENkZEtkdGFOSTFxRHRCZjBwIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiI2YjhlOTEyNjM2OTA4MGM2N2U1MTlhY2RkNTAyZTJhOGU2NWUxOWMxNDY5M2M3NjE3NDZlYmRhMTRkOTIxZjdmIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184622613\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1047669170 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1043</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/management/balances</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjliemhKQ2NSQmVpOWwrb2VBQnhKaVE9PSIsInZhbHVlIjoiSXhDNmJqdmtHU3Uvbm8ybWVHYzdoSFU1Ym1qZWF0ai9lRnlRb3JiMDFISVQ4N0dYTFJkcUNZL1M5OVJiVmRhaTJwclM3SXNjOStSRnpFZzcvb3BoUkY5RHZHQTZkdkJ1czZUaGNBZmQ5S0hpZEFrUGp5MTlJSU1aWnNUNWRtVjVvQTRLK1M4NG5JaHBpUzRWdE1vc2tBPT0iLCJtYWMiOiI0YzdhMTM3MWFlNWUxODUwNTJiOWM3NDkzMWU5NDY2NGI5MjljYmU0ZTgxN2QxYjVkYmY1OTg1ZTY5YmM5ZTJkIiwidGFnIjoiIn0%3D; ladybird_session=wlXlRruX2PfaGJavVPNjRis2N3hxByorNqLQ96CL; XSRF-TOKEN=eyJpdiI6InNRQncwNk84cTE0MEllYlZoRTRKR2c9PSIsInZhbHVlIjoiNFk5cFBkUnV5a2ZCZDBnZlFTcCtEeDYwaXBNMGd1cVdFS1h4RUNpcUJVN1JrbDZrMm5odGJwMDQvM2Q2VjZ2cENXSmRaaTRadlFmMkVGUmM1RCtlRmhzZ2I5SFJpM2pGTEFYWU92MGJjSlZUU0IveVg1QUp4TTNOayt0TUVXSWUiLCJtYWMiOiI5YjllMmI5ZmYxY2JiNjU1N2M0NTY3ODJlYzBmNzkzYzM1YjVjNTA3N2I2NmUxNDI2ZDI4MjYzYWUxZmNiZmMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047669170\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1586487690 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1586487690\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-573839423 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 02:23:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573839423\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-115217685 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/management/balances</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-115217685\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}