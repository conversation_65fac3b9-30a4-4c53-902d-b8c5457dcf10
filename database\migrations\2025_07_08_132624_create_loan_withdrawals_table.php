<?php

use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('loan_withdrawals', function (BlueprintCustom $table) {
            $table->id();
            $table->integer('customer_id')->comment('顧客ID')->index();
            $table->integer('application_id')->comment('申込ID')->index();
            $table->date('withdrawal_date')->comment('出金日');
            $table->decimal('amount', 10, 2)->comment('入金額');
            $table->char('withdrawal_type', 1)->comment('出金種別');
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_withdrawals');
    }
};
