const express = require('express');
const fs = require('fs');
const path = require('path');
const puppeteer = require('puppeteer');

const app = express();
app.use(express.json());

app.post('/convert-pdf', async (req, res) => {
    const { htmlPath, outputDir, pdfName, landscapeParam = false, pageSize } = req.body;

    if (!htmlPath || !outputDir || !pdfName) {
        return res.status(400).json({ success: false, message: 'Missing parameters' });
    }

    try {
        // Read HTML content
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');

        // Ensure output directory exists
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        // Convert HTML to PDF using Puppeteer
        const browser = await puppeteer.launch({
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            executablePath: '/usr/local/puppeteer/chrome/linux-127.0.6533.88/chrome-linux64/chrome', // path to chrome
        });
        const page = await browser.newPage();
        await page.setContent(htmlContent, { waitUntil: 'domcontentloaded' });
        await page.emulateMediaType('screen');
        const pdfPath = path.join(outputDir, pdfName);
        await page.pdf({
            path: pdfPath,
            format: pageSize,
            landscape: landscapeParam,
            printBackground: true,
        });

        await browser.close();

        return res.json({ success: true, pdfPath });
    } catch (err) {
        console.error(err);
        return res.status(500).json({ success: false, message: err.message });
    }
});

const PORT = process.env.SERVER_EXPRESS_PORT || 3000;
app.listen(PORT, () => {
    console.log(`PDF convert server listening on port ${PORT}`);
});
