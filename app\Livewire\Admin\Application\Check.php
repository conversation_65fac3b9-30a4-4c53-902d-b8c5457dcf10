<?php

namespace App\Livewire\Admin\Application;

use App\Enums\ApplicationTabEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Services\ToastService;
use Livewire\Attributes\Layout;
use Livewire\WithFileUploads;
use App\Services\FileStorageService;
use Illuminate\Http\UploadedFile;

#[Layout('components.layouts.application-layout')]
class Check extends BaseAdminPageComponent
{
    use WithFileUploads;

    public $applicationId;

    public $application = null;

    public function __construct()
    {
        $this->page = ApplicationTabEnum::SERVICE;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
        $this->dispatch('init-select2');
    }

    public function mount($application_id)
    {
        $this->applicationId = $application_id;

        $this->application = $this->getApplication($application_id);

        if (empty($this->application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('application.check', [
            'application' => $this->application,
        ]);
    }

    private function getApplication($application_id)
    {
        return app()->make(ApplicationRepository::class)->find($application_id);
    }

    public function uploadSignature($base64 = null)
    {
        if (!$base64) {
            $this->toastError(__('messages.update_failed'));
            return;
        }

        if (strpos($base64, ',') !== false) {
            [$header, $base64] = explode(',', $base64, 2);
        }
        $base64 = str_replace(' ', '+', $base64);
        $imageData = base64_decode($base64);
        if ($imageData === false) {
            $this->toastError(__('messages.update_failed'));
            return;
        }
        // create tmp file
        $tmpFilePath = tempnam(sys_get_temp_dir(), 'signature_');
        file_put_contents($tmpFilePath, $imageData);
        $uploadedFile = new UploadedFile(
            $tmpFilePath,
            'signature_' . $this->applicationId . '_' . time() . '.png',
            'image/png',
            null,
            true // $test mode
        );
        $fileStorage = app(FileStorageService::class);
        $path = $fileStorage->storeFile($uploadedFile, 'signatures');
        $url = $fileStorage->getFileUrl($path);

        // save to DB
        $app = $this->application;

        if ($app) {
            $app->sign_image_url = $url;
            $app->application_date = now()->format('Y-m-d');
            $app->save();
            $this->toastSuccess(__('messages.update_success'));

            return redirect()->route('admin.customer.application.completed', [$app->id]);
        } else {
            $this->toastError(__('messages.update_failed'));
        }
        // del tmp file
        @unlink($tmpFilePath);
    }
}
