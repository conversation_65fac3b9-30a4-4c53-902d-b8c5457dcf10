<?php

namespace App\Services;

use App\Enums\ApplicationStatusEnum;
use App\Enums\ContractStatusEnum;
use App\Enums\ReInvoiceDocTargetFlagEnum;
use App\Enums\TypeEnum;
use App\Repositories\ApplicationCourseRepository;
use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Services\ApplicationService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ContractService extends CustomService
{
    public function __construct(
        public ApplicationRepository $applicationRepository,
        public CustomerRepository $customerRepository,
    ) {
        parent::__construct();
    }

    public function updateContract($applicationId, $customerId, $params)
    {
        $application = app(ApplicationRepository::class)->find($applicationId);
        $customer = app(CustomerRepository::class)->find($customerId);
        if (empty($customer) || empty($application)) {
            return false;
        }
        $productGenerals = $params['product_general'] ?? [];
        $productOptionals = $params['product_optional'] ?? [];
        $subTotalAmount = $this->calcSubTotalAmount($productGenerals, $productOptionals);

        $syncGeneralData = [];
        $syncOptionalData = [];
        foreach ($productGenerals as $item) {
            $syncGeneralData[$item['course_id']] = [
                'count' => $item['count'],
                'amount' => $item['amount'],
                'type' => TypeEnum::NORMAL
            ];
        }
        foreach ($productOptionals as $item) {
            $syncOptionalData[$item['course_id']] = [
                'count' => $item['count'],
                'amount' => $item['amount'],
                'type' => TypeEnum::OPTIONAL
            ];
        }

        $amounts = $this->calcAmounts($application, $subTotalAmount);

        $usuallyMonthPaymentAmount = 0;
        $bonusMonthPaymentAmount = 0;
        if ($application->bonus_flag->value == 0) {
            // usually_month_payment_amount = ⑥分割支払金合計 / 支払回数
            $usuallyMonthPaymentAmount = $application->payment_count > 0 ? $amounts['fee_total_amount'] / $application->payment_count : 0;
        }

        // usually_month_payment_amount & bonus_month_payment_amount
        if ($application->bonus_flag->value == 1) {
            // usually_month_payment_amount  =（⑥分割支払金合計 - ボーナス支払額）/ 支払回数)
            $usuallyMonthPaymentAmount = $application->payment_count > 0 ? ($amounts['fee_total_amount'] - $application->bonus_payment_amount) / $application->payment_count : 0;
            // bonus_month_payment_amount = ボーナス支払額 / ボーナス支払回数
            $bonusMonthPaymentAmount = $application->bonus_payment_count > 0 ? $application->bonus_payment_amount / $application->bonus_payment_count : 0;
        }

        // first_payment_amount & second_payment_amount
        $paymentStartMonth = Carbon::createFromFormat('Ym', $application->payment_start_month)->format('m'); // 'YYYYMM' -> MM
        $bonusPaymentMonth1Val = $application->bonus_payment_month1; // 1~12
        $bonusPaymentMonth2Val = $application->bonus_payment_month2; // 1~12

        if(($paymentStartMonth == $bonusPaymentMonth1Val) || ($paymentStartMonth == $bonusPaymentMonth2Val)) {
            $firstPaymentAmount = $usuallyMonthPaymentAmount + $bonusMonthPaymentAmount;
            $secondPaymentAmount = $usuallyMonthPaymentAmount;
        }else{
            $firstPaymentAmount = $usuallyMonthPaymentAmount + $bonusMonthPaymentAmount;    
            $secondPaymentAmount = $usuallyMonthPaymentAmount;
        }
        

        DB::beginTransaction();
        try {
            $application->shop_brand_id = $params['shop_brand_id'];
            $application->staff_name = $params['staff_name'];
            $application->application_date = $params['application_date'];
            $application->contract_date = $params['contract_date'];
            $application->contract_cancel_date = $params['contract_cancel_date'];
            $application->contract_cancel_amount = $params['contract_cancel_amount'];
            $application->regist_number = $params['regist_number'];
            $application->payment_company_regist_date = $params['payment_company_regist_date'];
            $application->payment_company_flag = $params['payment_company_flag'];
            $application->contract_comment = $params['contract_comment'];
            $application->sub_total_amount = $subTotalAmount;
            $application->contract_status = $params['isCheckContractStatus5'] ? ContractStatusEnum::REQUEST_CANCELLATION : $application->contract_status;

            // update amount
            $application->fee_amount = $amounts['fee_amount'];
            $application->remaining_amount = $amounts['remaining_amount'];
            $application->fee_total_amount = $amounts['fee_total_amount'];
            $application->total_amount = $amounts['total_amount'];

            // update usuallyMonthPaymentAmount & bonusMonthPaymentAmount
            $application->usually_month_payment_amount = $usuallyMonthPaymentAmount;
            $application->bonus_month_payment_amount = $bonusMonthPaymentAmount;

            // update firstPaymentAmount & secondPaymentAmount
            $application->first_month_payment_amount = floor($firstPaymentAmount);
            $application->second_month_payment_amount = floor($secondPaymentAmount);
            
            if ($params['contract_cancel_date']) {
                $application->contract_status = ContractStatusEnum::CANCELLED;
            }
            $application->re_invoice_doc_target_flag = $params['isCheckReInvoiceDocTargetFlag1'] ? ReInvoiceDocTargetFlagEnum::ELIGIBLE : ReInvoiceDocTargetFlagEnum::NOT_ELIGIBLE;

            $customer->information_input_flag = $params['information_input_flag'];
            $customer->bank_account_name = $params['bank_account_name'];
            $customer->bank_account_name_kana = $params['bank_account_name_kana'];
            $customer->bank_flag = $params['bank_flag'];
            $customer->bank_account_mark1 = $params['bank_account_mark1'];
            $customer->bank_account_mark2 = $params['bank_account_mark2'];
            $customer->bank_account_mark3 = $params['bank_account_mark3'];
            $customer->bank_account_number = $params['bank_account_number'];
            $customer->bank_code = $params['bank_code'];
            $customer->bank_name = $params['bank_name'];
            $customer->branch_code = $params['branch_code'];
            $customer->branch_name = $params['branch_name'];
            $customer->bank_account_type = $params['bank_account_type'];

            $application->courses()->sync($syncGeneralData + $syncOptionalData);

            $application->save();
            $customer->save();
            DB::commit();
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }

        return true;
    }

    public function calcAmounts($application, $subTotalAmount)
    {
        $depositAmount = $application->deposit; // amount2
        $paymentCount = $application->payment_count;

        $applicationService = app(ApplicationService::class);
        $feeAmount = $applicationService->calcFeeAmount($application, $paymentCount, $subTotalAmount, $depositAmount); // amount4
        $remainingAmount = max(0, $subTotalAmount - $depositAmount); // amount3
        $feeTotalAmount = $remainingAmount + $feeAmount; // amount6
        $totalAmount = $depositAmount + $feeTotalAmount; // amount7

        return [
            'fee_amount' => $feeAmount,
            'remaining_amount' => $remainingAmount,
            'fee_total_amount' => $feeTotalAmount,
            'total_amount' => $totalAmount,
        ];
    }   

    private function calcSubTotalAmount(array $productGenerals = [], array $productOptionals = [])
    {
        $totalAmount = 0;

        foreach ($productGenerals as $general) {
            $count = $general['count'] ?? 0;
            $amount = (float) ($general['amount'] ?? 0);
            $totalAmount += ($count * $amount);
        }

        foreach ($productOptionals as $optional) {
            if (!isset($optional['course_id'])) {
                continue;
            }
            $count = $optional['count'] ?? 0;
            $amount = (float) ($optional['amount'] ?? 0);
            $totalAmount += ($count * $amount);
        }

        return $totalAmount;
    }
}
