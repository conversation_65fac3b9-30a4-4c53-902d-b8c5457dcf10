<?php

namespace App\Livewire\Admin\Payment;

use App\Enums\ContractStatusEnum;
use App\Enums\PaymentCompanyFlagEnum;
use App\Enums\PaymentStatusEnum;
use App\Enums\PaymentTypeEnum;
use App\Enums\SidebarMenuEnum;
use App\Livewire\Admin\Payment\Forms\RegistrationForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\LoanPaymentRepository;
use App\Repositories\LoanRefundRepository;
use App\Repositories\LoanScheduleRepository;
use App\Services\ApplicationCourseService;
use App\Services\LoanScheduleService;
use App\Services\PaymentService;
use App\Services\ToastService;
use Gate;
use Livewire\Attributes\On;

class Registration extends BaseAdminPageComponent
{
    public $id;
    public $isCanDelete;
    public $status;

    public $loanSchedule;
    public $paymentDate;
    public $loanScheduleTransaction;
    public $applicationCourse;
    public $loanScheduleLatestPayment;
    public $payment_status;
    public $payment_status_text;
    public $payment_status_badge_class;
    public $payment_type_text;
    public $contract_status_text;
    public $contract_status_colors;
    public $payment_company_flag_text;
    public $perPaymentAmount;
    public RegistrationForm $registrationForm;


    public function __construct()
    {
        $this->page = SidebarMenuEnum::PAYMENT;
        parent::__construct();
        $subTitle = trans2('screens.payment.detail.page_title');
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle = $this->pageTitle . $subTitle . $suffixTitle;
    }

    public function mount($id, $status)
    {
        $this->id = $id;
        $this->status = $status;
        $this->loanSchedule = app()->make(LoanScheduleService::class)->getloanScheduleById($id);

        if (empty($this->loanSchedule)) {
            app(ToastService::class)->error(__('messages.no_data'));
            return $this->redirect(route('admin.contract.index'));
        }
        $loanSchedule = $this->loanSchedule;
        $this->payment_status = $loanSchedule->payment_status->value;
        $this->payment_status_text = PaymentStatusEnum::texts()[$loanSchedule->payment_status->value] ?? '不明';
        $this->payment_status_badge_class = PaymentStatusEnum::colors()[$loanSchedule->payment_status->value] ?? 'status-secondary';
        $this->payment_type_text = PaymentTypeEnum::texts()[$loanSchedule->payment_type->value] ?? '不明';
        $this->contract_status_text = ContractStatusEnum::texts()[$loanSchedule->application->contract_status->value] ?? '不明';
        $this->contract_status_colors = ContractStatusEnum::colors()[$loanSchedule->application->contract_status->value] ?? 'status-primary';
        $this->payment_company_flag_text = PaymentCompanyFlagEnum::texts()[$loanSchedule->payment_company_flag->value] ?? '';
        $this->perPaymentAmount = $this->calculatePerPaymentAmount();

        $applicationId = $this->loanSchedule->application_id;

        $this->loanScheduleTransaction = app()->make(LoanScheduleRepository::class)->getLoanScheduleTransactionsById($id);
        $this->paymentDate = app()->make(LoanPaymentRepository::class)->getPaymentDatebyApplicationId($applicationId);
        $this->applicationCourse = app()->make(ApplicationCourseService::class)->getCoursesByApplication($applicationId);
        $this->loanScheduleLatestPayment = app()->make(LoanScheduleRepository::class)->getLoanSchedulesWithArrearsAndLatestPayment($applicationId);
        $this->isCanDelete = true;
    }

    #[On('regisration-payment')]
    public function registrationPayment()
    {
        if ($this->status != PaymentStatusEnum::NOT_PAID_OVERDUE) {
            $this->registrationForm->validate();
            $params = $this->registrationForm->all();
            $result = app(PaymentService::class)->registerPayment($this->id, $params['payment_date'], $params['comment'], $this->status);
        } else {
            $this->registrationForm->validateOnly('comment');
            $comment = $this->registrationForm->comment;
            $result = app(PaymentService::class)->cancelRegisteredPayment($this->id, $comment);
        }

        if ($result === true) {
            app(ToastService::class)->createSuccess();
            return $this->redirect(route('admin.payment.details', ['id' => $this->id]));
        } else {
            app(ToastService::class)->createError();
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('payment.detail.registration');
    }

    public function calculatePerPaymentAmount(): ?float
    {
        if (!$this->loanSchedule->application->fee_amount || !$this->loanSchedule->application->payment_count || $this->loanSchedule->application->payment_count == 0) {
            return null;
        }

        return $this->loanSchedule->application->fee_amount / $this->loanSchedule->application->payment_count;
    }
}
