<?php

namespace App\Enums;

class ContractStatusEnum extends Enum
{
    const UNDER_CONTRACT = 1;
    const CONTRACT_EXPIRED = 2;
    const CANCELLED = 3;
    const LONG_TERM_DELINQUENT = 4;
    const REQUEST_CANCELLATION = 5;

    public static function texts(): array
    {
        return [
            self::UNDER_CONTRACT => trans2('ContractStatusEnum.UNDER_CONTRACT'),
            self::CONTRACT_EXPIRED => trans2('ContractStatusEnum.CONTRACT_EXPIRED'),
            self::CANCELLED => trans2('ContractStatusEnum.CANCELLED'),
            self::LONG_TERM_DELINQUENT => trans2('ContractStatusEnum.LONG_TERM_DELINQUENT'),
            self::REQUEST_CANCELLATION => trans2('ContractStatusEnum.REQUEST_CANCELLATION'),
        ];
    }

    public static function textList1(): array
    {
        return [
            self::UNDER_CONTRACT => trans2('ContractStatusEnum.UNDER_CONTRACT'),
        ];
    }
    public static function textList2(): array
    {
        return [
            self::REQUEST_CANCELLATION => trans2('ContractStatusEnum.REQUEST_CANCELLATION'),
            self::CANCELLED => trans2('ContractStatusEnum.CANCELLED'),
        ];
    }
    public static function textList3(): array
    {
        return [
            self::LONG_TERM_DELINQUENT => trans2('ContractStatusEnum.LONG_TERM_DELINQUENT'),
        ];
    }
    public static function textList4(): array
    {
        return [
            self::CONTRACT_EXPIRED => trans2('ContractStatusEnum.CONTRACT_EXPIRED'),
        ];
    }


    public static function colors(): array
    {
        return [
            self::UNDER_CONTRACT => 'status-primary',
            self::CONTRACT_EXPIRED => 'status-success',
            self::CANCELLED => 'status-muted',
            self::LONG_TERM_DELINQUENT => 'status-danger',
            self::REQUEST_CANCELLATION => 'status-secondary',
        ];
    }

    public static function canDeleteStatus()
    {
        return [
            self::CONTRACT_EXPIRED,
            self::CANCELLED,
        ];
    }
}
