<?php
namespace App\Enums;

final class PaymentTypeEnum extends Enum
{
    const DIRECT_DEBIT = 1;
    const DIRECT_BANK_TRANSFER = 2;
    const IN_PERSON_PAYMENT = 3;

    public static function texts(): array
    {
        return [
            self::DIRECT_DEBIT => trans2('PaymentTypeEnum.DIRECT_DEBIT'),
            self::DIRECT_BANK_TRANSFER => trans2('PaymentTypeEnum.DIRECT_BANK_TRANSFER'),
            self::IN_PERSON_PAYMENT => trans2('PaymentTypeEnum.IN_PERSON_PAYMENT'),
        ];
    }

}
