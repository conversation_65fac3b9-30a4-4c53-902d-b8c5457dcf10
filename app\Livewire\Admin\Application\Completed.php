<?php

namespace App\Livewire\Admin\Application;

use App\Enums\ApplicationTabEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Services\ApplicationService;
use App\Services\ToastService;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.complete-layout')]
class Completed extends BaseAdminPageComponent
{

    public $applicationId;
    public bool $isLoading = false;

    public function __construct()
    {
        $this->page = ApplicationTabEnum::COMPLETE;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
    }

    public function mount($application_id)
    {
        $this->applicationId = $application_id;

        $application = app()->make(ApplicationRepository::class)->find($application_id);

        if (empty($application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }
    }

    public function completeRequest()
    {
        $this->isLoading = true;
        // set application status to BEFORE_REVIEW
        $app = app()->make(ApplicationService::class)->updateApplicationCompleted($this->applicationId);

        if($app) {
            $this->toastSuccess(__('messages.update_success'));

            // open modal
            $this->dispatch('showCompleteRequestModal');
        } else {
            $this->toastError(__('messages.no_data'));
        }
        $this->isLoading = false;
    }
    
    public function render()
    {
        return $this->viewLivewireAdmin('application.complete');
    }
}
