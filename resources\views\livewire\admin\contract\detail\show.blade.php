@extends('livewire.admin.contract.detail.index')
@section('detail-tab')
    @php
        $func = \App\Enums\FunctionEnum::CONTRACT;
    @endphp
    <div class="card mb-3">
        <div class="card-body">
            <div class="container-min">
                <div class="mb-4 d-flex justify-content-end">
                    <a href="{{ getRoute('contract.edit', ['id' => data_get($contract, 'id') ]) }}" class="btn btn-dark btn-small">{{ trans2('screens.contract.detail.edit_button') }}</a>
                </div>
                <h2 class="heading-2 mt-0">{{ trans2('screens.contract.contract_tab.title') }}</h2>
                  <table class="table table-details">
                    <tbody>
                      <tr>
                        <th class="text-nowrap">{{ transm('_default.attributes.id') }}</th>
                        <td class="text-break">{{ data_get($contract, 'contract_id') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.full_name') }}</th>
                        <td class="text-break"><a href="{{ getRoute('customer.details',['id' => data_get($contract, 'customer.id') ]) }}">{{ data_get($contract, 'customer.full_name') }}</a></td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.full_name_kana') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.full_name_kana') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.contract_company') }}</th>
                        <td class="text-break">{{ trans2('company') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.shop_brand_id') }}</th>
                        <td class="text-break">{{ data_get($contract, 'shopBrand.name') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.item_type_name') }}</th>
                        <td class="text-break">
                            @foreach ($contract->courses as $course)
                                <div>
                                    {{ data_get($course, 'itemType.name') }}
                                </div>
                            @endforeach
                        </td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.course_name') }}</th>
                        <td class="text-break">
                            @foreach ($contract->courses as $course)
                                <div>
                                    {{ data_get($course, 'name_application') ?: data_get($course, 'name_management') }}
                                </div>
                            @endforeach
                        </td>
                      </tr>
                      <tr>
                          <th class="text-nowrap">{{ transm('applications.attributes.payment_company_flag') }}</th>
                          <td class="text-break">{{ data_get($contract, 'payment_company_flag') }}</td>
                      </tr>
                      <tr>
                          <th class="text-nowrap">{{ transm('applications.attributes.regist_number') }}</th>
                          <td class="text-break">{{ data_get($contract, 'regist_number') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.information_input_flag') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.information_input_flag.text') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.staff_name') }}</th>
                        <td class="text-break">{{ data_get($contract, 'staff_name') }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.contract_status') }}</h2>
                  <table class="table table-details">
                    <tbody>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.contract_status') }}</th>
                        <td class="text-break">{{ data_get($contract, 'contract_status.text') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.application_date') }}</th>
                        <td>{{ data_get($contract, 'application_date') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.contract_date') }}</th>
                        <td>{{ data_get($contract, 'contract_date') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.contract_cancel_date') }}</th>
                        <td>{{ data_get($contract, 'contract_cancel_date') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.contract_cancel_amount') }}</th>
                        <td>{{ number_format((int) removeTrailingZeros(data_get($contract, 'contract_cancel_amount'))) }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.payment_information') }}</h2>
                  <h3 class="heading-3 mt-0">{{ trans2('screens.contract.contract_tab.loan_contact_details') }}</h3>
                  <div class="row">
                    <div class="col-6">
                      <table class="table table-details mb-4">
                        <tbody>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.total_amount') }}</th>
                            <td><span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'total_amount')) }}</td>
                          </tr>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.principal_including_tax') }}</th>
                            <td><span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'principal_including_tax')) }}</td>
                          </tr>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.fee_amount') }}</th>
                            <td><span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'fee_amount')) }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="col-6">
                      <table class="table table-details mb-4">
                        <tbody>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.payment_count') }}</th>
                            <td>{{ data_get($contract, 'payment_count') }}{{ trans2('times') }}</td>
                          </tr>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.payment_start_month') }}</th>
                            <td>{{ formatYearMonth(data_get($contract, 'payment_start_month')) }}</td>
                          </tr>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.payment_last_month') }}</th>
                            <td>{{ formatYearMonth(data_get($contract, 'payment_last_month')) }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="col-6">
                      <table class="table table-details">
                        <tbody>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.bonus_addition_amount') }}</th>
                            <td><span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'bonus_addition_amount')) }}</td> {{-- bonus_month_payment_amount *  bonus_payment_amount--}}
                          </tr>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.bonus_payment_month') }}</th>
                            <td>{{ data_get($contract, 'bonus_payment_month1.text') }}@if(data_get($contract, 'bonus_payment_month1') || data_get($contract, 'bonus_payment_month2'))・@endif
                                {{ data_get($contract, 'bonus_payment_month2.text') }}</td>
                          </tr>
                          <tr>
                            <th class="text-nowrap w-50">{{ transm('contract.attributes.bonus_payment_start_month') }}</th>
                            <td>{{ formatYearMonth(data_get($contract, 'bonus_payment_start_month')) }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <h3 class="heading-3">{{ trans2('screens.contract.contract_tab.installment_payment_amount') }}</h3>
                  <table class="table table-border mb-5">
                    <thead class="text-nowrap">
                      <tr>
                        <th class="text-nowrap">{{ trans2('screens.contract.contract_tab.type') }}</th>
                        <th class="text-nowrap w-px-140 text-center">{{ transm('contract.attributes.total') }}</th>
                        <th class="text-nowrap w-px-140 text-center">{{ transm('contract.attributes.origin_include_tax') }}</th>
                        <th class="text-nowrap w-px-140 text-center">{{ transm('contract.attributes.split_fee') }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>{{ transm('contract.attributes.first_payment_amount') }}</td>
                        <td class="text-center">
                          <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'first_month_payment_amount')) }}
                        </td>
                        <td class="text-center">
                          <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'first_payment_origin_include_tax')) }}
                        </td>
                        <td class="text-center">
                          <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'split_fee')) }}
                        </td>
                      </tr>
                      <tr>
                        <td>{{ transm('contract.attributes.second_payment_amount') }}</td>
                        <td class="text-center">
                          <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'second_month_payment_amount')) }}
                        </td>
                        <td class="text-center">
                          <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'second_payment_origin_include_tax')) }}
                        </td>
                        <td class="text-center">
                          <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'split_fee')) }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <h2 class="heading-2">{{ data_get($contract, 'payment_company_flag.text') }}の情報</h2>
                  <table class="table table-details">
                    <tbody>
                      <tr>
                        <th class="text-nowrap">{{ data_get($contract, 'payment_company_flag.text') }}登録番号</th>
                        <td class="text-break">{{ data_get($contract, 'regist_number') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ data_get($contract, 'payment_company_flag.text') }}登録日</th>
                        <td>{{ data_get($contract, 'payment_company_regist_date') }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.customer_information') }}</h2>
                  <table class="table table-details">
                    <tbody>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_account_name') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.bank_account_name') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_account_name_kana') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.bank_account_name_kana') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_flag') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.bank_flag.text') }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.jp_post_bank') }}</h2>
                  <table class="table table-details">
                    <tbody>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_account_mark') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.bank_account_mark') }}</td> {{-- bank_account_mark1-bank_account_mark2 --}}
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_account_number') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.bank_account_number') }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.banks') }}</h2>
                  <table class="table table-details">
                    <tbody>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_code') }}</th>
                        <td>{{ data_get($contract, 'customer.bank_code') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_name') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.bank_name') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.branch_code') }}</th>
                        <td>{{ data_get($contract, 'customer.branch_code') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.branch_name') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.branch_name') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_account_type') }}</th>
                        <td class="text-break">{{ data_get($contract, 'customer.bank_account_type.text') }}</td>
                      </tr>
                      <tr>
                        <th class="text-nowrap">{{ transm('contract.attributes.bank_account_number') }}</th>
                        <td>{{ data_get($contract, 'customer.bank_account_number') }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.remarks') }}</h2>
                  <div class="mb-5">{{ data_get($contract, 'contract_comment') }}</div>
                <div class="text-end">
                    <span class="small me-4">{{ transm('_default.attributes.ins_date') }}：{{ $contract->ins_date ? \Carbon\Carbon::parse($contract->ins_date)->format('Y/m/d') : '' }}</span>
                    <span class="small me-4">{{ transm('_default.attributes.upd_date') }}：{{ $contract->upd_date ? \Carbon\Carbon::parse($contract->upd_date)->format('Y/m/d') : '' }}</span>
                    <button type="button" class="small btn-text fc-primary" data-bs-toggle="modal" data-bs-target="#updateHistoryModal">{{ trans2('button.history') }}</button>
                </div>
            </div>
        </div>
        <livewire:common.tracking-history-modal :function="$func" :id="data_get($contract, 'id')" />
    </div>
@endsection

