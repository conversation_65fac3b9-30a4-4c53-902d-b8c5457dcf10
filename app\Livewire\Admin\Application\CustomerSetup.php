<?php

namespace App\Livewire\Admin\Application;

use App\Enums\ApplicationTabEnum;
use App\Livewire\Admin\Application\Forms\CustomerSetupForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Services\CustomerService;
use App\Services\PrefService;
use App\Services\ToastService;
use Illuminate\Http\Request;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;

#[Layout('components.layouts.application-layout')]
class CustomerSetup extends BaseAdminPageComponent
{
    public $customerId = null;

    public $applicationId;

    public $application = null;

    public CustomerSetupForm $basicForm;

    public function __construct()
    {
        $this->page = ApplicationTabEnum::CUSTOMER_SETUP;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
        $this->dispatch('init-select2');
    }

    public function mount($application_id)
    {
        $this->applicationId = $application_id;
        $this->customerId    = request()->get('customer_id');
        $this->application   = $this->getApplication($application_id);

        if (!$this->application) {
            app(ToastService::class)->error(__('messages.no_data'));
            return $this->redirect(route('admin.customer.index'));
        }

        $customerId = $this->customerId ?? $this->application->customer_id;

        if ($customerId) {
            $customer = app(CustomerRepository::class)->getBasicInfoApplicationCustomer($customerId);

            if ($customer) {
                $this->basicForm->fillFromCustomer($customer);
            }
        }
    }

    public function validateSave()
    {
        $this->basicForm->validate();
        $this->dispatch('app-basic-store', $this->basicForm);
    }

    #[On('app-basic-store')]
    public function store()
    {
        $params = $this->basicForm->all();
        $customerService = app(CustomerService::class);

        $customer = empty($this->customerId) ? $customerService->update($this->application->customer_id, $params) : $customerService->store($params);

        if ($this->application && $customer) {
            app(ApplicationRepository::class)->update($this->application->id, [
                'customer_id' => $customer->id,
            ]);
        }

        if ($customer) {
            app(ToastService::class)->createSuccess();

            return redirect()->route('admin.customer.application.setup.emergency', [
                'application_id' => $this->applicationId,
                'customer_id'    => $customer->id,
            ]);
        }

        app(ToastService::class)->createError();
    }

    public function render()
    {
        return $this->viewLivewireAdmin('application.customer-setup', [
            'listPrefs' => app(PrefService::class)->getAllPrefs(),
        ]);
    }

    private function getApplication($application_id)
    {
        return app()->make(ApplicationRepository::class)->find($application_id);
    }
}
