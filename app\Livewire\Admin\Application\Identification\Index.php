<?php

namespace App\Livewire\Admin\Application\Identification;

use App\Enums\ApplicationTabEnum;
use App\Enums\ConfirmationDocFlagEnum;
use App\Enums\CustomerFlagEnum;
use App\Livewire\Admin\Application\Forms\IdentificationForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Services\ApplicationService;
use App\Services\ToastService;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\WithFileUploads;

#[Layout('components.layouts.application-layout')]
class Index extends BaseAdminPageComponent
{
    use WithFileUploads;

    public IdentificationForm $identificationForm;

    public $applicationId;

    public $application = null;

    public function __construct()
    {
        $this->page = ApplicationTabEnum::SERVICE;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
        $this->dispatch('init-select2');
    }

    public function mount($application_id)
    {
        $this->applicationId = $application_id;

        $this->application = $this->getApplication($application_id);

        if (empty($this->application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }

        // prepare data for edit
        $this->prepareDataUpdate();
    }

    private function prepareDataUpdate()
    {
        $this->identificationForm->fill($this->application->toArray());
        $this->identificationForm->confirmation_doc_flag = $this->application->confirmation_doc_flag?->value ?? ConfirmationDocFlagEnum::DRIVER_LICENSE;
    }

    public function validateSave()
    {
        // prepare data before save
        if($this->identificationForm?->confirmation_doc_flag != ConfirmationDocFlagEnum::OTHER) {
            $this->identificationForm->confirmation_doc_other = null;
        }

        $this->identificationForm->validate();

        $this->store();
    }

    protected function store()
    {
        $body = $this->identificationForm->toArray();

        // if files are not provided, use the existing files from the form
        if (empty($body['files']) || (is_array($body['files']) && empty($body['files'][0]))) {
            $body['files'] = $this->identificationForm->files;
        }

        $application = app()->make(ApplicationService::class)->createApplicationIdentification($this->applicationId, $body);

        if($application) {
            app(ToastService::class)->createSuccess();
            // redirect to tab service
            if (!empty($application->customer_id)) {
                return redirect()->route('admin.customer.application.setup', ['application_id' => $application->id]);
            }
            return redirect()->route('admin.customer.application.customer', ['application_id' => $application->id]);
        }
        app(ToastService::class)->createError();
    }

    #[On('app-identification-reload')]
    public function reloadApplicationList()
    {
        $this->application = $this->getApplication($this->applicationId);
    }

    public function render()
    {
        return $this->viewLivewireAdmin('application.identification.index', [
            'application' => $this->application,
        ]);
    }

    private function getApplication($application_id)
    {
        return app()->make(ApplicationRepository::class)->find($application_id);
    }
}
