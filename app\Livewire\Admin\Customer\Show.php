<?php

namespace App\Livewire\Admin\Customer;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\CustomerRepository;
use App\Services\ApplicationService;
use App\Services\ToastService;
use Gate;

class Show extends BaseAdminPageComponent
{
    public $id;
    public $isCanDelete;
    public $brandIds;
    public $shopIds;

    protected $customer = null;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CUSTOMER;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
    }

    public function mount($id)
    {
        $this->id = $id;
        $this->customer = app()->make(CustomerRepository::class)->getBasicInfoCustomer($id);

        if (empty($this->customer)) {
            app(ToastService::class)->error(__('messages.no_data'));
            return $this->redirect(route('admin.customer.index'));
        }

        $this->brandIds = $this->customer->applications->pluck('brand_id');
        $this->shopIds = $this->customer->applications->pluck('shop_id');
        Gate::authorize('allowed-update', [SidebarMenuEnum::CUSTOMER, $this->brandIds, $this->shopIds]);

        $countApp = app(ApplicationService::class)->getCountAppByCustomerId($this->id);
        $this->isCanDelete = !$countApp;
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.detail.show', [
            'customer' => $this->customer,
        ]);
    }
}
