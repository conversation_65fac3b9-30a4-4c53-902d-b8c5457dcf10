<?php

namespace App\Livewire\Admin\Contract;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\ApplicationService;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use App\Services\ToastService;

class Delete extends BaseAdminPageComponent
{
    #[Reactive]
    public $id;

    #[Reactive]
    public $isCanDelete;

    #[On('delete')]
    public function delete()
    {

        try {
            $contract = app(ApplicationService::class)->getContractByApplication($this->id);

            if (empty($contract)) {
                app(ToastService::class)->error(__('messages.no_data'));
                $this->redirect(route('admin.contract.index'));
                return;
            }

            $contract->delete();

            app(ToastService::class)->deleteSuccess();

            return redirect()->route('admin.contract.index');
        } catch (\Throwable $th) {
            logError($th);
            app(ToastService::class)->deleteError();
            return;
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('contract.delete');
    }
}
