<?php

namespace App\Services;

use App\Enums\AutoManualFlagEnum;
use App\Enums\LoanTransactionTypeEnum;
use App\Enums\PaymentCompanyFlagEnum;
use App\Enums\PaymentStatusEnum;
use App\Enums\PaymentTypeEnum;
use App\Enums\RefundFlagEnum;
use App\Enums\ResultStatusEnum;
use App\Models\LoanArrear;
use App\Repositories\LoanPaymentRepository;
use App\Repositories\LoanScheduleRepository;
use App\Repositories\LoanPaymentAllocationRepository;
use App\Repositories\LoanArrearRepository;
use App\Repositories\LoanOverPaymentRepository;
use App\Repositories\LoanRefundRepository;
use App\Repositories\LoanTransactionLogRepository;
use App\Repositories\ApplicationRepository;
use App\Repositories\LoanWithdrawalRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Throwable;

class PaymentService extends CustomService
{
    public function __construct(
        public LoanPaymentRepository $loanPaymentRepository,
    ) {
        parent::__construct();
    }

    public function createPayment($applicationId, $customerId, $params)
    {
        try {
            if ($params['type'] == LoanTransactionTypeEnum::DEPOSIT) {
                $schedules = app(LoanScheduleRepository::class)->getListByApplicationAndPaymentStatus($applicationId, PaymentStatusEnum::waitingValues());

                // save loan payment
                $createdLoanPayment = $this->loanPaymentRepository->create(
                    [
                        'customer_id' => $customerId,
                        'application_id' => $applicationId,
                        'payment_date' => $params['payment_date'],
                        'amount' => $params['amount'],
                        'auto_manual_flag' => AutoManualFlagEnum::MANUAL,
                        'payment_type' => $params['payment_type'],
                        'payment_company_flag' => PaymentCompanyFlagEnum::OTHER,
                    ]
                );
                $remainingAmount = $params['amount'];

                foreach ($schedules as $schedule) {
                    $scheduleId = $schedule['id'];
                    $due = $schedule['total_amount'];
                    $paid = $schedule['amount_paid'];
                    $remain = $due - $paid;
                    $applied = 0;

                    if ($remainingAmount <= 0) {
                        break;
                    }

                    if ($remainingAmount >= $remain) {
                        $applied = $remain;
                    } else {
                        $applied = $remainingAmount;
                    }

                    $createdAllocation = app(LoanPaymentAllocationRepository::class)->create(
                        [
                            'customer_id' => $customerId,
                            'application_id' => $applicationId,
                            'loan_schedule_id' => $scheduleId,
                            'loan_payment_id' => $createdLoanPayment->id,
                            'amount' => $applied,
                            'before_payment_status' => $schedule['payment_status'],
                        ]
                    );

                    app(LoanTransactionLogRepository::class)->create([
                        'customer_id' => $customerId,
                        'application_id' => $applicationId,
                        'type' => LoanTransactionTypeEnum::DEPOSIT,
                        'loan_payment_allocation_id' => $createdAllocation->id,
                        'loan_payment_id' => $createdLoanPayment->id,
                        'loan_schedule_id' => $scheduleId,
                    ]);

                    $newPaid = $paid + $applied;
                    $newStatus = ($newPaid >= $due) ? PaymentStatusEnum::PAID : PaymentStatusEnum::PAID_PARTIAL_PAYMENT;

                    app(LoanScheduleRepository::class)->update($scheduleId, [
                        'amount_paid' => $newPaid,
                        'payment_status' => $newStatus,
                    ]);

                    app(LoanPaymentRepository::class)->update($createdLoanPayment->id, [
                        'payment_status' => $newStatus,
                    ]);

                    if (($applied < $remain) && $scheduleId) {
                        $arrears = $remain - $applied;

                        $createdArrear = app(LoanArrearRepository::class)->create([
                            'customer_id' => $customerId,
                            'application_id' => $applicationId,
                            'loan_schedule_id' => $scheduleId,
                            'amount' => $arrears,
                            'delay_damage_amount' => 0,
                            'reissue_fee' => 0,
                        ]);

                        app(LoanTransactionLogRepository::class)->create([
                            'customer_id' => $customerId,
                            'application_id' => $applicationId,
                            'type' => LoanTransactionTypeEnum::DEPOSIT,
                            'loan_arrear_id' => $createdArrear->id,
                            'loan_payment_id' => $createdLoanPayment->id,
                            'loan_schedule_id' => $scheduleId,
                        ]);
                    }

                    $remainingAmount -= $applied;
                }

                if ($remainingAmount > 0) {
                    $createdLoanOverpayment = app(LoanOverPaymentRepository::class)->create([
                        'customer_id' => $customerId,
                        'application_id' => $applicationId,
                        'loan_payment_id' => $createdLoanPayment->id,
                        'amount' => $remainingAmount,
                        'refunded_flag' => RefundFlagEnum::UNREFUNDED,
                    ]);

                    app(LoanTransactionLogRepository::class)->create([
                        'customer_id' => $customerId,
                        'application_id' => $applicationId,
                        'type' => $params['type'],
                        'loan_overpayment_id' => $createdLoanOverpayment->id,
                        'loan_payment_id' => $createdLoanPayment->id,
                    ]);
                }

            } elseif ($params['type'] == LoanTransactionTypeEnum::WITHDRAWAL) {
                $createdLoanWithdrawal = app(LoanWithdrawalRepository::class)->create(
                    [
                        'customer_id' => $customerId,
                        'application_id' => $applicationId,
                        'withdrawal_date' => $params['payment_date'],
                        'amount' => $params['amount'],
                        'withdrawal_type' => $params['payment_type'],
                    ]
                );

                $overpayments = app(LoanOverPaymentRepository::class)->getListByApplicationAndPaymentStatus($applicationId, RefundFlagEnum::UNREFUNDED);

                $remainingRefund = $params['amount'];
                foreach ($overpayments as $op) {
                    $opId = $op['id'];
                    $opAmount = $op['amount'];

                    if ($remainingRefund <= 0)
                        break;

                    $refundToApply = min($opAmount, $remainingRefund);

                    $lastLoanScheduleId = app(LoanPaymentAllocationRepository::class)->getMaxLoanScheduleIdByLoanPayment($op['loan_payment_id']);

                    $createdLoanRefund = app(LoanRefundRepository::class)->create([
                        'customer_id' => $customerId,
                        'application_id' => $applicationId,
                        'loan_schedule_id' => $lastLoanScheduleId,
                        'loan_payment_id' => $op['loan_payment_id'],
                        'loan_withdrawal_id' => $createdLoanWithdrawal->id,
                        'amount' => $refundToApply,
                        'payment_date' => $params['payment_date'],
                        'comment' => trans2('PaymentStatusEnum.REFUND'),
                    ]);

                    app(LoanOverPaymentRepository::class)->update($opId, [
                        'refunded_flag' => RefundFlagEnum::REFUNDED,
                        'loan_refund_id' => $createdLoanRefund->id,
                        'payment_date' => $params['payment_date'],
                    ]);

                    app(LoanTransactionLogRepository::class)->create([
                        'customer_id' => $customerId,
                        'application_id' => $applicationId,
                        'type' => $params['type'],
                        'loan_refund_id' => $createdLoanRefund->id,
                        'loan_overpayment_id' => $opId,
                        'loan_withdrawal_id' => $createdLoanWithdrawal->id,
                    ]);

                    $remainingRefund -= $refundToApply;
                }

                if ($remainingRefund > 0) {
                    return [
                        'result' => false,
                        'message' => '過入金が不足して返金できません。残り: ' . formatCurrency($remainingRefund),
                    ];
                }
            } else {
                return [
                    'result' => false,
                    'message' => '',
                ];
            }
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            return [
                'result' => false,
                'message' => '',
            ];
        }

        return [
            'result' => true,
            'message' => '',
        ];
    }

    public function editPayment($applicationId, $customerId, $params, $id)
    {
        switch($params['type_flag']){
            case LoanTransactionTypeEnum::DEPOSIT:
                unset($params['type_flag']);
                return $this->editLoanPayment($applicationId, $customerId, $params, $id);
            case LoanTransactionTypeEnum::WITHDRAWAL:
                unset($params['type_flag']);
                return $this->editLoanRefund($applicationId, $customerId, $params, $id);
            default:
                return [
                    'result' => false,
                    'message' => '',
                ];
        }
    }

    private function editLoanPayment($applicationId, $customerId, $params, $loanPaymentId)
    {
        $loanPaymentAllocations = app(LoanPaymentAllocationRepository::class)->getAllByLoanPaymentId($loanPaymentId);

        foreach ($loanPaymentAllocations as $allocation) {
            $schedule = app(LoanScheduleRepository::class)->find($allocation->loan_schedule_id);
            $schedule->fill([
                'payment_status' => $allocation->before_payment_status?->value,
                'amount_paid' => $schedule->amount_paid - $allocation->amount,
            ])->save();

            app(LoanPaymentAllocationRepository::class)->delete($allocation->id);

            $deletingLoanArrear = app(LoanArrearRepository::class)->findByField('loan_schedule_id',$allocation->loan_schedule_id)->first();

            if($deletingLoanArrear){
                app(LoanArrearRepository::class)->delete($deletingLoanArrear->id);
            }
        }

        $deletingLoanOverpayment = app(LoanOverPaymentRepository::class)->findDeletingByLoanPaymentId($loanPaymentId);

        if($deletingLoanOverpayment){
            app(LoanOverPaymentRepository::class)->delete($deletingLoanOverpayment->id);
        }
        $deletingLogs = app(LoanTransactionLogRepository::class)->findByField('loan_payment_id',$loanPaymentId);

        foreach ($deletingLogs as $log) {
            app(LoanTransactionLogRepository::class)->delete($log->id);
        }

        app(LoanPaymentRepository::class)->delete($loanPaymentId);

        return $this->createPayment($applicationId, $customerId, $params);
    }

    private function editLoanRefund($applicationId, $customerId, $params, $loanWithdrawalId)
    {
        $loanRefunds = app(LoanRefundRepository::class)->getAllByLoanWithdrawalId($loanWithdrawalId);

        foreach ($loanRefunds as $refund) {
            $ops = app(LoanOverPaymentRepository::class)->findByField('loan_refund_id',$refund->id);
            foreach ($ops as $op) {
                $op->fill([
                    'refunded_flag' => RefundFlagEnum::UNREFUNDED,
                    'loan_refund_id' => null,
                    'payment_date' => null,
                ])->save();
            }

            app(LoanRefundRepository::class)->delete($refund->id);
        }

        $deletingLogs = app(LoanTransactionLogRepository::class)->findByField('loan_withdrawal_id',$loanWithdrawalId);

        foreach ($deletingLogs as $log) {
            app(LoanTransactionLogRepository::class)->delete($log->id);
        }

        app(LoanWithdrawalRepository::class)->delete($loanWithdrawalId);

        return $this->createPayment($applicationId, $customerId, $params);
    }

    private function calcSubTotalAmount(array $productGenerals = [], array $productOptionals = [])
    {
        $totalAmount = 0;

        foreach ($productGenerals as $general) {
            $count = $general['count'] ?? 0;
            $amount = (float) ($general['amount'] ?? 0);
            $totalAmount += ($count * $amount);
        }

        foreach ($productOptionals as $optional) {
            if (!isset($optional['course_id'])) {
                continue;
            }
            $count = $optional['count'] ?? 0;
            $amount = (float) ($optional['amount'] ?? 0);
            $totalAmount += ($count * $amount);
        }

        return $totalAmount;
    }

    public function registerPayment($loanScheduleId, $paymentDate, $comment, $status)
    {
        DB::beginTransaction();

        try {
            $loanSchedule = app(LoanScheduleRepository::class)->getLoanScheduleById($loanScheduleId);

            $customerId = $loanSchedule->customer_id;
            $applicationId = $loanSchedule->application_id;
            $totalAmount = $loanSchedule->total_amount;
            $paymentType = $loanSchedule->payment_type;
            $paymentCompanyFlag = $loanSchedule->payment_company_flag;
            $userId = auth()->id();

            $loanPaymentId = app(LoanPaymentRepository::class)->insertLoanPayment([
                'customer_id'            => $customerId,
                'application_id'         => $applicationId,
                'payment_date'           => $paymentDate,
                'amount'                  => $totalAmount,
                'auto_manual_flag'       => AutoManualFlagEnum::AUTOMATIC,
                'payment_type'           => $paymentType,
                'payment_company_flag'   => $paymentCompanyFlag,
                'payment_status'         => $status,
                'del_flag'                => 0,
                'ins_date'                => now(),
                'ins_id'                  => $userId,
            ]);

            $allocation = app(LoanPaymentAllocationRepository::class)->insertPaymentAllocation([
                'customer_id'     => $customerId,
                'application_id'  => $applicationId,
                'loan_schedule_id' => $loanScheduleId,
                'loan_payment_id' => $loanPaymentId,
                'amount'          => $totalAmount,
                'before_payment_status' => $loanSchedule->payment_status,
                'del_flag'        => 0,
                'ins_date'        => now(),
                'ins_id'          => $userId,
            ]);

            app(LoanScheduleRepository::class)->updateLoanSchedule($loanSchedule->id, [
                'amount_paid'    => $totalAmount,
                'payment_status' => $status,
                'upd_date'       => now(),
                'upd_id'         => $userId,
            ]);

            app(LoanTransactionLogRepository::class)->insertTransactionLog([
                'customer_id'     => $customerId,
                'application_id'  => $applicationId,
                'type'            => 1,
                'loan_schedule_id' => $loanScheduleId,
                'loan_payment_id' => $loanPaymentId,
                'loan_payment_allocation_id' => $allocation->id,
                'del_flag'        => 0,
                'ins_date'        => now(),
                'ins_id'          => $userId,
                'comment'         => $comment,
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            logError($e);
            DB::rollBack();
            throw $e;
            return false;
        }
    }
    public function cancelRegisteredPayment($scheduleId, $comment)
    {
        $userId = auth()->id();

        try {
            DB::beginTransaction();

            $loanSchedule = app(LoanScheduleRepository::class)->getLoanScheduleById($scheduleId);

            $allocations = app(LoanPaymentAllocationRepository::class)->getLoanPaymentAllocationsByloanSchedule($scheduleId);

            $totalReversalAmount = $allocations->sum('amount');

            $loanSchedule->amount_paid = max(0, $loanSchedule->amount_paid - $totalReversalAmount);
            $loanSchedule->payment_status = PaymentStatusEnum::NOT_PAID_OVERDUE;
            $loanSchedule->upd_id = $userId;
            $loanSchedule->upd_date = now();
            $loanSchedule->save();

            foreach ($allocations as $alloc) {
                $alloc->del_flag = '1';
                $alloc->upd_id = $userId;
                $alloc->upd_date = now();
                $alloc->save();

                $paymentId = $alloc->loan_payment_id;
                $overpayments = app(LoanOverPaymentRepository::class)->find($paymentId);

                if ($overpayments) {
                    app(LoanOverPaymentRepository::class)->updateLoanOverPayment($paymentId, $scheduleId, $userId);
                }

                $otherAllocations = app(LoanPaymentAllocationRepository::class)->countLoanPaymentAllocations($paymentId, $scheduleId);

                if ($otherAllocations === 0) {
                    app(LoanPaymentRepository::class)->updateLoanPayment($paymentId, $userId);
                }

                $payment = app(LoanPaymentRepository::class)->getLoanPaymentById($paymentId);
                if ($payment) {
                    $data = [
                        'customer_id'      => $payment->customer_id,
                        'application_id'   => $payment->application_id,
                        'loan_schedule_id' => $scheduleId,
                        'loan_payment_id'  => $paymentId,
                        'loan_payment_allocation_id' => $alloc->id,
                        'type'             => LoanTransactionTypeEnum::CANCEL,
                        'comment'          => $comment,
                        'del_flag'         => '0',
                        'ins_id'           => $userId,
                        'ins_date'         => now(),
                    ];

                    app(LoanTransactionLogRepository::class)->createLoanTransactionLog($data);
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            logError($e);
            return false;
        }
    }

    public function handleImportCsv($data = [])
    {
        $appData = app(ApplicationRepository::class)->getCustomerIdByRegisNumber($data['regist_number']);
        if(!$appData){
            return;
        }
        $loanSchedules = app(LoanScheduleRepository::class)->getLoanScheduleByApplicationIdAndPaymentPlanDate($appData->id, $data['payment_plan_date']);
        if($data['result_status'] != 0) {
            foreach ($loanSchedules as $loanSchedule) {
                $updatingLoanSchedule = app(LoanScheduleRepository::class)->find($loanSchedule->id);
                $updatingLoanSchedule->fill([
                    'result_status' => $data['result_status'],
                    'payment_status' => PaymentStatusEnum::NOT_PAID_OVERDUE,
                ])->save();
            }

        } elseif ($data['result_status'] == 0){
            $createdLoanPayment = app(LoanPaymentRepository::class)->create([
                'customer_id' => $appData->customer_id,
                'application_id' => $appData->id,
                'payment_date' => Carbon::createFromFormat('Ym', $data['payment_plan_date'])->day(27),
                'amount' => $data['total_amount'],
                'auto_manual_flag' => AutoManualFlagEnum::AUTOMATIC,
                'payment_type' => PaymentTypeEnum::DIRECT_DEBIT,
                'payment_company_flag' => PaymentCompanyFlagEnum::JACCS,
                'result_status' => $data['result_status'],
            ]);
            $schedules = app(LoanScheduleRepository::class)->getLoanScheduleByApplicationIdPaymentPlanDatePaymentStatus($appData->id, $data['payment_plan_date'], PaymentStatusEnum::waitingValues());
            $remainingAmount = $data['total_amount'];

            foreach ($schedules as $schedule) {
                $scheduleId = $schedule->id;
                $due = $schedule->total_amount;
                $paid = $schedule->amount_paid;
                $remain = $due - $paid;
                $applied = 0;

                if ($remainingAmount <= 0) break;

                if ($remainingAmount >= $remain) {
                    $applied = $remain;
                } else {
                    $applied = $remainingAmount;
                }

                $createdLoanPaymentAllocation = app(LoanPaymentAllocationRepository::class)->create([
                    'customer_id' => $appData->customer_id,
                    'application_id' => $appData->id,
                    'loan_schedule_id' => $scheduleId,
                    'loan_payment_id' => $createdLoanPayment->id,
                    'amount' => $applied,
                    'before_payment_status' => $schedule->payment_status->value,
                ]);

                app(LoanTransactionLogRepository::class)->create([
                    'customer_id' => $appData->customer_id,
                    'application_id' => $appData->id,
                    'type' => LoanTransactionTypeEnum::DEPOSIT,
                    'loan_payment_id' => $createdLoanPayment->id,
                    'loan_schedule_id' => $scheduleId,
                    'loan_payment_allocation_id' => $createdLoanPaymentAllocation->id,
                ]);

                $newPaid = $paid + $applied;
                $newStatus = ($newPaid >= $due) ? PaymentStatusEnum::PAID : PaymentStatusEnum::PAID_PARTIAL_PAYMENT;

                $updatingLoanSchedule = app(LoanScheduleRepository::class)->find($scheduleId);

                $updatingLoanSchedule->fill([
                    'amount_paid' => $newPaid,
                    'payment_status' => $newStatus,
                ])->save();

                $createdLoanPayment->fill([
                    'payment_status' => $newStatus,
                ])->save();

                if ($applied < $remain) {
                    $arrears = $remain - $applied;

                    $createdLoanArrear = app(LoanArrearRepository::class)->create([
                        'customer_id' => $appData->customer_id,
                        'application_id' => $appData->id,
                        'loan_schedule_id' => $scheduleId,
                        'amount' => $arrears,
                        'delay_damage_amount' => 0,
                        'reissue_fee' => 0,
                    ]);

                    app(LoanTransactionLogRepository::class)->create([
                        'customer_id' => $appData->customer_id,
                        'application_id' => $appData->id,
                        'type' => LoanTransactionTypeEnum::DEPOSIT,
                        'loan_payment_id' => $createdLoanPayment->id,
                        'loan_schedule_id' => $scheduleId,
                        'loan_arrear_id' => $createdLoanArrear->id,
                    ]);
                }

                $remainingAmount -= $applied;
            }

            if ($remainingAmount > 0) {
                $createdLoanOverpayment = app(LoanOverPaymentRepository::class)->create([
                    'customer_id' => $appData->customer_id,
                    'application_id' => $appData->id,
                    'loan_payment_id' => $createdLoanPayment->id,
                    'amount' => $remainingAmount,
                    'refunded_flag' => RefundFlagEnum::UNREFUNDED,
                ]);

                app(LoanTransactionLogRepository::class)->create([
                    'customer_id' => $appData->customer_id,
                    'application_id' => $appData->id,
                    'type' => LoanTransactionTypeEnum::DEPOSIT,
                    'loan_payment_id' => $createdLoanPayment->id,
                    'loan_overpayment_id' => $createdLoanOverpayment->id,
                ]);
            }
        }
    }
}
