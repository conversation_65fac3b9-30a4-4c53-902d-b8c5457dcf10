<div x-data="{ showGuarantor: @entangle('guarantorForm.information_input_flag') }">
    <h1 class="application-page-title">6.お客様登録画面 / 連帯保証人・参考人情報</h1>
    <h3 class="heading-3">連帯保証人・参考人情報の有無</h3>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">情報入力の有無<span class="required-icon">必須</span></th>
                <td>
                    <div class="d-flex">
                        <div class="form-radio me-5">
                            <label>
                                <input type="radio" name="input_guarantor" value="1"
                                    wire:model="guarantorForm.information_input_flag"
                                    class="form-radio-input form-hidden-control-information"
                                    data-hidden-target="guarantor-input">
                                <span class="form-radio-text">なし</span>
                            </label>
                        </div>
                        <div class="form-radio me-5">
                            <label>
                                <input type="radio" name="input_guarantor" value="2"
                                    class="form-radio-input  form-hidden-control-information"
                                    data-hidden-target="guarantor-input"
                                    wire:model="guarantorForm.information_input_flag">
                                <span class="form-radio-text">連帯保証人あり</span>
                            </label>
                        </div>
                        <div class="form-radio">
                            <label>
                                <input type="radio" name="input_guarantor" value="3"
                                    class="form-radio-input  form-hidden-control-information"
                                    data-hidden-target="guarantor-input"
                                    wire:model='guarantorForm.information_input_flag'>
                                <span class="form-radio-text">参考人情報あり</span>
                            </label>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <div class="hidden-target-guarantor-input" x-show="showGuarantor > 1" x-cloak>
        <h3 class="heading-3 pt-4">連帯保証人・参考人情報</h3>
        <table class="table table-edit">
            <tbody>
                <tr>
                    <th class="required">お名前<span class="required-icon">必須</span></th>
                    <td>
                        <div class="row">
                            <div class="col-6">
                                <input type="text" name="" value="" class="form-control form-required" placeholder="姓"
                                    wire:model="guarantorForm.gw_last_name" maxlength="128">
                                @error('guarantorForm.gw_last_name') <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="col-6">
                                <input type="text" name="" value="" class="form-control form-required" placeholder="名"
                                    wire:model="guarantorForm.gw_first_name" maxlength="128">
                                @error('guarantorForm.gw_first_name') <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th class="required">お名前（フリガナ）<span class="required-icon">必須</span></th>
                    <td>
                        <div class="row">
                            <div class="col-6">
                                <input type="text" name="" value="" class="form-control form-required" placeholder="セイ"
                                    wire:model="guarantorForm.gw_last_name_kana" maxlength="128">
                                @error('guarantorForm.gw_last_name_kana') <span class="message-error">{{ $message
                                    }}</span>
                                @enderror
                            </div>
                            <div class="col-6">
                                <input type="text" name="" value="" class="form-control form-required" placeholder="メイ"
                                    wire:model="guarantorForm.gw_first_name_kana" maxlength="128">
                                @error('guarantorForm.gw_first_name_kana') <span class="message-error">{{ $message
                                    }}</span>
                                @enderror
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th class="required">性別<span class="required-icon">必須</span></th>
                    <td>
                        <div class="row">
                            <div class="col-3">
                                <div class="form-radio">
                                    <label>
                                        <input type="radio" wire:model="guarantorForm.gw_sex" name="gender" value="1"
                                            class="form-radio-input">
                                        <span class="form-radio-text">女性</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-radio">
                                    <label>
                                        <input type="radio" wire:model="guarantorForm.gw_sex" name="gender" value="2"
                                            class="form-radio-input">
                                        <span class="form-radio-text">男性</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-radio">
                                    <label>
                                        <input type="radio" wire:model="guarantorForm.gw_sex" name="gender" value="3"
                                            class="form-radio-input">
                                        <span class="form-radio-text">その他</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </td>
                    @error('guarantorForm.gw_sex') <span class="message-error">{{ $message }}</span> @enderror
                </tr>
                <tr>
                    <th class="required">誕生日<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="" value="" class="form-control datepicker form-required"
                            wire:model="guarantorForm.gw_birthday">
                        @error('guarantorForm.gw_birthday') <span class="message-error">{{ $message }}</span> @enderror
                    </td>
                </tr>
                <tr>
                    <th class="required">電話番号<span class="required-icon">必須</span></th>
                    <td>
                        <div class="d-flex align-items-center">
                            <div>
                                <input type="text" name="" value="" class="form-control form-required w-px-150"
                                    wire:model="guarantorForm.gw_tel1" maxlength="32">
                            </div>
                            <div class="mx-2">-</div>
                            <div>
                                <input type="text" name="" value="" class="form-control form-required w-px-150"
                                    wire:model="guarantorForm.gw_tel2" maxlength="32">
                            </div>
                            <div class="mx-2">-</div>
                            <div>
                                <input type="text" name="" value="" class="form-control form-required w-px-150"
                                    wire:model="guarantorForm.gw_tel3" maxlength="32">
                            </div>
                        </div>
                        <div>
                            <div>
                                @error('guarantorForm.gw_tel1') <span class="message-error">{{
                                    $message}}</span>@enderror
                            </div>
                            <div>
                                @error('guarantorForm.gw_tel2') <span class="message-error">{{
                                    $message}}</span>@enderror
                            </div>
                            <div>
                                @error('guarantorForm.gw_tel3') <span class="message-error">{{$message}}</span>@enderror
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th class="align-top required">お申込者様とのご関係<span class="required-icon">必須</span></th>
                    <td class="align-top">
                        <div class="row flex-wra pt-2 pb-3">
                            <div class="col-3 mb-4">
                                <div class="form-radio">
                                    <label>
                                        <input type="radio" name="relationship" value="1"
                                            wire:model="guarantorForm.gw_relationship_flag"
                                            class="form-radio-input form-disabled-control-flag"
                                            data-disabled-cancel="false" data-disabled-target="relationship">
                                        <span class="form-radio-text">配偶者</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-3 mb-4">
                                <div class="form-radio">
                                    <label>
                                        <input type="radio" name="relationship" value="2"
                                            wire:model="guarantorForm.gw_relationship_flag"
                                            class="form-radio-input form-disabled-control-flag"
                                            data-disabled-cancel="false" data-disabled-target="relationship">
                                        <span class="form-radio-text">親子</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-3 mb-4">
                                <div class="form-radio">
                                    <label>
                                        <input type="radio" name="relationship" value="3"
                                            wire:model="guarantorForm.gw_relationship_flag"
                                            class="form-radio-input form-disabled-control-flag"
                                            data-disabled-cancel="false" data-disabled-target="relationship">
                                        <span class="form-radio-text">兄弟</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-3 mb-4">
                                <div class="form-radio">
                                    <label>
                                        <input type="radio" name="relationship" value="4"
                                            wire:model="guarantorForm.gw_relationship_flag"
                                            class="form-radio-input form-disabled-control-flag"
                                            data-disabled-cancel="false" data-disabled-target="relationship">
                                        <span class="form-radio-text">親戚</span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-radio mb-2">
                                    <label>
                                        <input type="radio" name="relationship" value="5"
                                            wire:model="guarantorForm.gw_relationship_flag"
                                            class="form-radio-input form-disabled-control-flag"
                                            data-disabled-cancel="true" data-disabled-target="relationship">
                                        <span class="form-radio-text">その他</span>
                                    </label>
                                </div>
                                <div class="ps-5">
                                    <input type="text" name="" value=""
                                        class="form-control  disabled-target-relationship"
                                        wire:model="guarantorForm.gw_relationship_other"
                                        @disabled($guarantorForm->gw_relationship_flag != 5) maxlength="512">
                                </div>
                            </div>
                        </div>
                    </td>
                    @error('guarantorForm.gw_relationship_flag') <span class="message-error">{{ $message }}</span>
                    @enderror
                </tr>
                <tr>
                    <th>郵便番号<span class="required-icon">必須</span></th>
                    <td>
                        <div class="d-flex align-items-center">
                            <div>
                                <input type="text" name="zip1" value="" maxlength="4"
                                    class="form-control w-px-150 form-required" wire:model="guarantorForm.gw_zip1">
                            </div>
                            <div class="mx-2">-</div>
                            <div>
                                <input type="text" name="zip2" value="" maxlength="4"
                                    class="form-control w-px-150 form-required" wire:model="guarantorForm.gw_zip2">
                            </div>
                        </div>
                        <div>
                            <div>
                                @error('guarantorForm.gw_zip1') <span class="message-error">{{
                                    $message}}</span>@enderror
                            </div>
                            <div>
                                @error('guarantorForm.gw_zip2') <span class="message-error">{{
                                    $message}}</span>@enderror
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>都道府県<span class="required-icon">必須</span></th>
                    <td>
                        <div wire:ignore>
                            <select name="pref_id" class="form-select2 form-required"
                                wire:model="guarantorForm.gw_pref_id" style="width:280px;"
                                data-placeholder="{{ trans2('select_default') }}">
                                <option value=""></option>
                                @foreach ($listPrefs as $key => $pref)
                                <option value="{{ data_get($pref,'id') }}">{{ data_get($pref,'name') }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            @error('guarantorForm.gw_pref_id') <span class="message-error">{{
                                $message}}</span>@enderror
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>市区町村<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="city" value="" class="form-control form-required"
                            wire:model="guarantorForm.gw_city" maxlength="1000">
                        @error('guarantorForm.gw_city') <span class="message-error">{{ $message }}</span> @enderror
                    </td>
                </tr>
                <tr>
                    <th>町名・番地<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="address" value="" class="form-control form-required"
                            wire:model="guarantorForm.gw_address" maxlength="1000">
                        @error('guarantorForm.gw_address') <span class="message-error">{{ $message }}</span> @enderror
                    </td>
                </tr>
                <tr>
                    <th>建物名・部屋番号</th>
                    <td>
                        <input type="text" name="building" value="" class="form-control"
                            wire:model="guarantorForm.gw_building" maxlength="1000">
                        @error('guarantorForm.gw_building') <span class="message-error">{{ $message }}</span> @enderror
                    </td>
                </tr>
            </tbody>
        </table>
        <h3 class="heading-3 pt-4">連帯保証人・参考人の勤務先情報</h3>
        <table class="table table-edit">
            <tbody>
                <tr>
                    <th>勤務先名称</th>
                    <td>
                        <input type="text" name="" value="" class="form-control" placeholder=""
                            wire:model="guarantorForm.gw_company_name" maxlength="128">
                        @error('guarantorForm.gw_company_name') <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
                <tr>
                    <th>勤務先名称（フリガナ）</th>
                    <td>
                        <input type="text" name="" value="" class="form-control" placeholder=""
                            wire:model="guarantorForm.gw_company_name_kana" maxlength="128">
                        @error('guarantorForm.gw_company_name_kana') <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
                <tr>
                    <th>郵便番号</th>
                    <td>
                        <div class="d-flex align-items-center">
                            <div>
                                <input type="text" name="gw_company_zip1" value="" maxlength="4"
                                    class="form-control w-px-150" wire:model="guarantorForm.gw_company_zip1">
                            </div>
                            <div class="mx-2">-</div>
                            <div>
                                <input type="text" name="gw_company_zip2" value="" maxlength="4"
                                    class="form-control w-px-150" wire:model="guarantorForm.gw_company_zip2">
                            </div>
                        </div>
                        <div>
                            <div>
                                @error('guarantorForm.gw_company_zip1') <span
                                    class="message-error">{{$message}}</span>@enderror
                            </div>
                            <div>
                                @error('guarantorForm.gw_company_zip2') <span
                                    class="message-error">{{$message}}</span>@enderror
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>都道府県</th>
                    <td>
                        <div wire:ignore>
                            <select name="company_pref_id" class="form-select2"
                                wire:model="guarantorForm.gw_company_pref_id" style="width:280px;"
                                data-placeholder="{{ trans2('select_default') }}">
                                <option value=""></option>
                                @foreach ($listPrefs as $key => $pref)
                                <option value="{{ data_get($pref,'id') }}">{{ data_get($pref,'name') }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            @error('guarantorForm.gw_company_pref_id') <span class="message-error">{{
                                $message}}</span>@enderror
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>市区町村</th>
                    <td>
                        <input type="text" name="gw_company_city" value="" class="form-control"
                            wire:model="guarantorForm.gw_company_city" maxlength="1000">
                        @error('guarantorForm.gw_company_city') <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
                <tr>
                    <th>町名・番地</th>
                    <td>
                        <input type="text" name="gw_company_address" value="" class="form-control"
                            wire:model="guarantorForm.gw_company_address" maxlength="1000">
                        <div>
                            @error('guarantorForm.gw_company_address') <span class="message-error">{{ $message }}</span>
                            @enderror
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>建物名・部屋番号</th>
                    <td>
                        <input type="text" name="gw_company_buiding" value="buiding" class="form-control"
                            wire:model="guarantorForm.gw_company_building" maxlength="1000">
                        <div>
                            @error('guarantorForm.gw_company_building') <span class="message-error">{{ $message
                                }}</span>
                            @enderror
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>電話番号</th>
                    <td>
                        <div class="d-flex align-items-center">
                            <div>
                                <input type="text" name="" value="" class="form-control w-px-150"
                                    wire:model="guarantorForm.gw_company_tel1" maxlength="32">
                            </div>
                            <div class="mx-2">-</div>
                            <div>
                                <input type="text" name="" value="" class="form-control w-px-150"
                                    wire:model="guarantorForm.gw_company_tel2" maxlength="32">
                            </div>
                            <div class="mx-2">-</div>
                            <div>
                                <input type="text" name="" value="" class="form-control w-px-150"
                                    wire:model="guarantorForm.gw_company_tel3" maxlength="32">
                            </div>
                        </div>
                        <div>
                            <div>
                                @error('guarantorForm.gw_company_tel1') <span class="message-error">{{
                                    $message}}</span>@enderror
                            </div>
                            <div>
                                @error('guarantorForm.gw_company_tel2') <span class="message-error">{{
                                    $message}}</span>@enderror
                            </div>
                            <div>
                                @error('guarantorForm.gw_company_tel3') <span
                                    class="message-error">{{$message}}</span>@enderror
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="pt-5 text-center">
        <button wire:click.prevent="validateSave" class="btn btn-dark btn-large">{{ trans2('button.next') }}</button>
    </div>
</div>
@include('livewire.admin.application.customer.script')
