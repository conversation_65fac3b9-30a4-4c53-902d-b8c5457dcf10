{"__meta": {"id": "01JZS0SKX6WKX9CAGCH9XKKTGM", "datetime": "2025-07-10 10:56:55", "utime": **********.334899, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 20, "messages": [{"message": "[10:56:55] LOG.debug: (Time: 01.55) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.243595, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 03.08) SQL: select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.26569, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.36) SQL: select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.269826, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.33) SQL: select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.272761, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.275847, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.49) SQL: select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.278989, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.32) SQL: select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.281918, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.58) SQL: select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.285216, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.35) SQL: select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.288547, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.291458, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.32) SQL: select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.294304, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.51) SQL: select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.297376, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.40) SQL: select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.300761, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.34) SQL: select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.303695, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.306558, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.51) SQL: select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.309675, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 03.12) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.316459, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.41) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.319931, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.36) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.322809, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:55] LOG.debug: (Time: 00.41) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.326034, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.021676, "end": **********.334942, "duration": 0.3132660388946533, "duration_str": "313ms", "measures": [{"label": "Booting", "start": **********.021676, "relative_start": 0, "end": **********.211377, "relative_end": **********.211377, "duration": 0.*****************, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.211386, "relative_start": 0.*****************, "end": **********.334944, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "124ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.219092, "relative_start": 0.*****************, "end": **********.220961, "relative_end": **********.220961, "duration": 0.*****************, "duration_str": "1.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.332205, "relative_start": 0.****************, "end": **********.332982, "relative_end": **********.332982, "duration": 0.0007770061492919922, "duration_str": "777μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x livewire.admin.dashboard.review-table", "param_count": null, "params": [], "start": **********.329998, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/dashboard/review-table.blade.phplivewire.admin.dashboard.review-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fdashboard%2Freview-table.blade.php&line=1", "ajax": false, "filename": "review-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.dashboard.review-table"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 20, "nb_statements": 20, "nb_visible_statements": 20, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.014629999999999999, "accumulated_duration_str": "14.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.242155, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 10.595}, {"sql": "select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.2627132, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 10.595, "width_percent": 21.053}, {"sql": "select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.269571, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 31.647, "width_percent": 2.461}, {"sql": "select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [3, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.272532, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 34.108, "width_percent": 2.256}, {"sql": "select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [4, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.275555, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 36.364, "width_percent": 2.802}, {"sql": "select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.2786071, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 39.166, "width_percent": 3.349}, {"sql": "select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.2817001, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 42.515, "width_percent": 2.187}, {"sql": "select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.284759, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 44.703, "width_percent": 3.964}, {"sql": "select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [8, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.288299, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 48.667, "width_percent": 2.392}, {"sql": "select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [9, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.291167, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 51.059, "width_percent": 2.666}, {"sql": "select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [10, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.294082, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 53.725, "width_percent": 2.187}, {"sql": "select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.296967, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 55.913, "width_percent": 3.486}, {"sql": "select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [12, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.3005, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 59.398, "width_percent": 2.734}, {"sql": "select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.303452, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 62.133, "width_percent": 2.324}, {"sql": "select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [14, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.3062649, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 64.457, "width_percent": 2.666}, {"sql": "select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.30927, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:415", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=415", "ajax": false, "filename": "ApplicationRepository.php", "line": "415"}, "connection": "local-ladybird", "explain": null, "start_percent": 67.122, "width_percent": 3.486}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, 3, 4, 5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.313479, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 70.608, "width_percent": 21.326}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, 8, 9, 10, 11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.3196208, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 91.934, "width_percent": 2.802}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, 12, 13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.322551, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 94.737, "width_percent": 2.461}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.325724, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 97.198, "width_percent": 2.802}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.dashboard.review-table #DO47ImhHDEeBNea3J8Bp": "array:4 [\n  \"data\" => array:5 [\n    \"shopId\" => \"\"\n    \"shopBrandId\" => \"\"\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.dashboard.review-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Dashboard\\ReviewTable\"\n  \"id\" => \"DO47ImhHDEeBNea3J8Bp\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "316ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-372655970 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-372655970\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1463630523 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"421 characters\">{&quot;data&quot;:{&quot;shopId&quot;:&quot;&quot;,&quot;shopBrandId&quot;:&quot;&quot;,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;DO47ImhHDEeBNea3J8Bp&quot;,&quot;name&quot;:&quot;admin.dashboard.review-table&quot;,&quot;path&quot;:&quot;management&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;props&quot;:[&quot;shopId&quot;,&quot;shopBrandId&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;4b3692d9bdb6a087cf669c963e9f607e2311e1bdcfabe7ecc1abe0fb0cc689b2&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"284 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJzaG9wQnJhbmRJZCI6IiIsInNob3BJZCI6IiJ9LHsicyI6ImFyciJ9XX0sIm1lbW8iOnsiaWQiOiJZNmJKdjFSbnNXb1dQckdieWFBbCIsIm5hbWUiOiJfX21vdW50UGFyYW1zQ29udGFpbmVyIn0sImNoZWNrc3VtIjoiN2MxMzZkNTdkNzQ5NGQ0NTEyY2JlNGE2NDNlYjhlYzY4OTkxYzY4NTFhMGJiNDM4YmI2ZjBhMTAxYzRlNzFiZCJ9</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463630523\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">924</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjliemhKQ2NSQmVpOWwrb2VBQnhKaVE9PSIsInZhbHVlIjoiSXhDNmJqdmtHU3Uvbm8ybWVHYzdoSFU1Ym1qZWF0ai9lRnlRb3JiMDFISVQ4N0dYTFJkcUNZL1M5OVJiVmRhaTJwclM3SXNjOStSRnpFZzcvb3BoUkY5RHZHQTZkdkJ1czZUaGNBZmQ5S0hpZEFrUGp5MTlJSU1aWnNUNWRtVjVvQTRLK1M4NG5JaHBpUzRWdE1vc2tBPT0iLCJtYWMiOiI0YzdhMTM3MWFlNWUxODUwNTJiOWM3NDkzMWU5NDY2NGI5MjljYmU0ZTgxN2QxYjVkYmY1OTg1ZTY5YmM5ZTJkIiwidGFnIjoiIn0%3D; ladybird_session=wlXlRruX2PfaGJavVPNjRis2N3hxByorNqLQ96CL; XSRF-TOKEN=eyJpdiI6Impobk5rWE5NNjlEblc1T0tyVXVMRGc9PSIsInZhbHVlIjoiZkcyZk5ra2NBZGM5OEVsSTdOLzNqelVnR3MyN0xYUzRHb3FZTGd4UDJZUTF2L3NsdnlnQkc0N29lS3hGU2lEeFZqT2pHMGxCbFpYblFHeWFZQ2kxOTMyNWlIeXRhTlFrelJ6TGxBNlA4UXI0RVUvR3c1UWJwUDJ0eHRmbVpGckoiLCJtYWMiOiJkNTRkMTczM2ZlNGQyNDkyMGQxNTg5MTg0NzQ1MjY1YjY5NmQyYWEzZTUwYTZmZTExMWJkMDIzODE0MzYxNTcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1365261856 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365261856\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1898305935 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 01:56:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898305935\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1537455001 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537455001\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}