<?php

namespace App\Livewire\Admin\Application;

use App\Enums\ApplicationTabEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Services\CustomerService;
use App\Services\ToastService;
use Livewire\Attributes\Layout;
use Livewire\WithPagination;

#[Layout('components.layouts.application-layout')]
class Customer extends BaseAdminPageComponent
{
    use WithPagination;

    public $last_name_kana;

    public $first_name_kana;

    public $birthday;

    public $tel;

    public $applicationId;

    public $application = null;

    public function mount($application_id)
    {        
        $this->page = ApplicationTabEnum::CUSTOMER;
        $this->pageTitle .= '｜' . trans2('project_name');
        $this->dispatch('init-select2');

        $this->applicationId = $application_id;

        $this->application = app()->make(ApplicationRepository::class)->find($application_id);

        if (empty($this->application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }
    }

    public function search()
    {
        $dataSearch = [
            'last_name_kana' => $this->last_name_kana,
            'first_name_kana' => $this->first_name_kana,
            'birthday' => $this->birthday,
            'tel' => $this->tel,
        ];

        return app(CustomerService::class)->searchCustomer($dataSearch, getConfig('page_number'));
    }

    public function render()
    {
        $listCustomers = $this->search();

        return $this->viewLivewireAdmin('application.customer', [
            'listCustomers' => $listCustomers,
        ]);
    }
}
