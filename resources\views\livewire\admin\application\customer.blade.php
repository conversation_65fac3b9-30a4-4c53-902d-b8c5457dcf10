<div>
    <h1 class="application-page-title">5.{{ trans2('screens.application.customer.title') }}</h1>
    <h2 class="heading-2 fs-16 mb-4">初めてのお客様</h2>
    <div class="border-bottom pb-5 mb-5">
        <a href="#" class="btn btn-dark btn-medium" data-bs-toggle="modal"
            data-bs-target="#confirmEntryCustomerModal">新規登録</a>
    </div>
    <h2 class="heading-2 fs-16 mb-4">既存のお客様</h2>
    <h3 class="heading-3">お客様検索</h3>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th style="width:35%">お名前</th>
                <td>
                    <div class="row">
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control" placeholder="姓（せい）"
                                wire:model="last_name_kana" maxlength="128">
                        </div>
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control" placeholder="名（めい）"
                                wire:model="first_name_kana" maxlength="128">
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th style="width:35%">誕生日</th>
                <td>
                    <input type="text" id="birthday" class="form-control datepicker" data-date-format="yy年m月d日"
                        wire:model="birthday">
                </td>
            </tr>
            <tr>
                <th style="width:35%">電話番号</th>
                <td>
                    <input type="text" name="" value="" class="form-control" wire:model="tel" maxlength="128">
                </td>
            </tr>
        </tbody>
    </table>
    <div class="text-end">
        <button type="button" class="btn btn-dark btn-medium" wire:click="search">検索</button>
    </div>
    <h3 class="heading-3">お客様一覧</h3>
    <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
        <table class="table table-borderless table-thead-bordered table-align-middle customer-list-table">
            <thead>
                <tr>
                    <th scope="col">お名前</th>
                    <th scope="col">カナ</th>
                    <th scope="col">生年月日</th>
                    <th scope="col">電話番号</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($listCustomers as $item)
                <tr @click="$dispatch('open-customer-modal', { customerId: '{{ $item->id }}' })" style="cursor: pointer;">
                    <td>{{ $item->full_name }}</td>
                    <td>{{ $item->full_name_kana }}</td>
                    <td>{{ $item->birthday ? \Carbon\Carbon::parse($item?->birthday)->format('Y/m/d') : '' }}</td>
                    <td>{{ $item->full_phone }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @if ($listCustomers->isEmpty())
        @include('components.no-result-found')
        @endif
    </div>
    <div class="text-center mt-4">
        @include('components.pagination-range-text', ['items' => $listCustomers])
        {{ $listCustomers->links('components.pagination') }}
    </div>
    @include('livewire.admin.application.modal-confirm-entry-customer')
</div>
<script>
    document.addEventListener('livewire:initialized', () => {
        setTimeout(() => {
            initializeDatepickers();
        }, 50);
    });

    function initializeDatepickers() {
        document.querySelectorAll('.datepicker').forEach(function (el) {
            const currentYear = new Date().getFullYear();
            const minYear = currentYear - 200;

            $(el).datepicker({
                dateFormat: 'yy年m月d日',
                yearRange: `${minYear}:${currentYear}`,
                minDate: new Date(minYear, 0, 1),
                maxDate: new Date(),
                changeYear: true,
            }).on('change', function () {
                const rawValue = $(this).val();
                const model = el.getAttribute('wire:model');

                const formatted = rawValue
                    .replace('年', '-')
                    .replace('月', '-')
                    .replace('日', '');

                if (model) {
                    const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                    Livewire.find(componentId).set(model, formatted);
                }
            });
        });
    }
</script>
