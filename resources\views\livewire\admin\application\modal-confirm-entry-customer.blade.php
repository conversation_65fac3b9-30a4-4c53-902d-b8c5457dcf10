<div 
    x-data="{ customerId: null }" 
    x-on:open-customer-modal.window="customerId = $event.detail.customerId; new bootstrap.Modal($refs.modal).show();"
>
    <div class="modal fade" x-ref="modal" id="confirmEntryCustomerModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="text-center p-3">
                        <h5 class="fs-16 mb-3">ご確認</h5>
                        <p class="fw-medium">
                            ご担当者様の入力はここまでとなります。<br>
                            次ページからはお客様自身でご登録いただく内容となります。
                        </p>

                        <a 
                            :href="customerId 
                                ? '{{ getRoute('customer.application.setup', ['application_id' => $application->id]) }}' + '?customer_id=' + customerId 
                                : '{{ getRoute('customer.application.setup', ['application_id' => $application->id]) }}'"
                            class="btn btn-dark btn-medium"
                        >
                            ⑥お客様登録画面へ進む
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
