<?php

namespace App\Models\Presenters;

use Carbon\Carbon;


trait PApplication
{
    public function convertToCsvRow(): array
    {
        $courseNames = collect(data_get($this, 'courses', []))
            ->map(function ($course) {
                return joinSlash(
                    data_get($this, 'shopBrand.name'),
                    data_get($course, 'itemType.name'),
                    data_get($course, 'name_application'),
                );
            })->implode(PHP_EOL);

        return [
            $this->contract_id ?? "",
            $this->customer->full_name ?? "",
            data_get($this, 'payment_company_flag.text') ?? "",
            $this->regist_number,
            data_get($this, 'contract_status.text') ?? "",
            $this->contract_date,
            $courseNames,
        ];
    }

    public function getBonusAdditionAmountAttribute()
    {
        $amount = ($this->attributes['bonus_month_payment_amount'] ?? 0) * ($this->attributes['bonus_payment_count'] ?? 0);
        return floor($amount);
    }

    public function getPrincipalIncludingTaxAttribute()
    {
        return ($this->attributes['total_amount'] ?? 0) - ($this->attributes['fee_amount'] ?? 0);
    }

    public function getFirstPaymentOriginIncludeTaxAttribute()
    {
        if($this->attributes['payment_count'] == 0){
            return ($this->attributes['first_month_payment_amount'] ?? 0);
        }
        return ($this->attributes['first_month_payment_amount'] ?? 0) - (($this->attributes['fee_amount'] ?? 0) / $this->attributes['payment_count']);
    }

    public function getSecondPaymentOriginIncludeTaxAttribute()
    {
        if($this->attributes['payment_count'] == 0){
            return ($this->attributes['second_month_payment_amount'] ?? 0);
        }
        return ($this->attributes['second_month_payment_amount'] ?? 0) - (($this->attributes['fee_amount'] ?? 0) / $this->attributes['payment_count']);
    }
    public function getSplitFeeAttribute()
    {
        if($this->attributes['payment_count'] == 0){
            return 0;
        }
        return ($this->attributes['fee_amount'] ?? 0) / $this->attributes['payment_count'];
    }

    public function getApplicationDateAttribute(){
        return $this->attributes['application_date']
            ? Carbon::parse($this->attributes['application_date'])->format('Y/m/d')
            : null;
    }

    public function getApplicationDatePDF(){
        return $this->attributes['application_date']
            ? Carbon::parse($this->attributes['application_date'])->format('Y年m月d日')
            : null;
    }

    public function getContractDateAttribute(){
        return $this->attributes['contract_date']
            ? Carbon::parse($this->attributes['contract_date'])->format('Y/m/d')
            : null;
    }

    public function getContractCancelDateAttribute(){
        return $this->attributes['contract_cancel_date']
            ? Carbon::parse($this->attributes['contract_cancel_date'])->format('Y/m/d')
            : null;
    }

    public function getPaymentCompanyRegistDateAttribute(){
        return $this->attributes['payment_company_regist_date']
            ? Carbon::parse($this->attributes['payment_company_regist_date'])->format('Y/m/d')
            : null;
    }

    public function getServiceHandoverDatePDF()
    {
        return $this->attributes['service_handover_date']
            ? Carbon::parse($this->attributes['service_handover_date'])->format('Y年m月d日')
            : null;
    }

    public function getServiceStartDatePDF()
    {
        return $this->attributes['service_start_date']
            ? Carbon::parse($this->attributes['service_start_date'])->format('Y年m月d日')
            : null;
    }

    public function getServiceEndDatePDF()
    {
        return $this->attributes['service_end_date']
            ? Carbon::parse($this->attributes['service_end_date'])->format('Y年m月d日')
            : null;
    }
}
