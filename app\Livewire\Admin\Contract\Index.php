<?php

namespace App\Livewire\Admin\Contract;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;

class Index extends BaseAdminPageComponent
{
    public function __construct()
    {
        $this->page = SidebarMenuEnum::CONTRACT;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
    }

    public function render()
    {
        return $this->viewLivewireAdmin('contract.index');
    }
}
