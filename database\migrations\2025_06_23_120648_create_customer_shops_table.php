<?php

use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('customer_shops', function (BlueprintCustom $table) {
            $table->increments('id')->comment('ID');
            $table->integer('customer_id')->comment('顧客ID')->index();
            $table->integer('shop_id')->comment('店舗ID')->index();
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SchemaCustom::dropIfExists('customer_shops');
    }
};
