{"__meta": {"id": "01JZS0VYRESCZBFYXZ31MBKHE4", "datetime": "2025-07-10 10:58:11", "utime": **********.982686, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 6, "messages": [{"message": "[10:58:11] LOG.debug: (Time: 02.15) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.920737, "xdebug_link": null, "collector": "log"}, {"message": "[10:58:11] LOG.debug: (Time: 00.39) SQL: select * from `shops` where `shops`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.931039, "xdebug_link": null, "collector": "log"}, {"message": "[10:58:11] LOG.debug: (Time: 00.89) SQL: select * from `brands` where `brands`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.935975, "xdebug_link": null, "collector": "log"}, {"message": "[10:58:11] LOG.debug: (Time: 00.38) SQL: select * from `shops` where `shops`.`id` in (1, 2) {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.944214, "xdebug_link": null, "collector": "log"}, {"message": "[10:58:11] LOG.debug: (Time: 00.42) SQL: select * from `brands` where `brands`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.947738, "xdebug_link": null, "collector": "log"}, {"message": "[10:58:11] LOG.debug: (Time: 00.73) SQL: select DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') AS payment_month,\n\n            SUM(loan_schedules.total_amount - loan_schedules.amount_paid) AS loan_balance,\n\n            SUM(loan_schedules.amount_paid) AS paid_amount,\n\n            SUM(IFNULL(loan_arrears.delay_damage_amount, 0)) AS delay_damage_total,\n            SUM(IFNULL(loan_arrears.reissue_fee, 0)) AS reissue_fee_total,\n\n            SUM(DISTINCT IFNULL(applications.sub_total_amount, 0)) AS principal_total,\n            SUM(DISTINCT IFNULL(applications.fee_amount, 0)) AS fee_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.total_amount, 0), 0\n            )) AS current_month_contract_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.sub_total_amount, 0), 0\n            )) AS current_month_principal_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.contract_cancel_amount, 0), 0\n            )) AS cancel_amount_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.forced_contract_cancel_amount, 0), 0\n            )) AS forced_cancel_amount_total,\n\n            SUM(\n                (loan_schedules.total_amount - loan_schedules.amount_paid)\n                * (IFNULL(applications.sub_total_amount, 0) / NULLIF(applications.total_amount, 0))\n            ) AS remaining_principal_estimated from `loan_schedules` left join `loan_arrears` on `loan_arrears`.`loan_schedule_id` = `loan_schedules`.`id` and `loan_arrears`.`del_flag` = 0 left join `applications` on `loan_schedules`.`application_id` = `applications`.`id` and `applications`.`del_flag` = 0 where DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') >= '202507' and DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') <= '202507' and `loan_schedules`.`del_flag` = '0' group by DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') order by `payment_month` asc {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.975824, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.704214, "end": **********.982714, "duration": 0.2784998416900635, "duration_str": "278ms", "measures": [{"label": "Booting", "start": **********.704214, "relative_start": 0, "end": **********.882215, "relative_end": **********.882215, "duration": 0.*****************, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.882227, "relative_start": 0.*****************, "end": **********.982716, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.891036, "relative_start": 0.*****************, "end": **********.893103, "relative_end": **********.893103, "duration": 0.0020668506622314453, "duration_str": "2.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.980143, "relative_start": 0.*****************, "end": **********.980957, "relative_end": **********.980957, "duration": 0.0008139610290527344, "duration_str": "814μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "1x volt-livewire::admin.balance.search", "param_count": null, "params": [], "start": **********.96194, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/admin/balance/search.blade.phpvolt-livewire::admin.balance.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fbalance%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::admin.balance.search"}, {"name": "1x livewire.admin.balance.table-data-list", "param_count": null, "params": [], "start": **********.978252, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/balance/table-data-list.blade.phplivewire.admin.balance.table-data-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fbalance%2Ftable-data-list.blade.php&line=1", "ajax": false, "filename": "table-data-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.balance.table-data-list"}, {"name": "1x components.no-result-found", "param_count": null, "params": [], "start": **********.978911, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/no-result-found.blade.phpcomponents.no-result-found", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Fno-result-found.blade.php&line=1", "ajax": false, "filename": "no-result-found.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.no-result-found"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00496, "accumulated_duration_str": "4.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.918705, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 43.347}, {"sql": "select * from `shops` where `shops`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 61}, {"index": 16, "namespace": null, "name": "resources/views/livewire/admin/balance/search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\balance\\search.blade.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentResolver.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentResolver.php", "line": 39}], "start": **********.930755, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ShopRepository.php:61", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopRepository.php&line=61", "ajax": false, "filename": "ShopRepository.php", "line": "61"}, "connection": "local-ladybird", "explain": null, "start_percent": 43.347, "width_percent": 7.863}, {"sql": "select * from `brands` where `brands`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 54}, {"index": 16, "namespace": null, "name": "resources/views/livewire/admin/balance/search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\balance\\search.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentResolver.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentResolver.php", "line": 39}], "start": **********.935196, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BrandRepository.php:54", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FBrandRepository.php&line=54", "ajax": false, "filename": "BrandRepository.php", "line": "54"}, "connection": "local-ladybird", "explain": null, "start_percent": 51.21, "width_percent": 17.944}, {"sql": "select * from `shops` where `shops`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.943947, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "EloquentCollectionSynth.php:70", "source": {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FEloquentCollectionSynth.php&line=70", "ajax": false, "filename": "EloquentCollectionSynth.php", "line": "70"}, "connection": "local-ladybird", "explain": null, "start_percent": 69.153, "width_percent": 7.661}, {"sql": "select * from `brands` where `brands`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.9474268, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EloquentCollectionSynth.php:70", "source": {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FEloquentCollectionSynth.php&line=70", "ajax": false, "filename": "EloquentCollectionSynth.php", "line": "70"}, "connection": "local-ladybird", "explain": null, "start_percent": 76.815, "width_percent": 8.468}, {"sql": "select DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') AS payment_month,\nSUM(loan_schedules.total_amount - loan_schedules.amount_paid) AS loan_balance,\nSUM(loan_schedules.amount_paid) AS paid_amount,\nSUM(IFNULL(loan_arrears.delay_damage_amount, 0)) AS delay_damage_total,\nSUM(IFNULL(loan_arrears.reissue_fee, 0)) AS reissue_fee_total,\nSUM(DISTINCT IFNULL(applications.sub_total_amount, 0)) AS principal_total,\nSUM(DISTINCT IFNULL(applications.fee_amount, 0)) AS fee_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.total_amount, 0), 0\n)) AS current_month_contract_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.sub_total_amount, 0), 0\n)) AS current_month_principal_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.contract_cancel_amount, 0), 0\n)) AS cancel_amount_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.forced_contract_cancel_amount, 0), 0\n)) AS forced_cancel_amount_total,\nSUM(\n(loan_schedules.total_amount - loan_schedules.amount_paid)\n* (IFNULL(applications.sub_total_amount, 0) / NULLIF(applications.total_amount, 0))\n) AS remaining_principal_estimated from `loan_schedules` left join `loan_arrears` on `loan_arrears`.`loan_schedule_id` = `loan_schedules`.`id` and `loan_arrears`.`del_flag` = 0 left join `applications` on `loan_schedules`.`application_id` = `applications`.`id` and `applications`.`del_flag` = 0 where DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') >= '\\'202507\\'' and DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') <= '\\'202507\\'' and `loan_schedules`.`del_flag` = '\\'0\\'' group by DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') order by `payment_month` asc", "type": "query", "params": [], "bindings": [0, 0, "'202507'", "'202507'", "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 689}, {"index": 16, "namespace": null, "name": "app/Services/BalanceService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\BalanceService.php", "line": 12}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Balance/TableDataList.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Balance\\TableDataList.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.975198, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "LoanScheduleRepository.php:689", "source": {"index": 15, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 689}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FLoanScheduleRepository.php&line=689", "ajax": false, "filename": "LoanScheduleRepository.php", "line": "689"}, "connection": "local-ladybird", "explain": null, "start_percent": 85.282, "width_percent": 14.718}]}, "models": {"data": {"App\\Models\\Brand": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Shop": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FShop.php&line=1", "ajax": false, "filename": "Shop.php", "line": "?"}}, "App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 25, "is_counter": true}, "livewire": {"data": {"admin.balance.search #NAoVvSdHDlSEg46pWuI8": "array:4 [\n  \"data\" => array:7 [\n    \"authType\" => \"\"\n    \"brandId\" => \"\"\n    \"storeId\" => \"\"\n    \"from\" => \"2025年7月\"\n    \"to\" => \"2025年7月\"\n    \"stores\" => Illuminate\\Database\\Eloquent\\Collection {#899\n      #items: array:2 [\n        0 => App\\Models\\Shop {#898\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Shop {#897\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"brands\" => Illuminate\\Database\\Eloquent\\Collection {#917\n      #items: array:10 [\n        0 => App\\Models\\Brand {#916\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Brand {#915\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        2 => App\\Models\\Brand {#914\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        3 => App\\Models\\Brand {#913\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        4 => App\\Models\\Brand {#912\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        5 => App\\Models\\Brand {#911\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        6 => App\\Models\\Brand {#910\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        7 => App\\Models\\Brand {#909\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        8 => App\\Models\\Brand {#908\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        9 => App\\Models\\Brand {#907\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"admin.balance.search\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0ceb4ce65af6e605135ffdc5d4e76fb6.php:8$256d\"\n  \"id\" => \"NAoVvSdHDlSEg46pWuI8\"\n]", "admin.balance.table-data-list #8GozjeAULhMc4wbHF5Hs": "array:4 [\n  \"data\" => array:8 [\n    \"authType\" => \"\"\n    \"from\" => \"2025年7月\"\n    \"to\" => \"2025年7月\"\n    \"brandId\" => \"\"\n    \"storeId\" => \"\"\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.balance.table-data-list\"\n  \"component\" => \"App\\Livewire\\Admin\\Balance\\TableDataList\"\n  \"id\" => \"8GozjeAULhMc4wbHF5Hs\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/management/balances\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "281ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1627346867 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1627346867\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-832691979 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"661 characters\">{&quot;data&quot;:{&quot;authType&quot;:&quot;&quot;,&quot;brandId&quot;:&quot;&quot;,&quot;storeId&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;to&quot;:&quot;&quot;,&quot;stores&quot;:[null,{&quot;keys&quot;:[1,2],&quot;class&quot;:&quot;Illuminate\\\\Database\\\\Eloquent\\\\Collection&quot;,&quot;modelClass&quot;:&quot;App\\\\Models\\\\Shop&quot;,&quot;s&quot;:&quot;elcln&quot;}],&quot;brands&quot;:[null,{&quot;keys&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;class&quot;:&quot;Illuminate\\\\Database\\\\Eloquent\\\\Collection&quot;,&quot;modelClass&quot;:&quot;App\\\\Models\\\\Brand&quot;,&quot;s&quot;:&quot;elcln&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;NAoVvSdHDlSEg46pWuI8&quot;,&quot;name&quot;:&quot;admin.balance.search&quot;,&quot;path&quot;:&quot;management\\/balances&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-1147524950-0&quot;:[&quot;div&quot;,&quot;8GozjeAULhMc4wbHF5Hs&quot;]},&quot;scripts&quot;:[&quot;1147524950-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;1570b5d2fde12bbffd33bc9a298601659738e25dec785dc64526d3b8165618dc&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2025&#24180;7&#26376;</span>\"\n        \"<span class=sf-dump-key>to</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2025&#24180;7&#26376;</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"481 characters\">{&quot;data&quot;:{&quot;authType&quot;:&quot;&quot;,&quot;from&quot;:&quot;&quot;,&quot;to&quot;:&quot;&quot;,&quot;brandId&quot;:&quot;&quot;,&quot;storeId&quot;:&quot;&quot;,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;8GozjeAULhMc4wbHF5Hs&quot;,&quot;name&quot;:&quot;admin.balance.table-data-list&quot;,&quot;path&quot;:&quot;management\\/balances&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;props&quot;:[&quot;authType&quot;,&quot;from&quot;,&quot;to&quot;,&quot;brandId&quot;,&quot;storeId&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;1dfcd131af185debe8fd924597d72f90c436f428907381435dd223effb3ba7d1&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832691979\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1641885943 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1529</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/management/balances</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjliemhKQ2NSQmVpOWwrb2VBQnhKaVE9PSIsInZhbHVlIjoiSXhDNmJqdmtHU3Uvbm8ybWVHYzdoSFU1Ym1qZWF0ai9lRnlRb3JiMDFISVQ4N0dYTFJkcUNZL1M5OVJiVmRhaTJwclM3SXNjOStSRnpFZzcvb3BoUkY5RHZHQTZkdkJ1czZUaGNBZmQ5S0hpZEFrUGp5MTlJSU1aWnNUNWRtVjVvQTRLK1M4NG5JaHBpUzRWdE1vc2tBPT0iLCJtYWMiOiI0YzdhMTM3MWFlNWUxODUwNTJiOWM3NDkzMWU5NDY2NGI5MjljYmU0ZTgxN2QxYjVkYmY1OTg1ZTY5YmM5ZTJkIiwidGFnIjoiIn0%3D; ladybird_session=wlXlRruX2PfaGJavVPNjRis2N3hxByorNqLQ96CL; XSRF-TOKEN=eyJpdiI6ImVsTFArVE5FOVY5REpjalA2Rlcyc3c9PSIsInZhbHVlIjoiTUpVNC9PL01DVWljQ0huNmdVN2YzNTB0SDlIbEJvYTh5ZmtQb3hwTWVPOU9kY1FSYW8wTlFWYitoQnF6T1dML0J3blNmZVJ1cXFicFZzanBDUS9lVGJ6RmJLUnd3VGs4WGU1Q3FjY0VrcG43a2dGTmhpcXVsY3JHUzR0eUVIRWMiLCJtYWMiOiJjYTkxYzUwOTFjOGVlNTRiYzI1YzRjNDk3ODhkZWI5NjkzYjRkYTM0OWY0OGJlNjIwMDFlMjBmYTljODIwMmNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641885943\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1305276711 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305276711\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1900987573 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 01:58:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1900987573\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-201321886 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/management/balances</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201321886\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}