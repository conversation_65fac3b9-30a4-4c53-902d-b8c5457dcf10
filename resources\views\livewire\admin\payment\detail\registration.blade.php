@extends('livewire.admin.payment.detail.index')
@section('detail-tab')
<div>
    <div class="card mb-3">
        <div class="card-body">
            <dl class="horizon-definition-list mb-4">
                <dt class="small">{{ trans2('screens.payment.detail.payment_detail_title', ['id' => $loanSchedule->id]) }}</dt>

                <dd class="small">
                    @if($status == \App\Enums\PaymentStatusEnum::PAID)
                    {{ trans2('screens.payment.detail.status_paid') }}
                    @elseif($status == \App\Enums\PaymentStatusEnum::PAID_LUMP_SUM)
                    {{ trans2('screens.payment.detail.payment_title_paid_lump_sum') }}
                    @elseif($status == \App\Enums\PaymentStatusEnum::NOT_PAID_OVERDUE)
                    {{ trans2('screens.payment.detail.payment_title_not_paid_overdue') }}
                    @endif
                </dd>
            </dl>
            <div class="container-min">
                <h2 class="fs-20 mt-5 mb-4">
                    @if($status == \App\Enums\PaymentStatusEnum::PAID)
                    {{ trans2('screens.payment.detail.status_paid') }}
                    @elseif($status == \App\Enums\PaymentStatusEnum::PAID_LUMP_SUM)
                    {{ trans2('screens.payment.detail.status_paid_lump_sum') }}
                    @elseif($status == \App\Enums\PaymentStatusEnum::NOT_PAID_OVERDUE)
                    {{ trans2('screens.payment.detail.status_not_paid_overdue') }}
                    @endif
                </h2>
                <dl class="d-flex align-items-center mb-5">
                    <dt>入金ステータス：</dt>
                    <dd class="ms-2 d-flex align-items-center">
                        <span
                            class="badge badge-status rounded-pill badge-{{$payment_status_badge_class}} px-4 fs-14 mb-1">
                            {{$payment_status_text}}
                        </span>
                        <div class="mx-3 pb-1">
                            <img src="{{ asset('assets/admin/img/ui/ui_history_arrow.svg') }}" alt="">
                        </div>
                        <span
                            class="badge badge-status rounded-pill badge-{{ \App\Enums\PaymentStatusEnum::colors()[$status] ?? 'status-secondary' }} px-4 fs-14 mb-1">
                            {{ \App\Enums\PaymentStatusEnum::texts()[$status] ?? '' }}
                        </span>
                    </dd>
                </dl>

                <h2 class="heading-2 mt-0">登録先契約情報</h2>
                <table class="table table-details">
                    <tbody>
                        <tr>
                            <th>支払ID</th>
                            <td>{{ $loanSchedule->id }}</td>
                        </tr>
                        <tr>
                            <th>お名前</th>
                            <td>
                                <a href="{{ getRoute('customer.details', ['id' => $loanSchedule->customer->id]) }}">
                                    {{ $loanSchedule->customer->last_name }}　{{ $loanSchedule->customer->first_name
                                    }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>お名前（フリガナ）</th>
                            <td>{{ $loanSchedule->customer->last_name_kana }}　{{
                                $loanSchedule->customer->first_name_kana }}</td>
                        </tr>
                        <tr>
                            <th>ローン契約金額</th>
                             <td>{{ $loanSchedule->application->total_amount > 0 ? formatCurrency($loanSchedule->application->total_amount) : '-' }}
                            </td>
                        </tr>
                        <tr>
                            <th>ローン支払期間</th>
                            <td>
                                {{ $loanSchedule->application->payment_start_month ?
                                substr($loanSchedule->application->payment_start_month, 0, 4) . '/' .
                                substr($loanSchedule->application->payment_start_month, 4, 2) . '/27' : '-' }} ～
                                {{ $loanSchedule->application->payment_last_month ?
                                substr($loanSchedule->application->payment_last_month, 0, 4) . '/' .
                                substr($loanSchedule->application->payment_last_month, 4, 2) . '/27' : '-' }}
                            </td>
                        </tr>
                    </tbody>
                </table>

                <h2 class="heading-2">入金情報</h2>
                <div class="row">
                    <div class="col-6 pe-4">
                        <table class="table table-edit">
                            <tbody>
                                <tr>
                                    <th class="w-px-180">変更後のステータス</th>
                                    <td>
                                        <span
                                            class="status-icon {{ \App\Enums\PaymentStatusEnum::colors()[$status] ?? 'status-secondary' }}"></span>
                                        {{ \App\Enums\PaymentStatusEnum::texts()[$status] ?? 'status-secondary' }}
                                    </td>
                                </tr>
                                <tr>
                                    <th class="w-px-180">入金予定日</th>
                                    <td>{{ $loanSchedule->payment_plan_date }}</td>
                                </tr>
                                @if ($status != \App\Enums\PaymentStatusEnum::NOT_PAID_OVERDUE)
                                <tr>
                                    <th class="required w-px-180">
                                        実入金日<span class="required-icon">必須</span>
                                    </th>
                                    <td class="px-3">
                                        <input type="text" name="payment_date" value="{{ old('payment_date') }}"
                                            class="form-control datepicker form-required w-100"
                                            wire:model="registrationForm.payment_date" data-date-format="yy/mm/dd"
                                            x-init="setTimeout(() => initializeDatepickers(), 100)">
                                        @error('registrationForm.payment_date')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </td>
                                </tr>
                                @else
                                <tr>
                                    <th class="required w-px-180">
                                        実入金日
                                    </th>
                                    <td>
                                        @if($paymentDate->isEmpty())
                                        -
                                        @else
                                        {!! $paymentDate->pluck('payment_date')->implode('<br>') !!}
                                        @endif
                                    </td>
                                </tr>
                                @endif
                                <tr>
                                    <th class="w-px-180">実返金日</th>
                                    <td>
                                        @if($loanSchedule->loanRefunds->isEmpty())
                                        -
                                        @else
                                        {!! $loanSchedule->loanRefunds->pluck('payment_date')->implode('<br>') !!}
                                        @endif
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-6 ps-4">
                        <table class="table table-edit">
                            <tbody>
                                <tr>
                                    <th class="w-px-180">支払総額</th>
                                    <td>{{ formatCurrency($loanSchedule->total_amount) }}</td>
                                </tr>
                                <tr>
                                    <th class="w-px-180">元金（税込）</th>
                                    <td>{{ formatCurrency($loanSchedule->total_amount -
                                        $perPaymentAmount) }}</td>
                                </tr>
                                <tr>
                                    <th class="w-px-180">分割手数料</th>
                                    <td>{{ ($perPaymentAmount > 0) ? formatCurrency($perPaymentAmount) : '-' }}
                                    </td>
                                </tr>
                                <tr>
                                    <th class="w-px-180">損害遅延金</th>
                                    <td>{{ ($loanSchedule->loanArrears->sum('delay_damage_amount') > 0) ?
                                        formatCurrency($loanSchedule->loanArrears->sum('delay_damage_amount')) : '-'
                                        }}</td>
                                </tr>
                                <tr>
                                    <th class="w-px-180">再発行手数料</th>
                                    <td>{{ $loanSchedule->loanArrears->sum('reissue_fee') > 0 ?
                                        formatCurrency($loanSchedule->loanArrears->sum('reissue_fee')) : '-' }}</td>
                                </tr>
                                <tr>
                                    <th class="w-px-180">過不足金</th>
                                    <td>{{formatCurrency($loanSchedule->balance_difference) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <h2 class="heading-2">備考</h2>
                <div class="mb-5">
                    <textarea name="comment" class="form-control bg-light-blue" wire:model="registrationForm.comment"
                        rows="8">{{ old('comment', $loanSchedule->comment) }}</textarea>
                    @error('registrationForm.comment')
                    <div class="text-danger">{{ $message }}</div>
                    @enderror
                </div>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <a href="{{ getRoute('payment.details', ['id' => $loanSchedule->id]) }}" class="btn-text-prev small">登録せずに戻る</a>
                    <button wire:click.prevent="registrationPayment" class="btn btn-dark btn-medium">登録</button>
                </div>
            </div>
        </div>
    </div>
</div>
@include('livewire.admin.payment.script')
@endsection
