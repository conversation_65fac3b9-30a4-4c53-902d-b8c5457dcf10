<?php

use Illuminate\Database\Migrations\Migration;
use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('application_inspection_status', function (BlueprintCustom $table) {
            $table->increments('id')->comment('ID');
            $table->integer('application_id')->index()->comment('申込ID')->index();
            $table->integer('to_shop_id')->comment('依頼先店舗ID')->nullable();
            $table->integer('from_shop_id')->comment('依頼元店舗ID')->nullable();
            $table->text('comment')->comment('コメント')->nullable();
            $table->string('file_name', 512)->comment('ファイル名')->nullable();
            $table->string('file_url', 512)->comment('ファイルURL')->nullable();
            $table->char('status', 1)->comment('審査ステータス');
            $table->char('cancel_status', 1)->comment('キャンセルステータス')->nullable();
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SchemaCustom::dropIfExists('application_inspection_status');
    }
};
