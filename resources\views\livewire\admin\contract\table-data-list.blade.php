@php
$contractStatusText = \App\Enums\ContractStatusEnum::texts();
$contractStatusColor = \App\Enums\ContractStatusEnum::colors();
@endphp
<div>
    {{-- skeleton loading overlay --}}
    <div wire:loading.flex class="row">
        @include('components.loading-overlay')
    </div>
    <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false" style="margin: 0px;">
        <table class="table table-borderless table-thead-bordered table-align-middle table-database" id="dataListTable">
            <thead>
                <tr>
                    <th class="text-nowrap" scope="col">{{ transm('_default.attributes.id') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('contract.attributes.full_name') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('contract.attributes.payment_company_flag') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('contract.attributes.regist_number') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('contract.attributes.contract_status') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('contract.attributes.contract_date') }}</th>
                    <th class="text-nowrap" scope="col">{{ transm('contract.attributes.courses') }}</th>
                </tr>
            </thead>
            <tbody>
                @if ($tableData->isNotEmpty())
                    @foreach($tableData as $item)
                        <tr wire:navigate href="{{ getRoute('contract.details', ['id' => $item?->id]) }}" data-link>
                            <td class="text-nowrap">{{ data_get($item,'contract_id') }}</td>
                            <td class="text-break">{{ data_get($item,'customer.full_name') }}</td>
                            <td class="text-nowrap">{{ data_get($item?->payment_company_flag,'text') }}</td>
                            <td class="text-nowrap">{{ data_get($item,'regist_number') }}</td>
                            <td class="text-break">
                                @if ($item->contract_status)
                                    <span class="badge badge-status rounded-pill badge-lg badge-{{ $contractStatusColor[$item->contract_status?->value] }}">{{ $contractStatusText[$item->contract_status?->value] }}</span>
                                @endif
                            </td>
                            <td class="text-nowrap">{{ data_get($item,'contract_date') }}</td>
                            <td class="text-break">
                                @foreach ( data_get($item,'courses') as $course)
                                    <div>
                                        {{ joinSlash(
                                            data_get($item, 'shopBrand.name'),
                                            data_get($course, 'itemType.name'),
                                            data_get($course, 'name_application'),
                                        ) }}
                                    </div>
                                @endforeach
                            </td>
                        </tr>
                    @endforeach
                @endif
            </tbody>
        </table>
        @if ($tableData->isEmpty())
            @include('components.no-result-found')
        @endif
    </div>
    <div class="text-center mt-4">
        @include('components.pagination-range-text', ['items' => $tableData])
        {{ $tableData->links('components.pagination') }}
    </div>
</div>
