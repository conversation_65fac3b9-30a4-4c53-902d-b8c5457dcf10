<?php

namespace App\Repositories;

use App\Enums\RefundFlagEnum;
use App\Models\LoanArrear;
use App\Models\LoanOverpayment;
use App\Models\LoanPayment;
use App\Models\LoanPaymentAllocation;

class LoanOverPaymentRepository extends CustomRepository
{
    protected $model = LoanOverpayment::class;

    public function getListByApplicationAndPaymentStatus($applicationId, $refundedFlag)
    {
        $q = $this->select(['*'])->orderBy($this->modelField('ins_date'), 'asc')
            ->where($this->modelField('application_id'), $applicationId)
            ->where($this->modelField('refunded_flag'), $refundedFlag);

        return $q->get();
    }

    public function updateLoanOverPayment($paymentId, $scheduleId, $userId)
    {
        return $this->where($this->modelField('loan_payment_id'), $paymentId)
            ->where($this->modelField('loan_schedule_id'), $scheduleId)
            ->where($this->modelField('del_flag'), '0')
            ->update([
                'del_flag' => '1',
                'refunded_flag' => '0',
                'upd_id' => $userId,
                'upd_date' => now(),
            ]);
    }

    public function findDeletingByLoanPaymentId($loanPaymentid)
    {
        return $this->select([$this->modelField('id')])
            ->where($this->modelField('loan_payment_id'), $loanPaymentid)
            ->where($this->modelField('refunded_flag'), RefundFlagEnum::UNREFUNDED)
            ->get()->first();
    }
}
