
#SQL in Request: URI= | ACTION= | SCRIPT=C:\xampp\htdocs\ladybird/artisan | SERVER=http://localhost | IP=************** | AGENT=Windows NT DESKTOP-CG9QTSJ 10.0 build 19045 (Windows 10) AMD64 | REFERER= | 
[2025-07-17 18:46:32] (Time: 05.97) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0'  
[2025-07-17 18:46:34] (Time: 00.53) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1  
[2025-07-17 18:46:34] (Time: 01.03) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:46:34] (Time: 01.45) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:46:34] (Time: 00.46) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1  
[2025-07-17 18:46:34] (Time: 00.43) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:46:34] (Time: 00.43) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:46:34] (Time: 00.45) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1  
[2025-07-17 18:46:34] (Time: 00.51) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:46:34] (Time: 00.41) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:46:34] (Time: 00.42) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1  
[2025-07-17 18:46:34] (Time: 00.39) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:46:34] (Time: 00.42) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1  

#SQL in Request: URI= | ACTION= | SCRIPT=C:\xampp\htdocs\ladybird/artisan | SERVER=http://localhost | IP=************** | AGENT=Windows NT DESKTOP-CG9QTSJ 10.0 build 19045 (Windows 10) AMD64 | REFERER= | 
[2025-07-17 18:47:01] (Time: 02.91) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0'  
[2025-07-17 18:47:01] (Time: 00.39) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1  
[2025-07-17 18:47:01] (Time: 00.42) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:47:01] (Time: 00.39) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:47:01] (Time: 00.37) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1  
[2025-07-17 18:47:01] (Time: 00.37) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:47:01] (Time: 00.44) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:47:01] (Time: 00.39) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1  
[2025-07-17 18:47:01] (Time: 00.35) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:47:01] (Time: 00.40) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:47:01] (Time: 00.34) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1  
[2025-07-17 18:47:01] (Time: 00.37) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:47:01] (Time: 00.41) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1  

#SQL in Request: URI= | ACTION= | SCRIPT=C:\xampp\htdocs\ladybird/artisan | SERVER=http://localhost | IP=************** | AGENT=Windows NT DESKTOP-CG9QTSJ 10.0 build 19045 (Windows 10) AMD64 | REFERER= | 
[2025-07-17 18:48:00] (Time: 02.93) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0'  
[2025-07-17 18:48:00] (Time: 00.38) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1  
[2025-07-17 18:48:00] (Time: 00.43) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:48:00] (Time: 00.39) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:48:00] (Time: 00.41) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1  
[2025-07-17 18:48:00] (Time: 00.38) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:48:00] (Time: 00.41) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:48:00] (Time: 00.42) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1  
[2025-07-17 18:48:00] (Time: 00.33) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:48:00] (Time: 00.41) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:48:00] (Time: 00.37) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1  
[2025-07-17 18:48:00] (Time: 00.36) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:48:00] (Time: 00.47) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1  

#SQL in Request: URI= | ACTION= | SCRIPT=C:\xampp\htdocs\ladybird/artisan | SERVER=http://localhost | IP=************** | AGENT=Windows NT DESKTOP-CG9QTSJ 10.0 build 19045 (Windows 10) AMD64 | REFERER= | 
[2025-07-17 18:49:00] (Time: 02.93) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0'  
[2025-07-17 18:49:00] (Time: 00.46) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1  
[2025-07-17 18:49:00] (Time: 00.44) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:49:00] (Time: 00.39) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:49:00] (Time: 00.44) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1  
[2025-07-17 18:49:00] (Time: 00.42) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:49:00] (Time: 00.46) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:49:00] (Time: 00.40) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1  
[2025-07-17 18:49:00] (Time: 00.39) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:49:00] (Time: 00.39) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:49:00] (Time: 00.38) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1  
[2025-07-17 18:49:00] (Time: 00.41) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:49:00] (Time: 00.40) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1  

#SQL in Request: URI= | ACTION= | SCRIPT=C:\xampp\htdocs\ladybird/artisan | SERVER=http://localhost | IP=************** | AGENT=Windows NT DESKTOP-CG9QTSJ 10.0 build 19045 (Windows 10) AMD64 | REFERER= | 
[2025-07-17 18:54:00] (Time: 03.31) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0'  
[2025-07-17 18:54:00] (Time: 00.39) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1  
[2025-07-17 18:54:00] (Time: 00.42) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:54:00] (Time: 00.42) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:54:00] (Time: 00.36) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1  
[2025-07-17 18:54:00] (Time: 00.38) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:54:00] (Time: 00.41) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:54:00] (Time: 00.41) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1  
[2025-07-17 18:54:00] (Time: 00.37) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:54:00] (Time: 00.38) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:54:00] (Time: 00.36) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1  
[2025-07-17 18:54:00] (Time: 00.39) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:54:00] (Time: 00.41) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1  

#SQL in Request: URI= | ACTION= | SCRIPT=C:\xampp\htdocs\ladybird/artisan | SERVER=http://localhost | IP=************** | AGENT=Windows NT DESKTOP-CG9QTSJ 10.0 build 19045 (Windows 10) AMD64 | REFERER= | 
[2025-07-17 18:55:00] (Time: 03.05) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0'  
[2025-07-17 18:55:00] (Time: 00.43) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1  
[2025-07-17 18:55:00] (Time: 00.45) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:55:00] (Time: 00.41) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:55:00] (Time: 00.39) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1  
[2025-07-17 18:55:00] (Time: 00.38) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:55:00] (Time: 00.40) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:55:00] (Time: 00.43) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1  
[2025-07-17 18:55:00] (Time: 00.35) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:55:00] (Time: 00.31) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1  
[2025-07-17 18:55:00] (Time: 00.32) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1  
[2025-07-17 18:55:00] (Time: 00.37) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0'  
[2025-07-17 18:55:00] (Time: 00.44) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1  
