{"__meta": {"id": "01K03AMCMH2XXE45R5XDV7PKQT", "datetime": "2025-07-14 11:01:14", "utime": **********.129923, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 20, "messages": [{"message": "[11:01:14] LOG.debug: (Time: 21.87) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.025335, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 02.22) SQL: select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.049035, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.86) SQL: select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.058429, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.71) SQL: select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.064745, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.54) SQL: select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.068933, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.072314, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.92) SQL: select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.076159, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.48) SQL: select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.079299, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.082252, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.49) SQL: select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.085283, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.46) SQL: select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.088325, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.091454, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.48) SQL: select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.094771, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.47) SQL: select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.097738, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.52) SQL: select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.100909, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.48) SQL: select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.103945, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 04.42) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.111845, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.46) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.114936, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.45) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.117856, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:14] LOG.debug: (Time: 00.50) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.120975, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.801638, "end": **********.129958, "duration": 0.3283200263977051, "duration_str": "328ms", "measures": [{"label": "Booting", "start": **********.801638, "relative_start": 0, "end": **********.973544, "relative_end": **********.973544, "duration": 0.****************, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.973552, "relative_start": 0.*****************, "end": **********.12996, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.981697, "relative_start": 0.*****************, "end": **********.983486, "relative_end": **********.983486, "duration": 0.0017888545989990234, "duration_str": "1.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.127748, "relative_start": 0.****************, "end": **********.128356, "relative_end": **********.128356, "duration": 0.0006079673767089844, "duration_str": "608μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x livewire.admin.dashboard.review-table", "param_count": null, "params": [], "start": **********.125183, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/dashboard/review-table.blade.phplivewire.admin.dashboard.review-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fdashboard%2Freview-table.blade.php&line=1", "ajax": false, "filename": "review-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.dashboard.review-table"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 20, "nb_statements": 20, "nb_visible_statements": 20, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.037680000000000005, "accumulated_duration_str": "37.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.003597, "duration": 0.02187, "duration_str": "21.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 58.041}, {"sql": "select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.0471492, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 58.041, "width_percent": 5.892}, {"sql": "select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.057844, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 63.933, "width_percent": 2.282}, {"sql": "select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [3, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.06423, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 66.215, "width_percent": 1.884}, {"sql": "select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [4, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.068511, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 68.1, "width_percent": 1.433}, {"sql": "select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.071968, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 69.533, "width_percent": 1.194}, {"sql": "select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.075351, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 70.727, "width_percent": 2.442}, {"sql": "select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.0789192, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 73.169, "width_percent": 1.274}, {"sql": "select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [8, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.081897, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 74.443, "width_percent": 1.194}, {"sql": "select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [9, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.084892, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 75.637, "width_percent": 1.3}, {"sql": "select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [10, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.087971, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 76.937, "width_percent": 1.221}, {"sql": "select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.0912461, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 78.158, "width_percent": 1.194}, {"sql": "select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [12, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.094393, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 79.352, "width_percent": 1.274}, {"sql": "select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.0973692, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 80.626, "width_percent": 1.247}, {"sql": "select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [14, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.10049, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 81.874, "width_percent": 1.38}, {"sql": "select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.1035662, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:424", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 424}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=424", "ajax": false, "filename": "ApplicationRepository.php", "line": "424"}, "connection": "local-ladybird", "explain": null, "start_percent": 83.254, "width_percent": 1.274}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, 3, 4, 5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.1075249, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 84.528, "width_percent": 11.73}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, 8, 9, 10, 11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.1145742, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 96.258, "width_percent": 1.221}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, 12, 13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.117505, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 97.479, "width_percent": 1.194}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.120579, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 98.673, "width_percent": 1.327}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.dashboard.review-table #bWVsQqLBpoJFPtipPxPp": "array:4 [\n  \"data\" => array:5 [\n    \"shopId\" => \"\"\n    \"shopBrandId\" => \"\"\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.dashboard.review-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Dashboard\\ReviewTable\"\n  \"id\" => \"bWVsQqLBpoJFPtipPxPp\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ijOLndFWNGcJNdFIRj4yCtVGCX5UnkXF8aPoDa9H", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/management\"\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "331ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-763404847 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-763404847\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2126945324 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ijOLndFWNGcJNdFIRj4yCtVGCX5UnkXF8aPoDa9H</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"421 characters\">{&quot;data&quot;:{&quot;shopId&quot;:&quot;&quot;,&quot;shopBrandId&quot;:&quot;&quot;,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;bWVsQqLBpoJFPtipPxPp&quot;,&quot;name&quot;:&quot;admin.dashboard.review-table&quot;,&quot;path&quot;:&quot;management&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;props&quot;:[&quot;shopId&quot;,&quot;shopBrandId&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;d22ecd3211cdb78fb2324de10373e653e60abb7b219547aa929552eaff035241&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"284 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJzaG9wQnJhbmRJZCI6IiIsInNob3BJZCI6IiJ9LHsicyI6ImFyciJ9XX0sIm1lbW8iOnsiaWQiOiJORTVHWjBZV0RFdkV5NzF0MnUzMSIsIm5hbWUiOiJfX21vdW50UGFyYW1zQ29udGFpbmVyIn0sImNoZWNrc3VtIjoiY2YyZjE0YTg5MjkzYWM0N2UxY2MyZDBmY2MyNDcwNDIyMmJlYmZiZGE1ZWNjMmI3Y2NmMTdjOWNlNmQ1NDUxYyJ9</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126945324\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1496425947 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">924</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"844 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijk5YlFTdXhkcHN1NWJTTG9CUFF2V3c9PSIsInZhbHVlIjoib1ozamZZUWJDenNYL1NGd0tHbU1OVUZvT2R1LzlYeDdDT2hUQXZOK3JwaUo0M1hoZ3NhTk5qTkkzWUdOMUVreFhMQlE2VHZJeVJBbDVYWXVqVGJCT0pTTTVRalQ5K29xUTdDNE9DUHRlV1h6cHRIbXFVVTEwZDJ6SFpIbFZGU21VWnVhV2hIUlVvanYxS215NU1RbkxnPT0iLCJtYWMiOiI0YmVlYjllZWE0NGM2M2I5ZTNkNjg1OWZmOThhODQyZmI2ZWViNDQ1MGNiZTZkMjEzOTNkM2ZkNWQyODQ4YjcwIiwidGFnIjoiIn0%3D; ladybird_session=2Sx4MkUyNzL14QFL58Z9wEUkQyvqFK2yguYArzvI; XSRF-TOKEN=eyJpdiI6IkNGS3dXRXUvLzJOa1Q2S3p6ZUgzRmc9PSIsInZhbHVlIjoiT3RqRWFsZEpVUUtMemcwUVdxajNGNjJWMnAwdDhNRGVVZWFUTDlpbFhQM1BlNUljY24vdElMVUpUTk5JaGluREJ4S2RpalAzTVZsRHFHbDdnVnREb1dVTGJaSUtVM3M4VnRPMGt4WmlzWGFNQUF3UzcvVm9uOTRkTmNoV0FkaDUiLCJtYWMiOiJmNDE0MDBiMDVjMjgwODFjNzE1OWQyN2NiNWZmNjM2NzRiNjkzNDVlZjNhY2RmYTNiYzdkNTJiZTRjOTc0OGU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496425947\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1688150200 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ijOLndFWNGcJNdFIRj4yCtVGCX5UnkXF8aPoDa9H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688150200\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-67637997 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 02:01:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67637997\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-278176993 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ijOLndFWNGcJNdFIRj4yCtVGCX5UnkXF8aPoDa9H</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278176993\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}