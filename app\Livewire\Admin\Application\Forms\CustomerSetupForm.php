<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\SexEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Models\Customer;
use App\Rules\TelValidationRule;
use App\Rules\ZipCodeValidationRule;
use Illuminate\Validation\Rule;

class CustomerSetupForm extends BaseAdminForm
{
    public $last_name = '';
    public $first_name = '';
    public $first_name_kana = '';
    public $last_name_kana = '';
    public $sex = SexEnum::FEMALE;
    public $birthday = '';
    public $email = '';
    public $tel1 = '';
    public $tel2 = '';
    public $tel3 = '';
    public $zip1 = '';
    public $zip2 = '';
    public $pref_id = '';
    public $city = '';
    public $address = '';
    public $building = '';
    public $pref_kana_id = '';
    public $city_kana = '';
    public $address_kana = '';
    public $building_kana = '';

    public function rules()
    {
        $rules = [
            'last_name' => 'required|string|max:128',
            'first_name' => 'required|string|max:128',
            'first_name_kana' => 'required|katakana|max:128',
            'last_name_kana' => 'required|katakana|max:128',
            'sex' => ['required', Rule::in(SexEnum::getValues())],
            'birthday' => 'required|date',
            'email' => 'nullable|email|check_email|max:128',
            'tel1' => ['required', 'max:32', new TelValidationRule()],
            'tel2' => ['required', 'max:32', new TelValidationRule()],
            'tel3' => ['required', 'max:32', new TelValidationRule()],
            'zip1' => ['required', 'max:4', new ZipCodeValidationRule()],
            'zip2' => ['required', 'max:4', new ZipCodeValidationRule()],
            'pref_id' => 'required|exists:prefs,id',
            'city' => 'required|string|max:1000',
            'address' => 'required|string|max:1000',
            'building' => 'nullable|string|max:1000',
            'pref_kana_id' => 'required',
            'city_kana' => 'required|katakana_full|max:1000',
            'address_kana' => 'required|katakana_full|max:1000',
            'building_kana' => 'nullable|katakana_full|max:1000',
        ];

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.customer.attributes');
    }

    public function fillFromCustomer(Customer $customer): void
    {
        $this->last_name = $customer->last_name ?? '';
        $this->first_name = $customer->first_name ?? '';
        $this->first_name_kana = $customer->first_name_kana ?? '';
        $this->last_name_kana = $customer->last_name_kana ?? '';
        $this->sex = (string)($customer->sex ?? '1');
        $this->birthday = $customer->birthday ?? '';
        $this->email = $customer->email ?? '';

        $this->tel1 = $customer->tel1 ?? '';
        $this->tel2 = $customer->tel2 ?? '';
        $this->tel3 = $customer->tel3 ?? '';

        $this->zip1 = $customer->zip1 ?? '';
        $this->zip2 = $customer->zip2 ?? '';

        $this->pref_id = $customer->pref_id ?? '';
        $this->city = $customer->city ?? '';
        $this->address = $customer->address ?? '';
        $this->building = $customer->building ?? '';

        $this->pref_kana_id = $customer->pref_kana_id ?? '';
        $this->city_kana = $customer->city_kana ?? '';
        $this->address_kana = $customer->address_kana ?? '';
        $this->building_kana = $customer->building_kana ?? '';
    }
}
