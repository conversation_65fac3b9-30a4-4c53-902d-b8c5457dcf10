<?php

namespace App\Services;

use App\Repositories\LoanPaymentRepository;
use App\Repositories\LoanTransactionLogRepository;

class LoanPaymentService extends CustomService
{
    public function __construct(
        public LoanPaymentRepository $loanPaymentRepository,
    ) {
        parent::__construct();
    }

    public function getLoanPayment($id)
    {
        return $this->loanPaymentRepository->find($id);
    }

    public function getLoanPaymentsWithLogsByApplicationId($applicationId)
    {
        return $this->loanPaymentRepository->getLoanPaymentsWithLogsByApplicationId($applicationId);
    }
}
