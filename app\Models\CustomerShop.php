<?php

namespace App\Models;

use App\Models\Base\CustomModel;
use App\Models\Concerns\HasNameLang;
use App\Models\Presenters\PCustomerShop;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerShop extends CustomModel
{
    use PCustomerShop;
    use HasNameLang;

    /**
     * The table associated with the model.
     *
     * @var string
     */

    protected $table = 'customer_shops';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'customer_id',
        'shop_id',
    ];
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class,'shop_id', 'id');
    }
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class,'customer_id', 'id');
    }

    public static function getFieldRelationshipMap() :array
    {
        return [
            'customer_id' => 'customer',
            'shop_id' => 'shop',
        ];
    }
}
