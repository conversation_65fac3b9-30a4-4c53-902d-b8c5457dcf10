<?php

namespace App\Livewire\Admin\Customer;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Admin\Customer\Forms\UpdateForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\CustomerRepository;
use App\Services\ApplicationService;
use App\Services\CustomerService;
use App\Services\HolidayService;
use App\Services\PrefService;
use Gate;
use Livewire\Attributes\On;
use App\Services\ToastService;


class Update extends BaseAdminPageComponent
{
    public UpdateForm $updateForm;

    public $idCustomer;
    public $isCanDelete;
    public $selectedHoliday;

    public $customer = null;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CUSTOMER;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
    }

    public function mount($id)
    {
        $this->idCustomer = $id;

        $this->customer = app()->make(CustomerRepository::class)->getBasicInfoCustomer($this->idCustomer);

        $this->selectedHoliday = app(HolidayService::class)->getAllHolidays();

        if (empty($this->customer)) {
            app(ToastService::class)->error(__('messages.no_data'));
            return $this->redirect(route('admin.customer.index'));
        }

        $brandIds = $this->customer->applications->pluck('brand_id');
        $shopIds = $this->customer->applications->pluck('shop_id');
        Gate::authorize('allowed-update', [SidebarMenuEnum::CUSTOMER, $brandIds, $shopIds]);

        $this->updateForm->fill($this->customer->toArray());

        $countApp = app(ApplicationService::class)->getCountAppByCustomerId($this->idCustomer);
        $this->isCanDelete = !$countApp;
    }

    public function validateUpdate()
    {

        $this->updateForm->validate();

        $this->setDataEditConfirmModal(getConstant('SCREENS.CUSTOMER'), 'customer-update');
    }

    #[On('customer-update')]
    public function update()
    {
        $brandIds = $this->customer->applications->pluck('brand_id');
        $shopIds = $this->customer->applications->pluck('shop_id');
        Gate::authorize('allowed-update', [SidebarMenuEnum::CUSTOMER, $brandIds, $shopIds]);

        try {
            $params = $this->updateForm->all();
            if (!$params['black_flag']) {
                $params['black_flag'] = 0;
            }

            $customer = app(CustomerService::class)->getCustomer($this->idCustomer);
            $customer->snapshotForTracking();

            if (empty($customer)) {
                app(ToastService::class)->error(__('messages.no_data'));
                $this->redirect(route('admin.customer.index'));
            }

            $update = app(CustomerService::class)->update($this->idCustomer, $params);

            if ($update) {
                app(ToastService::class)->updateSuccess();
            } else {
                app(ToastService::class)->updateError();
            }

            return redirect()->route('admin.customer.details', $this->idCustomer);
        } catch (\Throwable $th) {
            logError($th);
            return redirect()->route('admin.customer.details', $this->idCustomer);
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.detail.update', [
            'customer' => $this->customer,
            'listPref' => app(PrefService::class)->getAllPrefs(),
        ]);
    }
}
