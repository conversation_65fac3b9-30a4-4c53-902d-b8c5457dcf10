<?php

use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('loan_transaction_logs', function (BlueprintCustom $table) {
            $table->id();
            $table->integer('customer_id')->comment('顧客ID')->index();
            $table->integer('application_id')->comment('申込ID')->index();
            $table->char('type',1)->comment('種別')->index();
            $table->integer('loan_schedule_id')->comment('返済予定ID')->nullable()->index();
            $table->integer('loan_payment_id')->comment('ローン入金ID')->nullable()->index();
            $table->integer('loan_payment_allocation_id')->comment('ローン充当ID')->nullable()->index();
            $table->integer('loan_overpayment_id')->comment('ローン過入金ID')->nullable()->index();
            $table->integer('loan_refund_id')->comment('ローン返金ID')->nullable()->index();
            $table->integer('loan_arrear_id')->comment('ローン不足金ID')->nullable()->index();
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SchemaCustom::dropIfExists('loan_transaction_logs');
    }
};
