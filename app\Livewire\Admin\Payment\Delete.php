<?php

namespace App\Livewire\Admin\Payment;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\LoanScheduleService;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use App\Services\ToastService;

class Delete extends BaseAdminPageComponent
{
    #[Reactive]
    public $id;

    #[Reactive]
    public $isCanDelete;

    #[On('delete')]
    public function delete()
    {

        try {
            $loanSchedule = app(LoanScheduleService::class)->getLoanScheduleById($this->id);

            if (empty($loanSchedule)) {
                app(ToastService::class)->error(__('messages.no_data'));
                $this->redirect(route('admin.payment.index'));
                return;
            }

            $loanSchedule->delete();

            app(ToastService::class)->deleteSuccess();

            return redirect()->route('admin.payment.index');
        } catch (\Throwable $th) {
            logError($th);
            app(ToastService::class)->deleteError();
            return;
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('payment.delete');
    }
}
