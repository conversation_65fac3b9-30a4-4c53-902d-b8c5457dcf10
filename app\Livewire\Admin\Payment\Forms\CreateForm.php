<?php

namespace App\Livewire\Admin\Payment\Forms;

use App\Enums\BlackFlagEnum;
use App\Enums\LoanTransactionTypeEnum;
use App\Enums\PaymentTypeEnum;
use App\Enums\SexEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Rules\TelValidationRule;
use App\Rules\ZipCodeValidationRule;
use Illuminate\Validation\Rule;

class CreateForm extends BaseAdminForm
{
    public $payment_date;
    public $type;
    public $payment_type;
    public $amount;

    public function rules()
    {
        $costRule = $this->costRule();

        $rules = [
            'payment_date' => 'required|date|date_format:Y/m/d',
            'type' => ['required', Rule::in(LoanTransactionTypeEnum::getValues())],
            'payment_type' => ['required', Rule::in(PaymentTypeEnum::getValues())],
            'amount' => 'required|' . $costRule. '|gt:0',
        ];

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.payment.attributes');
    }
}
