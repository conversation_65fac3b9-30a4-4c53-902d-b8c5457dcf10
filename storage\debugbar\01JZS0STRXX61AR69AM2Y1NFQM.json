{"__meta": {"id": "01JZS0STRXX61AR69AM2Y1NFQM", "datetime": "2025-07-10 10:57:02", "utime": **********.366242, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[10:57:02] LOG.debug: (Time: 16.21) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.319958, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:02] LOG.warning: Optional parameter $dataSearch declared before required parameter $paymentStatus is implicitly treated as a required parameter in C:\\xampp\\htdocs\\ladybird\\app\\Services\\LoanScheduleService.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.332082, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:02] LOG.debug: (Time: 00.98) SQL: select `brands`.`id`, `brands`.`name`, \n                SUM(CASE WHEN ls.payment_status IN (1, 2) THEN 1 ELSE 0 END) as waiting_count,\n                SUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN 1 ELSE 0 END) as deposited_count,\n                SUM(CASE WHEN ls.payment_status = 2 THEN 1 ELSE 0 END) as unpaid_count,\n                SUM(CASE WHEN ls.payment_status IN (8,9) THEN 1 ELSE 0 END) as other_count,\n                SUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN 1 ELSE 0 END) as grand_count,\n\n                SUM(CASE WHEN ls.payment_status IN (1, 2) THEN ls.total_amount ELSE 0 END) as waiting_total_amount,\n                SUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN ls.total_amount ELSE 0 END) as deposited_total_amount,\n                SUM(CASE WHEN ls.payment_status = 2 THEN ls.total_amount ELSE 0 END) as unpaid_total_amount,\n                SUM(CASE WHEN ls.payment_status IN (8,9) THEN ls.total_amount ELSE 0 END) as other_total_amount,\n                SUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN ls.total_amount ELSE 0 END) as grand_total_amount\n             from (select `loan_schedules`.`id`, `loan_schedules`.`total_amount`, `loan_schedules`.`payment_status`, `applications`.`brand_id`, `applications`.`shop_id`, `applications`.`contract_status` from `loan_schedules` inner join `applications` on `applications`.`id` = `loan_schedules`.`application_id` left join `application_courses` on `applications`.`id` = `application_courses`.`application_id` where `loan_schedules`.`del_flag` = 0 and `applications`.`del_flag` = 0 and year(`loan_schedules`.`payment_plan_date`) = 2025 and month(`loan_schedules`.`payment_plan_date`) = '06' and `applications`.`contract_status` is not null group by `loan_schedules`.`id`, `loan_schedules`.`total_amount`, `loan_schedules`.`payment_status`) as ls inner join `brands` on `brands`.`id` = `ls`.`brand_id` inner join `shop_brands` on `shop_brands`.`shop_id` = `ls`.`shop_id` where `shop_brands`.`del_flag` = 0 and `brands`.`del_flag` = 0 group by `brands`.`id`, `brands`.`name` {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.341171, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.124212, "end": **********.366263, "duration": 0.2420508861541748, "duration_str": "242ms", "measures": [{"label": "Booting", "start": **********.124212, "relative_start": 0, "end": **********.274973, "relative_end": **********.274973, "duration": 0.*****************, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.274981, "relative_start": 0.*****************, "end": **********.366264, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "91.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.281597, "relative_start": 0.*****************, "end": **********.283319, "relative_end": **********.283319, "duration": 0.001722097396850586, "duration_str": "1.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.364082, "relative_start": 0.****************, "end": **********.364703, "relative_end": **********.364703, "duration": 0.0006208419799804688, "duration_str": "621μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x livewire.admin.payment.payment-table", "param_count": null, "params": [], "start": **********.345053, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/payment/payment-table.blade.phplivewire.admin.payment.payment-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fpayment%2Fpayment-table.blade.php&line=1", "ajax": false, "filename": "payment-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.payment.payment-table"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.017190000000000004, "accumulated_duration_str": "17.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3038518, "duration": 0.016210000000000002, "duration_str": "16.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 94.299}, {"sql": "select `brands`.`id`, `brands`.`name`,\nSUM(CASE WHEN ls.payment_status IN (1, 2) THEN 1 ELSE 0 END) as waiting_count,\nSUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN 1 ELSE 0 END) as deposited_count,\nSUM(CASE WHEN ls.payment_status = 2 THEN 1 ELSE 0 END) as unpaid_count,\nSUM(CASE WHEN ls.payment_status IN (8,9) THEN 1 ELSE 0 END) as other_count,\nSUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN 1 ELSE 0 END) as grand_count,\nSUM(CASE WHEN ls.payment_status IN (1, 2) THEN ls.total_amount ELSE 0 END) as waiting_total_amount,\nSUM(CASE WHEN ls.payment_status IN (3,4,5,6,7) THEN ls.total_amount ELSE 0 END) as deposited_total_amount,\nSUM(CASE WHEN ls.payment_status = 2 THEN ls.total_amount ELSE 0 END) as unpaid_total_amount,\nSUM(CASE WHEN ls.payment_status IN (8,9) THEN ls.total_amount ELSE 0 END) as other_total_amount,\nSUM(CASE WHEN ls.payment_status IN (1,2,3,4,5,6,7,8,9,10,11) THEN ls.total_amount ELSE 0 END) as grand_total_amount\nfrom (select `loan_schedules`.`id`, `loan_schedules`.`total_amount`, `loan_schedules`.`payment_status`, `applications`.`brand_id`, `applications`.`shop_id`, `applications`.`contract_status` from `loan_schedules` inner join `applications` on `applications`.`id` = `loan_schedules`.`application_id` left join `application_courses` on `applications`.`id` = `application_courses`.`application_id` where `loan_schedules`.`del_flag` = 0 and `applications`.`del_flag` = 0 and year(`loan_schedules`.`payment_plan_date`) = 2025 and month(`loan_schedules`.`payment_plan_date`) = '\\'06\\'' and `applications`.`contract_status` is not null group by `loan_schedules`.`id`, `loan_schedules`.`total_amount`, `loan_schedules`.`payment_status`) as ls inner join `brands` on `brands`.`id` = `ls`.`brand_id` inner join `shop_brands` on `shop_brands`.`shop_id` = `ls`.`shop_id` where `shop_brands`.`del_flag` = 0 and `brands`.`del_flag` = 0 group by `brands`.`id`, `brands`.`name`", "type": "query", "params": [], "bindings": [0, 0, 2025, "'06'", 0, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 271}, {"index": 14, "namespace": null, "name": "app/Services/LoanScheduleService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\LoanScheduleService.php", "line": 33}, {"index": 15, "namespace": null, "name": "app/Livewire/Admin/Payment/PaymentTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Payment\\PaymentTable.php", "line": 114}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.340289, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "LoanScheduleRepository.php:271", "source": {"index": 13, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 271}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FLoanScheduleRepository.php&line=271", "ajax": false, "filename": "LoanScheduleRepository.php", "line": "271"}, "connection": "local-ladybird", "explain": null, "start_percent": 94.299, "width_percent": 5.701}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.payment.payment-table #EEyPYfbgUEZn0EFg9hZr": "array:4 [\n  \"data\" => array:11 [\n    \"monthIndex\" => \"-1\"\n    \"brand_id\" => null\n    \"shop_id\" => null\n    \"item_type_id\" => null\n    \"course_id\" => null\n    \"payment_plan_date_from\" => null\n    \"payment_plan_date_to\" => null\n    \"payment_status\" => null\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.payment.payment-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Payment\\PaymentTable\"\n  \"id\" => \"EEyPYfbgUEZn0EFg9hZr\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01JZS0STBV74N9W6X8YMTQTAJG&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "243ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1010566385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1010566385\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-894356135 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"495 characters\">{&quot;data&quot;:{&quot;monthIndex&quot;:null,&quot;brand_id&quot;:null,&quot;shop_id&quot;:null,&quot;item_type_id&quot;:null,&quot;course_id&quot;:null,&quot;payment_plan_date_from&quot;:null,&quot;payment_plan_date_to&quot;:null,&quot;payment_status&quot;:null,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;EEyPYfbgUEZn0EFg9hZr&quot;,&quot;name&quot;:&quot;admin.payment.payment-table&quot;,&quot;path&quot;:&quot;management\\/payments&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;f6fbab13487d90369ac63b1c956480c354a3124fb31707a0c436a0bc3a7faba3&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__dispatch</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">update-search</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>monthIndex</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-1</span>\"\n              </samp>]\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-894356135\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-256796855 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">748</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/management/payments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjliemhKQ2NSQmVpOWwrb2VBQnhKaVE9PSIsInZhbHVlIjoiSXhDNmJqdmtHU3Uvbm8ybWVHYzdoSFU1Ym1qZWF0ai9lRnlRb3JiMDFISVQ4N0dYTFJkcUNZL1M5OVJiVmRhaTJwclM3SXNjOStSRnpFZzcvb3BoUkY5RHZHQTZkdkJ1czZUaGNBZmQ5S0hpZEFrUGp5MTlJSU1aWnNUNWRtVjVvQTRLK1M4NG5JaHBpUzRWdE1vc2tBPT0iLCJtYWMiOiI0YzdhMTM3MWFlNWUxODUwNTJiOWM3NDkzMWU5NDY2NGI5MjljYmU0ZTgxN2QxYjVkYmY1OTg1ZTY5YmM5ZTJkIiwidGFnIjoiIn0%3D; ladybird_session=wlXlRruX2PfaGJavVPNjRis2N3hxByorNqLQ96CL; XSRF-TOKEN=eyJpdiI6IitNOEJIb0dPMWFqWTlLdUVFVVVqcEE9PSIsInZhbHVlIjoiSEVob0NuZjFuUXd0ZTBtVnk2bWozdExDeitOQTJLdFBjSTdJVHFJNlFlQlNDM3gwL0pyZ3F6NHU0VEtlK3dqbXJtYmNERm1uZ3RrTlhUN3ZhRXhNbERZV2VKWUdhMU5lZEhTem5MQ0lzc1MrZ3lhVFN3bUVzQ3VNbVFUdFkvV2YiLCJtYWMiOiIxMTZhMjc2MDYwYjYxODhmYjQ1MjRjNDQ5Y2YzNTEyMzgzYmE1YWQxNTcxNzRmMjI5YTkwNTA1ZTUyODJlYTkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256796855\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1231168525 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231168525\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1695143030 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 01:57:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695143030\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1218667533 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01JZS0STBV74N9W6X8YMTQTAJG&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218667533\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}