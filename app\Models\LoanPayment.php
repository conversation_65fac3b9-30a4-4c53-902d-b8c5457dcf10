<?php

namespace App\Models;

use App\Models\Base\CustomModel;
use App\Models\Presenters\PLoanPayment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LoanPayment extends CustomModel
{
    use HasFactory;
    use PLoanPayment;

    protected $table = 'loan_payments';

    protected $fillable = [
        'id',
        'customer_id',
        'application_id',
        'payment_date',
        'amount',
        'auto_manual_flag',
        'payment_type',
        'payment_company_flag',
        'result_status',
        'payment_status',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function loanTransactionLogs(): HasMany
    {
        return $this->hasMany(LoanTransactionLog::class, 'loan_payment_id', 'id');
    }
    public function loanPaymentAllocations(): HasMany
    {
        return $this->hasMany(LoanPaymentAllocation::class, 'loan_payment_id', 'id');
    }
}
