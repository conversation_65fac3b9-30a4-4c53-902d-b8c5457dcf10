const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

(async () => {
    // Get CLI arguments: [node, index.js, inputHtmlPath, outputPdfDir, pdfName]
    const args = process.argv.slice(2);
    const inputHtmlPath = args[0];
    const outputPdfDir = args[1] || '.';
    let pdfName = args[2] || 'output.pdf';
    const pageSize = args[3] || 'A4';
    const orientation = args[4] || 'portrait'; // Tham số thứ 5

    if (!inputHtmlPath) {
        console.error('Error: Path to HTML file is required.');
        process.exit(1);
    }

    // Ensure output directory exists
    if (!fs.existsSync(outputPdfDir)) {
        fs.mkdirSync(outputPdfDir, { recursive: true });
    }

    // Read HTML content
    let html;
    try {
        html = fs.readFileSync(inputHtmlPath, 'utf-8');
    } catch (err) {
        console.error(`Error reading HTML file: ${err.message}`);
        process.exit(1);
    }

    // Ensure PDF name ends with .pdf
    if (!pdfName.toLowerCase().endsWith('.pdf')) {
        pdfName += '.pdf';
    }

    const outputPdfPath = path.join(outputPdfDir, pdfName);

    // Launch Puppeteer and generate PDF
    const browser = await puppeteer.launch();
    const page = await browser.newPage();
    await page.setContent(html, { waitUntil: 'domcontentloaded' });
    await page.emulateMediaType('screen');

    await page.pdf({
        path: outputPdfPath,
        // margin: { top: '50px', right: '20px', bottom: '50px', left: '20px' },
        printBackground: true,
        format: pageSize,
        landscape: true,
    });

    await browser.close();
    console.log(`PDF exported to: ${outputPdfPath}`);
})();
