<?php

namespace App\Livewire\Admin\Payment;

use App\Livewire\Base\Features\UploadFileComponent;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use App\Services\CsvImportService;

class UploadCsv extends UploadFileComponent
{
    public $file_url;
    public function render()
    {
        return $this->viewLivewireAdmin('payment.upload-csv');
    }

    function  _finishUpload($name, $tmpPath, $isMultiple)
    {
        try {
            $this-> _finishUpload1($name, $tmpPath, $isMultiple);
        } catch (\Throwable $e) {
            $this->_uploadErrored($name, null, $isMultiple);
        }
        $this->js("hideLoading()");
        $file = TemporaryUploadedFile::createFromLivewire($tmpPath[0]);
        $fileName = $file->getClientOriginalName();
        $validateFileExt = [];
        if (pathinfo($fileName, PATHINFO_EXTENSION) !== 'csv') {
            $validateFileExt = ['validate' => false, 'errors' => [
                'file_url' => trans('validation.file_ext')
            ]];
        }
        if (!empty(data_get($validateFileExt, 'errors'))) {
            $this->file_url = null;
            throw (ValidationException::withMessages(data_get($validateFileExt, 'errors')));
        }
    }

    public function updatedFileUrl()
    {
        $this->validate([
            'file_url' => 'required|file|mimes:csv,txt|max:10240', // Max 10MB
        ]);

        $this->importDataDeposit();
    }

    public function importDataDeposit()
    {
        $path = $this->file_url->getRealPath();

        if (!file_exists($path)) {
            $this->toastError(__('messages.import_csv_failed'));
            return;
        }

        $result = app(CsvImportService::class)->importNoHeaderCsv($path, getConstant('SCREENS.PAYMENT'));

        if (!$result['status']) {
            if(!empty($result['errors'])){
                $this->dispatch('show-modal-errors',  $result['errors']);   
            } else {
                $this->toastError(__('messages.csv_data_invalid'));
            }
            return;
        }

        $this->toastSuccess(__('messages.import_success'));
    }
}
