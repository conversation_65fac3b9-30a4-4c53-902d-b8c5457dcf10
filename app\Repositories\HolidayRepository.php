<?php

namespace App\Repositories;

use App\Models\Holiday;
use Carbon\Carbon;

class HolidayRepository extends CustomRepository
{
    protected $model = Holiday::class;

    public function getHolidays(int $yearIndex = 0)
    {
        $currentYear = Carbon::now()->year + $yearIndex;
        $q = $this->model->select([
            $this->modelField('id'),
            $this->modelField('holiday_date'),
            $this->modelField('name')
        ]);
        $q->whereYear('holiday_date', '>', $currentYear - 1)
            ->whereYear('holiday_date', '<=', $currentYear);
        return $q->get();
    }

    public function getAllHolidays()
    {
        return $this->all();
    }

    public function getHolidayDatesBetween($startDate, $endDate)
    {
        return $this->whereBetween('holiday_date', [$startDate, $endDate])
            ->pluck('holiday_date')
            ->toArray();
    }
}
