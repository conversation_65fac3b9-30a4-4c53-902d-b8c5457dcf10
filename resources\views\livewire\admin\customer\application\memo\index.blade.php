@extends('livewire.admin.customer.detail.index')
@section('detail-tab')
    <div class="card mb-3">
        <div class="card-body">
            <div class="card-container border-bottom mb-5">
                <dl class="horizon-definition-list mb-4">
                    <dt class="small">{{ trans2('screens.customer.detail.application_tab') }}</dt>
                    <dd class="small">{{ trans2('screens.customer.detail.index.application_id') }}:{{ $application?->id }}</dd>
                </dl>
                <div class="d-flex justify-content-center mb-5">
                    <ul class="page-content-tabs nav nav-tabs">
                        @if($application?->status?->value !== \App\Enums\ApplicationStatusEnum::IN_PROGRESS)
                            <li class="nav-item w-px-190">
                                <a class="nav-link active">{{ trans2('screens.customer.detail.index.tab_memo') }}</a>
                            </li>
                        @endif

                        <li class="nav-item w-px-190">
                            <a href="{{ getRoute('customer.application.detail.index', [$customer?->id, $application?->id]) }}" class="nav-link">{{ trans2('screens.customer.detail.index.tab_detail') }}</a>
                        </li>
                    </ul>
                </div>
                <form action="/customers/application/memo/" method="post" role="form" class="form" id="memoForm">
                    <div class="form-block row mb-3">
                        <div class="col-8 d-flex align-items-center">
                            <div class="me-3">
                                <button type="button" class="btn-clip"></button>
                                <input type="file" name="application_file" id="" class="d-none" multiple wire:model="memoForm.files">
                            </div>
                            <div class="flex-grow-1">
                                <textarea wire:model="memoForm.comment" name="comment" class="form-control page-detail-comment-textarea flex-grow-1" placeholder="{{ trans2('screens.customer.detail.index.memo.comment_placeholder') }}" rows="9"></textarea>
                                @error('memoForm.comment')
                                    <div>
                                        <span class="message-error">{{ $message }}</span>
                                    </div>
                                @enderror
                                 @error('memoForm.files.0')
                                    <div>
                                        <span class="message-error">{{ $message }}</span>
                                    </div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-4 ps-4">
                            <div class="form-group">
                                <div class="form-label fw-normal small">{{ trans2('screens.customer.detail.index.memo.request_by') }}</div>
                                <div class="form-field">
                                    <select name="to_shop_id" wire:model="memoForm.to_shop_id" class="form-select2" style="width:100%;">
                                        @forEach($toShopIds as $key => $value)
                                            <option value="{{ $key }}">{{ $value }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="form-label fw-normal small">{{ trans2('screens.customer.detail.index.memo.inspection_status') }}</div>
                                <div class="form-field">
                                    <select name="status" wire:model="memoForm.status" class="form-select2 form-select2-status" style="width:100%;">
                                        <option value=" ">{{ trans2('not_choose') }}</option>
                                        @foreach($statuses as $key => $value)
                                            <option value="{{ $key }}" data-status="{{ $inspectionColors[$key] ?? '' }}">{{ $value }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="form-label fw-normal small">{{ trans2('screens.customer.detail.index.memo.inspection_status_cancel') }}</div>
                                <div class="form-field">
                                    <select name="cancel_status" wire:model="memoForm.cancel_status" class="form-select2" style="width:100%;" data-placeholder="未選択">
                                        <option value=" ">{{ trans2('not_choose') }}</option>
                                        @foreach($cancelStatuses as $key => $value)
                                            <option value="{{ $key }}">{{ $value }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mb-5">
                        <button
                            wire:click.prevent="validateSave"
                            wire:loading.attr="disabled"
                            wire:target="memoForm.files,validateSave"
                            class="btn btn-dark btn-medium"
                            id="btnSubmitMemo"
                        >{{ trans2('screens.customer.detail.index.memo.button_submit') }}</button>
                    </div>
                </form>
            </div>
            <div class="d-flex justify-content-between align-items-start mb-4">
                <div class="me-2 pt-4">
                    <a href="#attachmentCollapse" class="btn-text-clip collapsed" data-bs-toggle="collapse" aria-expanded="false" role="button" aria-controls="attachmentCollapse">
                     {{ trans2('screens.customer.detail.index.memo.application_files') }}(<span class="fc-primary">{{ $applicationInspectionStatuses?->where('file_name')->count() }}</span>)
                    </a>
                    <div id="attachmentCollapse" class="collapse">

                        @if($applicationInspectionStatuses->isNotEmpty())
                            @foreach($applicationInspectionStatuses as $index => $status)
                                @if($status->file_name)
                                    <div class="mt-1 d-flex">
                                        <a href="{{ getRoute('download.application_file', [$status->id]) }}" class="fc-primary">{{ $status->file_name }}</a>
                                        <span>({{ getFileSize($status->file_url) }} : {{ $status->getInsDateForCsvAttribute() }})</span>

                                        <livewire:admin.customer.application.memo.delete :id="$status->id" :key="'delete-memo-'.$status->id.'-'.$index" />
                                    </div>
                                @endif
                            @endforeach
                        @endif

                    </div>
                </div>
                <a href="{{ getRoute('customer.application.brand.edit', [$application->id]) }}" class="btn btn-dark" target="_blank">{{ trans2('screens.customer.detail.index.memo.button_edit_application') }}</a>
            </div>

            @if($applicationInspectionStatuses->isNotEmpty())
                <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
                    <table class="table table-borderless table-thead-bordered table-align-middle table-database" id="dataListTable">
                        <thead>
                            <tr>
                                <th scope="col">{{ trans2('screens.customer.detail.index.memo.history_col') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($applicationInspectionStatuses as $status)
                                @php
                                    $fromShop = $status->from_shop_id ? $status->fromShop->name : '本部';
                                    $toShop = $status->to_shop_id ? $status->toShop->name : '本部';
                                @endphp
                                <tr>
                                    <td>
                                        <div class="d-flex flex-wrap align-items-center pt-3">
                                            <h5 class="me-4 pe-2 position-relative">{{ $fromShop }}
                                                <span class="position-absolute top-0 start-100 translate-middle bg-alert rounded-circle" style="padding:.35rem;">
                                                <span class="visually-hidden">New alerts</span>
                                        </span>
                                            </h5>
                                            <div class="ps-2 pe-4 small">{{ $status->getInsDateForCsvAttribute() }}</div>
                                            <div class="small">担当者：{{ $fromShop }} → {{ $toShop }}</div>
                                        </div>
                                        <div class="d-flex flex-wrap align-items-center mt-3 pb-3">
                                            <div class="me-4">
                                                @if($status->status)
                                                    <span class="badge badge-status badge-lg rounded-pill badge-status-{{ $inspectionColors[$status->status?->value] ?? 'secondary' }}">{{ $status->status?->text ?? '未選択' }}</span>
                                                @endif

                                                @if($status->cancel_status)
                                                    <span class="badge badge-status badge-lg rounded-pill badge-status-secondary">{{ $cancelStatuseAll[$status->cancel_status?->value] ?? '未選択' }}</span>
                                                @endif
                                            </div>
                                            <div class="">{{ $status?->comment }}</div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>

        <div class="modal fade deleteModal" id="confirmDeleteMemoModal" tabindex="-1">
            <div class="modal-dialog modal-md modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body">
                        <h5 class="modal-title">
                            <span class="title-icon">{{ trans2('confirm_modal.delete') }}</span>
                        </h5>
                        <p>{{ trans2('confirm_modal.alert') }}</p>
                        <form role="form">
                            <div class="d-flex justify-content-center pt-4 pb-4">
                                <div class="d-flex">
                                    <button type="button" class="btn btn-border w-px-160 me-4" data-bs-dismiss="modal">{{ trans2('button.cancel') }}</button>
                                    <button type="button" class="btn btn-danger w-px-160" data-bs-dismiss="modal" wire:click.prevent="deleteMemoFile">{{ trans2('button.delete_to') }}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    @livewireScripts

    <script>
        $(document).ready(function(){
            $('button.btn-clip').click(function(e) {
                e.preventDefault();
                $(this).siblings('input[type="file"]').trigger('click');
            })

            // handle disable button submit when not change input form
            // const $form = $('#memoForm');
            // const $file = $form.find('input[type="file"][name="application_file"]');
            // const $textarea = $form.find('textarea[name="comment"]');
            // const $select1 = $form.find('select[name="to_shop_id"]');
            // const $select2 = $form.find('select[name="status"]');
            // const $select3 = $form.find('select[name="cancel_status"]');
            //
            // const initial = {
            //         file: $file.val(),
            //         textarea: $textarea.val(),
            //         select1: $select1.val(),
            //         select2: $select2.val(),
            //         select3: $select3.val()
            //     };
            //
            // function isFormChanged() {
            //     if ($file.val() != initial.file) return true;
            //     if ($textarea.val() != initial.textarea) return true;
            //     if ($select1.val() != initial.select1) return true;
            //     if ($select2.val() != initial.select2) return true;
            //     if ($select3.val() != initial.select3) return true;
            //
            //     return false;
            // }
            //
            // function toggleSubmitBtn() {
            //     $('#btnSubmitMemo').prop('disabled', !isFormChanged());
            // }
            //
            // $file.on('change', toggleSubmitBtn);
            // $textarea.on('input', toggleSubmitBtn);
            // $select1.on('change', toggleSubmitBtn);
            // $select2.on('change', toggleSubmitBtn);
            // $select3.on('change', toggleSubmitBtn);
            //
            // $('#btnSubmitMemo').prop('disabled', true);
            //
            // // listen event livewire change of select2
            // window.addEventListener('init-select2', function () {
            //     setTimeout(() => {
            //         $('#btnSubmitMemo').prop('disabled', !isFormChanged());
            //     }, 50);
            // });
        })
    </script>

    <script>
        window.addEventListener('init-select2', function () {
            setTimeout(() => {
                initSelect2Binding();
            }, 50);
        });

        function initSelect2Binding() {

            document.querySelectorAll('select.form-select2').forEach(function (el) {

                if (!$(el).hasClass('select2-hidden-accessible')) {
                    $(el).select2().on('change', function (e) {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');

                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                        }
                    });
                }
            });
        }

        document.addEventListener('livewire:initialized', () => {
            initSelect2Binding();
        });
    </script>

    <script>
        Livewire.on('showToastSuccess', message => {
            if (window.notyf) {
                window.notyf.success(Array.isArray(message) ? message[0] : message);
            }
        });

        Livewire.on('showToastError', message => {
            if (window.notyf) {
                window.notyf.error(Array.isArray(message) ? message[0] : message);
            }
        });
    </script>
@endpush
