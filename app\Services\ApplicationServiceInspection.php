<?php

namespace App\Services;

use App\Enums\ApplicationInspectionCancelStatusEnum as  AppInspecCancelEnum;
use App\Enums\ApplicationInspectionStatusEnum as AppInspecEnum;
use App\Enums\ApplicationInspectionToShopEnum;
use App\Enums\ApplicationStatusEnum;
use App\Enums\ContractStatusEnum;
use App\Enums\LoanScheduleTypeEnum;
use App\Enums\PaymentCompanyFlagEnum;
use App\Enums\PaymentStatusEnum;
use App\Enums\PaymentTypeEnum;
use App\Repositories\ApplicationInspectionRepository;
use App\Repositories\ApplicationRepository;
use App\Repositories\LoanScheduleRepository;
use App\Repositories\ShopBrandRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApplicationServiceInspection extends CustomService
{
    public function __construct(
        public ApplicationInspectionRepository $applicationInspectionRepository,
        public ApplicationRepository $applicationRepository,
        public ShopBrandRepository $shopBrandRepository
    ) {
        parent::__construct();
    }

    public function saveMemo($applicationId, $memoForm)
    {
        try {
            DB::beginTransaction();

            $application = $this->applicationRepository->findOrFail($applicationId);
            $formData = [];

            // Handle file uploads
            if (!empty($memoForm['files'])) {
                foreach ($memoForm['files'] as $file) {
                    $fileStorage = app(FileStorageService::class);
                    $fileName = $file->getClientOriginalName();
                    $filePath = app(FileStorageService::class)->storeFile($file, 'application_memos');
                    $url = $fileStorage->getFileUrl($filePath);

                    $formData['files'][] = [
                        'file_name' => $fileName,
                        'file_url' => $filePath,
                    ];
                }
            }

            // Update application with memo data
            $formData['to_shop_id'] = null;
            $formData['upd_date'] = now();
            $formData['upd_id'] = auth()->id();
            $formData['comment'] = $memoForm['comment'] ?? '';

            $lastApplicationInspection = $application?->applicationInspectionStatus?->last();
            // set init value to application inspection
            $formData['status'] = $lastApplicationInspection?->status?->value;
            $formData['cancel_status'] = $lastApplicationInspection?->cancel_status?->value;
            $formData['to_shop_id'] = $lastApplicationInspection?->to_shop_id;
            $formData['from_shop_id'] = $lastApplicationInspection?->from_shop_id;

            if (isset($memoForm['status'])) {
                $formData['status'] = $memoForm['status'] ?? null;
            }

            if (isset($memoForm['cancel_status'])) {
                $formData['cancel_status'] = $memoForm['cancel_status'] ?? null;
            }

            if (isset($memoForm['to_shop_id']) && $memoForm['to_shop_id'] == ApplicationInspectionToShopEnum::STORE) {
                $formData['to_shop_id'] = $application->shop_id;
                $formData['from_shop_id'] = $application->shop_id;
            }

            if (!empty($formData['files'])) {
                // mapping files upload & create many record
                foreach ($formData['files'] as $file) {
                    $formData['file_name'] = $file['file_name'];
                    $formData['file_url'] = $file['file_url'];
                    $application->applicationInspectionStatus()->create($formData);
                }
            } else {
                $application->applicationInspectionStatus()->create($formData);
            }

            // Update application status if necessary
            $application = $this->setApplicationStatus($application, $memoForm);

            $application->save();

            DB::commit();

            return $application;
        } catch (\Throwable $exception) {
            logError($exception->getMessage());
            DB::rollBack();
            return false;
        }
    }

    private function setApplicationStatus($application, $formData)
    {
        // action update to application table
        switch ($formData['status']) {
            // if application_inspection_status.status
            case AppInspecEnum::DOCUMENTS_SUBMITTED: // 3
            case AppInspecEnum::CONTACT_RESPONSE_RECEIVED: // 5
            case AppInspecEnum::IDENTITY_GUARANTOR_CONTACT_RESPONSE_RECEIVED: // 8
            case AppInspecEnum::IDENTITY_GUARANTOR_VERIFICATION_IN_PROGRESS: // 9
            case AppInspecEnum::IDENTITY_REDESIGNATED: // 11
            case AppInspecEnum::GUARANTOR_REDESIGNATED: // 13
                $application->status = ApplicationStatusEnum::UNDER_REVIEW;
                break;

            case AppInspecEnum::AWAITING_DOCUMENTS: // 2
                $application->status = ApplicationStatusEnum::DOCUMENT_CHECK_INCOMPLETE;
                break;

            case AppInspecEnum::AWAITING_CONTACT_RESPONSE: // 4
                $application->status = ApplicationStatusEnum::DOCUMENT_CHECK_OTHER;
                break;

            case AppInspecEnum::AWAITING_IDENTITY_REDESIGNATION: //10
                $application->status = ApplicationStatusEnum::IDENTITY_VERIFICATION_SCHEDULING;
                break;

            case AppInspecEnum::AWAITING_IDENTITY_CONTACT_RESPONSE: // 6
                $application->status = ApplicationStatusEnum::IDENTITY_VERIFICATION_OTHER;
                break;

            case AppInspecEnum::AWAITING_GUARANTOR_REDESIGNATION: // 12
                $application->status = ApplicationStatusEnum::GUARANTOR_VERIFICATION_SCHEDULING;
                break;

            case AppInspecEnum::AWAITING_GUARANTOR_CONTACT_RESPONSE: //7
                $application->status = ApplicationStatusEnum::GUARANTOR_VERIFICATION_OTHER;
                break;

            case AppInspecEnum::APPROVED: // 15
                $application->status = ApplicationStatusEnum::APPROVED;
                $maxContract = app()->make(ApplicationRepository::class)->getMaxContractId();
                $application->contract_id = $maxContract + 1;
                $application->contract_date = now();
                $application->payment_company_flag = PaymentCompanyFlagEnum::JACCS; // 1
                $application->regist_number = $this->getRegistNumber($application);
                $application->contract_status = ContractStatusEnum::UNDER_CONTRACT; // 1

                $loanSchedules = app()->make(LoanScheduleRepository::class)->getListByApplication($application->id);

                // logic update loan_schedule -> create records === applicatons.payment_count
                // mapping payment_count & increment payment_start_month -> end month
                if (!$loanSchedules->count() && $application->payment_count > 0 && $application->payment_start_month) {
                    $paymentStartMonth = Carbon::createFromFormat('Ym', $application->payment_start_month)->startOfMonth();
                    $paymentLastMonth = $paymentStartMonth->copy()->addMonths($application->payment_count - 1);
                    $months = $this->getMonthsBetween($paymentStartMonth, $paymentLastMonth);

                    $paymentDay = config('constant.LOAN_SCHEDULE_PAYMENT_DAY');
                    $firstPaymentAmount = $application->first_month_payment_amount;
                    $regularPaymentAmount = $application->second_month_payment_amount;

                    // has bonus
                    $bonusMonths = [];
                    if (!empty($application->bonus_payment_month1)) {
                        $bonusMonths[] = $application->bonus_payment_month1->value;
                    }
                    if (!empty($application->bonus_payment_month2)) {
                        $bonusMonths[] = $application->bonus_payment_month2->value;
                    }

                    $bonusAmount = !empty($application->bonus_payment_amount) && !empty($bonusMonths) ? $application->bonus_payment_amount : 0;

                    foreach ($months as $index => $month) {
                        $monthNum = (int)$month->format('n');
                        $paymentDate = $month->setDay($paymentDay)->format('Y-m-d');

                        $amount = $index === 0 ? $firstPaymentAmount : $regularPaymentAmount;
                        // if current month is in bonus months, set bonus amount
                        $bonus = (in_array($monthNum, $bonusMonths)) ? $bonusAmount : 0;
                        $totalAmount = $amount + $bonus;

                        $application->loanSchedules()->create([
                            'customer_id' => $application->customer_id,
                            'payment_plan_date' => $paymentDate,
                            'amount' => $amount,
                            'bonus_payment_amount' => $bonus,
                            'total_amount' => $totalAmount,
                            'amount_paid' => 0,
                            'type' => $index === 0 ? LoanScheduleTypeEnum::NEW : LoanScheduleTypeEnum::NOW,
                            'payment_type' => PaymentTypeEnum::DIRECT_DEBIT,
                            'payment_company_flag' => PaymentCompanyFlagEnum::JACCS,
                            'payment_status' => PaymentStatusEnum::WAITING_FOR_PAYMENT,
                        ]);
                    }
                }

                break;

            case AppInspecEnum::REJECTED: // 15
                $application->status = ApplicationStatusEnum::REJECTED;
                break;

            default:
                break;
        }

        // if application_inspection_status.cancel_status
        switch ($formData['cancel_status']) {
            case AppInspecCancelEnum::COOLING_OFF_REQUESTED: // 1
                $application->status = ApplicationStatusEnum::REJECTED;
                break;

            case AppInspecCancelEnum::COOLING_OFF_APPROVED: // 2
                $application->status = ApplicationStatusEnum::COOLING_OFF_APPROVED;
                break;

            case AppInspecCancelEnum::STORE_CANCELLATION_REQUESTED: // 3
                $application->status = ApplicationStatusEnum::STORE_CANCELLATION_REQUESTED;
                break;

            case AppInspecCancelEnum::STORE_CANCELLATION_APPROVED: // 4
                $application->status = ApplicationStatusEnum::STORE_CANCELLATION_APPROVED;

                break;

            default:
                break;
        }

        if (isset($formData['status']) && $formData['status'] != AppInspecEnum::BEFORE_REVIEW) { // 1
            $application->inspection_status = $formData['status'];
        }

        return $application;
    }

    private function getRegistNumber($application)
    {
        try {
            $firstNumber = config('constant.APPLICATION_REGIST_FIRST_NUMBER');
            $endNumber = config('constant.APPLICATION_REGIST_END_NUMBER');
            $yearPaymentStartMonth = $application->payment_start_month ? Carbon::createFromFormat('Ym', $application->payment_start_month)->format('Y') : '';
            $monthPaymentStartMonth = $application->payment_start_month ? Carbon::createFromFormat('Ym', $application->payment_start_month)->format('m') : '';
            $shopBrand = '';

            if ($application->shop_brand_id) {
                $shopBrand = $this->shopBrandRepository->find($application->shop_brand_id);

                // STRING cut format view_id of shopbrand from SB-1 => 001
                if ($shopBrand && $shopBrand->view_id) {
                    $viewId = $shopBrand->view_id;
                    $viewIdParts = explode('-', $viewId);

                    if (count($viewIdParts) > 1) {
                        $shopBrand = str_pad($viewIdParts[1], 3, '0', STR_PAD_LEFT);
                    } else {
                        $shopBrand = str_pad($viewId, 3, '0', STR_PAD_LEFT);
                    }
                }
            }

            $applicationCount = $this->applicationRepository->getCountAppByPaymentStartMonth($application->shop_brand_id, $application->payment_start_month);
            $applicationCount = str_pad($applicationCount + 1, 3, '0', STR_PAD_LEFT);
            return $firstNumber . $yearPaymentStartMonth . $monthPaymentStartMonth . $shopBrand . $applicationCount . $endNumber;
        } catch (\Throwable $exception) {
            logError($exception);
            return '';
        }
    }

    private function getMonthsBetween($start, $end)
    {
        $months = [];
        $current = $start->copy();

        while ($current->lte($end)) {
            $months[] = $current->copy();
            $current->addMonth();
        }

        return $months;
    }

    public function getApplicationInspectionStatuses($applicationId)
    {
        return $this->applicationInspectionRepository->getApplicationInspectionStatus($applicationId);
    }
}
