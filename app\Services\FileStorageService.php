<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class FileStorageService
{
    /**
     * Store a file and return its path
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param string|null $disk
     * @return string
     */
    public function storeFile(UploadedFile $file, string $directory = 'application_files', ?string $disk = null): string
    {
        $disk = $disk ?? config('filesystems.application_files_disk', 'public');
        $extension = $file->getClientOriginalExtension();
        $fileName = uniqid() . '.' . $extension;
        return $file->storeAs($directory, $fileName, $disk);
    }

    /**
     * Get the full URL for a file
     *
     * @param string $path
     * @param string|null $disk
     * @return string
     */
    public function getFileUrl(string $path, ?string $disk = null): string
    {
        $disk = $disk ?? config('filesystems.application_files_disk', 'public');
        return Storage::disk($disk)->url($path);
    }

    /**
     * Delete a file
     *
     * @param string $path
     * @param string|null $disk
     * @return bool
     */
    public function deleteFile(string $path, ?string $disk = null): bool
    {
        $disk = $disk ?? config('filesystems.application_files_disk', 'public');
        return Storage::disk($disk)->delete($path);
    }

    public static function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) { // >= 1MB (1024 * 1024 bytes)
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) { // >= 1KB (1024 bytes)
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
