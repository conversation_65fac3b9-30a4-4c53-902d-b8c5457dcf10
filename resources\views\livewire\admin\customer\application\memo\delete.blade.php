<div>
    <button type="button" class="small btn-text p-0 ms-2 fc-primary" data-bs-toggle="modal" data-bs-target="#confirmDeleteMemoModal-{{ $id }}">削除</button>
    <div class="modal fade deleteModal" id="confirmDeleteMemoModal-{{ $id }}" tabindex="-1">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <h5 class="modal-title">
                        <span class="title-icon">{{ trans2('confirm_modal.delete') }}</span>
                    </h5>
                    <p>{{ trans2('confirm_modal.alert') }}</p>
                    <form role="form">
                        <div class="d-flex justify-content-center pt-4 pb-4">
                            <div class="d-flex">
                                <button type="button" class="btn btn-border w-px-160 me-4" data-bs-dismiss="modal">{{ trans2('button.cancel') }}</button>
                                <button type="button" class="btn btn-danger w-px-160" data-bs-dismiss="modal" wire:click.prevent="delete">{{ trans2('button.delete_to') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
