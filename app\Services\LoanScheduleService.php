<?php

namespace App\Services;

use App\Enums\ApplicationStatusEnum;
use App\Enums\TypeEnum;
use App\Repositories\ApplicationCourseRepository;
use App\Repositories\LoanScheduleRepository;
use App\Repositories\ShopBrandRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class LoanScheduleService extends CustomService
{
    public function __construct(
        public LoanScheduleRepository $loanScheduleRepository
    ) {
        parent::__construct();
    }

    public function search($dataSearch, $perPage)
    {
        return $this->loanScheduleRepository->getListForSearch($dataSearch, $perPage);
    }

    public function getListByApplication($application_id)
    {
        return $this->loanScheduleRepository->getListByApplication($application_id);
    }

    public function getListForPaymentTable($dataSearch = [])
    {
        return $this->loanScheduleRepository->getListForPaymentTable($dataSearch);
    }
    public function getListForPaymentTableByApplication($application_id)
    {
        return $this->loanScheduleRepository->getListForPaymentTableByApplication($application_id);
    }

    public function getPaymentForCsv($dataSearch = [], $paymentStatus)
    {
        return $this->loanScheduleRepository->getPaymentForCsv($dataSearch, $paymentStatus);
    }

    public function getLoanScheduleById($id)
    {
        return $this->loanScheduleRepository->getLoanScheduleById($id);
    }
}
