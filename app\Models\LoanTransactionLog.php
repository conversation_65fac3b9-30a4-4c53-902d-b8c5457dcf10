<?php

namespace App\Models;

use App\Enums\LoanScheduleTypeEnum;
use App\Enums\LoanTransactionTypeEnum;
use App\Enums\PaymentTypeEnum;
use App\Models\Base\CustomModel;
use App\Models\Presenters\PLoanTransactionLog;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LoanTransactionLog extends CustomModel
{
    use HasFactory;
    use PLoanTransactionLog;

    /**
     * テーブル名
     */
    protected $table = 'loan_transaction_logs';

    /**
     * タイムスタンプ自動管理無効
     */
    public $timestamps = false;

    /**
     * ホワイトリスト
     */
    protected $fillable = [
        'id',
        'customer_id',
        'application_id',
        'type',
        'loan_schedule_id',
        'loan_payment_id',
        'loan_payment_allocation_id',
        'loan_overpayment_id',
        'loan_refund_id',
        'loan_arrear_id',
        'loan_withdrawal_id',
        'comment',
    ];

    /**
     * 関連定義
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function loanSchedule()
    {
        return $this->belongsTo(LoanSchedule::class);
    }

    public function loanPayment()
    {
        return $this->belongsTo(LoanPayment::class);
    }
    public function loanWithdrawal()
    {
        return $this->belongsTo(LoanWithdrawal::class);
    }

    public function loanPaymentAllocation()
    {
        return $this->belongsTo(LoanPaymentAllocation::class);
    }

    public function loanOverpayment()
    {
        return $this->belongsTo(LoanOverpayment::class);
    }

    public function loanRefund()
    {
        return $this->belongsTo(LoanRefund::class);
    }

    public function loanArrear()
    {
        return $this->belongsTo(LoanArrear::class);
    }

    protected $casts = [
        'type' => LoanTransactionTypeEnum::class,
        'payment_type' => PaymentTypeEnum::class,
    ];
}
