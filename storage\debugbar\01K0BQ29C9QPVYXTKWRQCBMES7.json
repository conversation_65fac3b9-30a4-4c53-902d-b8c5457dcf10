{"__meta": {"id": "01K0BQ29C9QPVYXTKWRQCBMES7", "datetime": "2025-07-17 17:12:27", "utime": **********.914535, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[17:12:27] LOG.debug: (Time: 73.11) SQL: select * from `administrators` where `email` = '<EMAIL>' and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.554671, "xdebug_link": null, "collector": "log"}, {"message": "[17:12:27] LOG.debug: (Time: 00.62) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.806245, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752739945.922054, "end": **********.914552, "duration": 1.9924979209899902, "duration_str": "1.99s", "measures": [{"label": "Booting", "start": 1752739945.922054, "relative_start": 0, "end": **********.14663, "relative_end": **********.14663, "duration": 0.*****************, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.146639, "relative_start": 0.*****************, "end": **********.914554, "relative_end": 2.1457672119140625e-06, "duration": 1.****************, "duration_str": "1.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.155136, "relative_start": 0.*****************, "end": **********.157536, "relative_end": **********.157536, "duration": 0.002399921417236328, "duration_str": "2.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.871311, "relative_start": 1.****************, "end": **********.912443, "relative_end": **********.912443, "duration": 0.*****************, "duration_str": "41.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "1x livewire.admin.auth.login", "param_count": null, "params": [], "start": **********.869222, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/auth/login.blade.phplivewire.admin.auth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.auth.login"}, {"name": "1x components.loading-overlay", "param_count": null, "params": [], "start": **********.869827, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/loading-overlay.blade.phpcomponents.loading-overlay", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Floading-overlay.blade.php&line=1", "ajax": false, "filename": "loading-overlay.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.loading-overlay"}, {"name": "1x components.elements.flash_messages", "param_count": null, "params": [], "start": **********.870267, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/elements/flash_messages.blade.phpcomponents.elements.flash_messages", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Felements%2Fflash_messages.blade.php&line=1", "ajax": false, "filename": "flash_messages.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.elements.flash_messages"}]}, "route": {"uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Auth\\Login@login<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Auth/Login.php:30-39</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07372999999999999, "accumulated_duration_str": "73.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `email` = '\\'<EMAIL>\\'' and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": ["'<EMAIL>'", "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Admin/Auth/Login.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Auth\\Login.php", "line": 56}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Auth/Login.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Auth\\Login.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.4818509, "duration": 0.07311, "duration_str": "73.11ms", "memory": 0, "memory_str": null, "filename": "Login.php:56", "source": {"index": 16, "namespace": null, "name": "app/Livewire/Admin/Auth/Login.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Auth\\Login.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=56", "ajax": false, "filename": "Login.php", "line": "56"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 99.159}, {"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "core/Common/Common.php", "file": "C:\\xampp\\htdocs\\ladybird\\core\\Common\\Common.php", "line": 588}, {"index": 21, "namespace": null, "name": "core/Database/Eloquent/Model/BaseModel.php", "file": "C:\\xampp\\htdocs\\ladybird\\core\\Database\\Eloquent\\Model\\BaseModel.php", "line": 153}], "start": **********.8058941, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 99.159, "width_percent": 0.841}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": {"admin.auth.login #u3NQxClqWJgs7cabtdoI": "array:4 [\n  \"data\" => array:5 [\n    \"guest\" => true\n    \"loginForm\" => App\\Livewire\\Admin\\Auth\\LoginForm {#762\n      #component: App\\Livewire\\Admin\\Auth\\Login {#732\n        #__id: \"u3NQxClqWJgs7cabtdoI\"\n        #__name: \"admin.auth.login\"\n        #listeners: []\n        #attributes: Livewire\\Features\\SupportAttributes\\AttributeCollection {#886\n          #items: array:4 [\n            0 => Livewire\\Attributes\\Layout {#758\n              #component: App\\Livewire\\Admin\\Auth\\Login {#732}\n              #subTarget: null\n              #subName: null\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#835\n                +name: \"ROOT\"\n              }\n              #levelName: null\n              +name: \"components.layouts.auth\"\n              +params: []\n            }\n            1 => Livewire\\Attributes\\Validate {#831\n              #component: App\\Livewire\\Admin\\Auth\\Login {#732}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#762}\n              #subName: \"email\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#829\n                +name: \"PROPERTY\"\n              }\n              #levelName: \"loginForm.email\"\n              +rule: \"required|check_email\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n            2 => Livewire\\Attributes\\Validate {#832\n              #component: App\\Livewire\\Admin\\Auth\\Login {#732}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#762}\n              #subName: \"password\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#829}\n              #levelName: \"loginForm.password\"\n              +rule: \"required\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n            3 => Livewire\\Attributes\\Validate {#830\n              #component: App\\Livewire\\Admin\\Auth\\Login {#732}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#762}\n              #subName: \"remember\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#829}\n              #levelName: \"loginForm.remember\"\n              +rule: \"nullable\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n        #withValidatorCallback: null\n        #rulesFromOutside: []\n        #messagesFromOutside: []\n        #validationAttributesFromOutside: []\n        +guest: true\n        #viewData: []\n        +page: \"\"\n        +pageTitle: \"ログイン\"\n        +redirecting: true\n        +loginForm: App\\Livewire\\Admin\\Auth\\LoginForm {#762}\n      }\n      #propertyName: \"loginForm\"\n      #withValidatorCallback: null\n      #rulesFromOutside: array:3 [\n        0 => array:1 [\n          \"email\" => \"required|check_email\"\n        ]\n        1 => array:1 [\n          \"password\" => \"required\"\n        ]\n        2 => array:1 [\n          \"remember\" => \"nullable\"\n        ]\n      ]\n      #messagesFromOutside: []\n      #validationAttributesFromOutside: []\n      +email: \"<EMAIL>\"\n      +password: \"admin123\"\n      +remember: null\n      +disableLogin: null\n    }\n    \"page\" => \"\"\n    \"pageTitle\" => \"ログイン\"\n    \"redirecting\" => true\n  ]\n  \"name\" => \"admin.auth.login\"\n  \"component\" => \"App\\Livewire\\Admin\\Auth\\Login\"\n  \"id\" => \"u3NQxClqWJgs7cabtdoI\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/livewire/update_1752739945", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Auth\\Login@login<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Auth/Login.php:30-39</a>", "middleware": "web", "duration": "1.99s", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1060854390 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1060854390\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1582668081 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S3Npaj4xoSHtyVYig42xftxE7lXFc46QHe5x7VXi</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"480 characters\">{&quot;data&quot;:{&quot;guest&quot;:true,&quot;loginForm&quot;:[{&quot;email&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;remember&quot;:false,&quot;disableLogin&quot;:false},{&quot;class&quot;:&quot;App\\\\Livewire\\\\Admin\\\\Auth\\\\LoginForm&quot;,&quot;s&quot;:&quot;form&quot;}],&quot;page&quot;:&quot;&quot;,&quot;pageTitle&quot;:&quot;\\u30ed\\u30b0\\u30a4\\u30f3&quot;,&quot;redirecting&quot;:false},&quot;memo&quot;:{&quot;id&quot;:&quot;u3NQxClqWJgs7cabtdoI&quot;,&quot;name&quot;:&quot;admin.auth.login&quot;,&quot;path&quot;:&quot;management\\/login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;115b26bd96f76b856dfaf3da9eb7e9df9507affdf5a55fcf9a44b69fa0adeb7f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>loginForm.email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>loginForm.password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">admin123</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"5 characters\">login</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582668081\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1703268468 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">778</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/management/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1182 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImltZnBOVG05bEQvdTBJeVBaYlhDQkE9PSIsInZhbHVlIjoiaXFWUkZ0dmtUWlB1NUtmTDZ5bWdLRGRyTFlRaHhTQVBXM2tEWTR0RWZnbERHU3FRdzQ0elE3SWRHUlo4NDE2ZXJaOFFJVGprb0RDZlV6YlFOR1U1WEF0L2pFQXpiV09vQ0NPdkV3MGNpcDRSdnd5QjNPeGkvRDdWVHRNOXUzVE5TT2NEZ09IUnVnVVlnbmxBcU91c3JnPT0iLCJtYWMiOiJmMGU1YTZhNjA1YjYwMGQ5ODJhYTIxOWY1ZDllNDdjMDc1OWNmZDAyODM2NjU1NTJmMmU1ZTA1NTc0MTg0ZTkwIiwidGFnIjoiIn0%3D; cookie_per_page=eyJpdiI6IkV4b3ZySWVaTjI0czgxRmpycEc3MHc9PSIsInZhbHVlIjoiQUk4ak14MytpVTdFUjBubkplSWZJK25Rc0FWbXV5K2pabU5nU0tuVUNycWx3UXpoZjNqdW9NNWlzcmJ3bmsvOCIsIm1hYyI6IjI5ZjgzNWFiMDIxNTg3MDc3MGU4YzFlNDA3OGVjNDg4N2ExODA5MmU2NDJjMzMyMzdmMWMxZDVkY2RjMDA1ZDMiLCJ0YWciOiIifQ%3D%3D; leasemart_session=w8w1HYeAl52hZGLblPhiSoOKeze2Xr5qGWmGpJqZ; ladybird_session=xP91ro83Xu14zfLBCjspYKCFvlwkzWRJlUTnE9HR; XSRF-TOKEN=eyJpdiI6IkdEaEFmakRlaW9BRFEvOG5GVW5oY2c9PSIsInZhbHVlIjoidWxaOTh6Y0FIS3pTcW55d1Mwa0N2VXN5U1BtTHZXTzQ0RFhHSjRwL3lqQkhrQzJmRThSN3FhSzBqSkJETE8vVFVWOGZLUlF4Y2hZbm5XRkhYaXg1VjVISnFyRFo1SURJNDJFSFB4MEd4VW5tM3NuK2pEQmx3RXNoSVpuRFplSEsiLCJtYWMiOiI1MzBiZTdkMmU3NzU5NzU1MTQxYWM2ZThlMGZjNzc1ODgzNDViMWExZTYzN2ZkOGNhMjE1YzlmMjE4OWZjY2U4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703268468\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-504557826 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>leasemart_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S3Npaj4xoSHtyVYig42xftxE7lXFc46QHe5x7VXi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504557826\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1545563479 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 08:12:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545563479\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1731566552 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_1752739945</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731566552\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}