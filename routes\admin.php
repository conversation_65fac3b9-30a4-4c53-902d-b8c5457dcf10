<?php

use App\Http\Controllers\Admin\ApplicationInspectionStatusController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Livewire\Volt\Volt;

Route::group(['as' => getConfig('routes.admin.as'), 'middleware' => 'locale'], function () {

    // guest
    Route::middleware('guest')->group(function () {
        Volt::route('login', \App\Livewire\Admin\Auth\Login::class)->name('login');

        Volt::route('login/code', \App\Livewire\Admin\Auth\Code\Index::class)->name('login.code');
        Volt::route('login/send', \App\Livewire\Admin\Auth\Send\Index::class)->name('login.send');
        Volt::route('login/reset/{token}', \App\Livewire\Admin\Auth\Reset\Index::class)->name('login.reset');
    });

    // authenticated
    Route::group(['middleware' => 'auth:admin'], function () {
        // logout
        Route::post('logout', function () {
            getGuard()->logout();
            Session::invalidate();
            Session::regenerateToken();
            return redirect(getRoute('login'));
        })->name('logout');

        //change language
        Route::get('change-language/{language}', function () {
            session()->put(getLocaleKey(), request('language'));
            return redirect()->back();
        })->name('change_language');

        // dashboard - home
        Volt::route('/', \App\Livewire\Admin\Dashboard\Index::class)->name('home');
        Volt::route('dashboard', \App\Livewire\Admin\Dashboard\Index::class)->name('dashboard.index');
        Volt::route('balances', \App\Livewire\Admin\Balance\Index::class)->name('balance.index');

        //masters
        Route::prefix('/master')->group(function () {
            //item_types
            Route::prefix('/item_types')->group(function () {
                Volt::route('/', \App\Livewire\Admin\ItemType\Index::class)->name('item_type.index');
                Volt::route('/details/{id}', \App\Livewire\Admin\ItemType\Show::class)->name('item_type.details');
                Volt::route('/edit/{id}', \App\Livewire\Admin\ItemType\Update::class)->name('item_type.edit')->middleware("can:allowed-update,'" . \App\Enums\SidebarMenuEnum::ITEM_TYPE . "'");
                Volt::route('/new', \App\Livewire\Admin\ItemType\Create::class)->name('item_type.new')->middleware("can:allowed-create,'" . \App\Enums\SidebarMenuEnum::ITEM_TYPE . "'");
            });
            //brands
            Route::prefix('/brands')->group(function () {
                Volt::route('/', \App\Livewire\Admin\Brand\Index::class)->name('brand.index');
                Volt::route('/details/{id}', \App\Livewire\Admin\Brand\Show::class)->name('brand.details');
                Volt::route('/edit/{id}', \App\Livewire\Admin\Brand\Update::class)->name('brand.edit')->middleware("can:allowed-update,'" . \App\Enums\SidebarMenuEnum::BRAND . "'");
                Volt::route('/new', \App\Livewire\Admin\Brand\Create::class)->name('brand.new')->middleware("can:allowed-create,'" . \App\Enums\SidebarMenuEnum::BRAND . "'");
            });
            //shops
            Route::prefix('/shops')->group(function () {
                Volt::route('/', \App\Livewire\Admin\Shop\Index::class)->name('shop.index');
                Volt::route('/details/{id}', \App\Livewire\Admin\Shop\Show::class)->name('shop.details');
                Volt::route('/edit/{id}', \App\Livewire\Admin\Shop\Update::class)->name('shop.edit')->middleware("can:allowed-update,'" . \App\Enums\SidebarMenuEnum::SHOP . "'");
                Volt::route('/new', \App\Livewire\Admin\Shop\Create::class)->name('shop.new')->middleware("can:allowed-create,'" . \App\Enums\SidebarMenuEnum::SHOP . "'");
            });
            //shop_brands
            Route::prefix('/shop_brands')->group(function () {
                Volt::route('/', \App\Livewire\Admin\ShopBrand\Index::class)->name('shop_brand.index');
                Volt::route('/details/{id}', \App\Livewire\Admin\ShopBrand\Show::class)->name('shop_brand.details');
                Volt::route('/edit/{id}', \App\Livewire\Admin\ShopBrand\Update::class)->name('shop_brand.edit')->middleware("can:allowed-update,'" . \App\Enums\SidebarMenuEnum::SHOP_BRAND . "'");
                Volt::route('/new', \App\Livewire\Admin\ShopBrand\Create::class)->name('shop_brand.new')->middleware("can:allowed-create,'" . \App\Enums\SidebarMenuEnum::SHOP_BRAND . "'");
            });
            //courses
            Route::prefix('/courses')->group(function () {
                Volt::route('/', \App\Livewire\Admin\Course\Index::class)->name('course.index');
                Volt::route('/details/{id}', \App\Livewire\Admin\Course\Show::class)->name('course.details');
                Volt::route('/edit/{id}', \App\Livewire\Admin\Course\Update::class)->name('course.edit')->middleware("can:allowed-update,'" . \App\Enums\SidebarMenuEnum::COURSE . "'");
                Volt::route('/new', \App\Livewire\Admin\Course\Create::class)->name('course.new')->middleware("can:allowed-create,'" . \App\Enums\SidebarMenuEnum::COURSE . "'");
            });
            //holidays
            Route::prefix('/holidays')->group(function () {
                Volt::route('/', \App\Livewire\Admin\Holiday\Index::class)->name('holiday.index');
            });

            Volt::route('/', \App\Livewire\Admin\Course\Index::class)->name('master');
        });

        Route::prefix('/users')->group(function () {
            Volt::route('/', \App\Livewire\Admin\Administrator\Index::class)->name('administrator.index');
            Volt::route('/details/{id}', \App\Livewire\Admin\Administrator\Profile::class)->name('administrator.details');
            Volt::route('/edit/{id}', \App\Livewire\Admin\Administrator\Update::class)->name('administrator.edit')->middleware("can:allowed-update,'" . \App\Enums\SidebarMenuEnum::ADMINISTRATOR . "'");
            Volt::route('/new', \App\Livewire\Admin\Administrator\Create::class)->name('administrator.new')->middleware("can:allowed-create,'" . \App\Enums\SidebarMenuEnum::ADMINISTRATOR . "'");
        });

        Route::group([
            'prefix' => 'customers',
            'as' => 'customer.',
        ], function () {
            Volt::route('/', \App\Livewire\Admin\Customer\Index::class)->name('index');
            Volt::route('/details/{id}', \App\Livewire\Admin\Customer\Show::class)->name('details');
            Volt::route('/edit/{id}', \App\Livewire\Admin\Customer\Update::class)->name('edit');
            Volt::route('/new', \App\Livewire\Admin\Customer\Create::class)->name('new')->middleware("can:allowed-create,'" . \App\Enums\SidebarMenuEnum::CUSTOMER . "'");

            // tab application
            Volt::route('/{customer_id}/application', \App\Livewire\Admin\Customer\Application\Index::class)->name('application.index');
            Volt::route('/{customer_id}/application/{application_id}/detail', \App\Livewire\Admin\Customer\Application\Detail\Index::class)->name('application.detail.index');
            Volt::route('/{customer_id}/application/{application_id}/memo', \App\Livewire\Admin\Customer\Application\Memo\Index::class)->name('application.memo.index');
            // tab contract
            Volt::route('/{customer_id}/contract', \App\Livewire\Admin\Customer\Contract\Index::class)->name('contract.index');

            // application management
            Route::group([
                'prefix' => '{customer_id}/application',
                'as' => 'application.',
                'middleware' => 'check.access.application'
            ], function () {
                Volt::route('/brand', \App\Livewire\Admin\Application\Brand::class)->name('brand');
                // tab customer search
            });
        });

        Route::group([
            'prefix' => 'application',
            'as' => 'customer.application.',
            'middleware' => 'check.access.application'
        ], function () {
            Volt::route('/{application_id}/brand', \App\Livewire\Admin\Application\Brand::class)->name('brand.edit');
            Volt::route('/{application_id}/payment', \App\Livewire\Admin\Application\Payment::class)->name('payment');
            Volt::route('/{application_id}/service', \App\Livewire\Admin\Application\Service::class)->name('service');
            Volt::route('/{application_id}/identification', \App\Livewire\Admin\Application\Identification\Index::class)->name('identification');
            Volt::route('/{application_id}/customer', \App\Livewire\Admin\Application\Customer::class)->name('customer');
            Volt::route('/{application_id}/check', \App\Livewire\Admin\Application\Check::class)->name('check');
            Volt::route('/{application_id}/complete', \App\Livewire\Admin\Application\Completed::class)->name('completed');
            Route::prefix('/{application_id}/setup')->group(function () {
                Volt::route('/', \App\Livewire\Admin\Application\CustomerSetup::class)->name('setup');
                Volt::route('{customer_id}/emergency', \App\Livewire\Admin\Application\Customer\Emergency::class)->name('setup.emergency');
                Volt::route('{customer_id}/work', \App\Livewire\Admin\Application\Customer\Work::class)->name('setup.work');
                Volt::route('{customer_id}/guarantor', \App\Livewire\Admin\Application\Customer\Guarantor::class)->name('setup.guarantor');
                Volt::route('{customer_id}/account', \App\Livewire\Admin\Application\Customer\Account::class)->name('setup.account');
                Volt::route('{customer_id}/contact', \App\Livewire\Admin\Application\Customer\Contact::class)->name('setup.contact');
            });

            // print pdf
            Route::get('{application_id}/export-pdf', [\App\Http\Controllers\Admin\ApplicationExportPDFController::class, 'export'])->name('export-pdf');
        });

        Route::group([
            'prefix' => 'contract',
            'as' => 'contract.',
        ], function () {
            Volt::route('/', \App\Livewire\Admin\Contract\Index::class)->name('index');
            Volt::route('/details/{id}', \App\Livewire\Admin\Contract\Show::class)->name('details');
            Volt::route('/edit/{id}', \App\Livewire\Admin\Contract\Update::class)->name('edit');

            // tab deposit
            Volt::route('/{contract_id}/deposit', \App\Livewire\Admin\Contract\ApplicationPayment\Index::class)->name('deposit.index');
        });

        Route::group([
            'prefix' => 'payments',
            'as' => 'payment.',
        ], function () {
            Volt::route('/', \App\Livewire\Admin\Payment\Index::class)->name('index');
            Volt::route('/details/{id}', \App\Livewire\Admin\Payment\Show::class)->name('details');
            Volt::route('/registration/{id}/{status}', \App\Livewire\Admin\Payment\Registration::class)->name('details.registration');

            // tab deposit list
            Volt::route('/{payment_id}/deposit-list', \App\Livewire\Admin\Payment\DepositList\Index::class)->name('deposit-list.index');
        });
    });

    Route::get('/download-file/{application_id}', [ApplicationInspectionStatusController::class, 'downloadFile'])->name('download.application_file');
});
