<?php

namespace App\Livewire\Admin\Customer\Application\Memo;

use App\Livewire\Base\BaseAdminPageComponent;
use Illuminate\Support\Facades\DB;
use App\Repositories\ApplicationInspectionRepository;
use App\Services\FileStorageService;
use App\Services\ToastService;

class Delete extends BaseAdminPageComponent
{
    public $id;

    public function mount($id)
    {
        $this->id = $id;
    }

    public function delete()
    {
        try {
            $file = app(ApplicationInspectionRepository::class)->findOrFail($this->id);
            if (empty($file)) {
                app(ToastService::class)->error(__('messages.no_data'));
                return;
            }
            DB::beginTransaction();
            app(FileStorageService::class)->deleteFile($file->file_url);

            $file->file_name = null;
            $file->file_url = null;
            $file->save();

            DB::commit();

            $this->dispatch('reload-memo-list');
            $this->dispatch('showToastSuccess', __('messages.delete_success'));
        } catch (\Throwable $th) {
            DB::rollBack();
            logError($th);
            $this->dispatch('showToastError', __('messages.delete_failed'));
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.application.memo.delete');
    }
}

