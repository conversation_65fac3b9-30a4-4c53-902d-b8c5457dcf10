<?php

namespace App\Livewire\Admin\Dashboard;

use App\Enums\ApplicationInspectionStatusEnum;
use App\Enums\ApplicationStatusEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Livewire\Base\BaseDataListComponent;
use App\Models\ApplicationInspectionStatus;
use App\Services\DashboardService;
use Livewire\Attributes\Reactive;

class ReviewTable extends BaseDataListComponent
{
    #[Reactive]
    public $shopId;

    #[Reactive]
    public $shopBrandId;

    protected $listeners = ['refresh-page' => '$refresh'];


    public function render()
    {
        $params = [
            'shopBrandId' => $this->shopBrandId,
            'shopId' => $this->shopId
        ];
        $application_in_progress_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::IN_PROGRESS, $params);
        $application_before_review_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::BEFORE_REVIEW, $params);
        $application_under_review_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::UNDER_REVIEW, $params);
        $application_incomplete_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::DOCUMENT_CHECK_INCOMPLETE, $params);
        $application_doc_check_other_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::DOCUMENT_CHECK_OTHER, $params);
        $application_identity_verification_scheduling_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::IDENTITY_VERIFICATION_SCHEDULING, $params);
        $application_identity_verification_other_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::IDENTITY_VERIFICATION_OTHER, $params);
        $application_guarantor_verification_scheduling_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::GUARANTOR_VERIFICATION_SCHEDULING, $params);
        $application_guarantor_verification_other_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::GUARANTOR_VERIFICATION_OTHER, $params);
        $application_approved_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::APPROVED, $params);
        $application_rejected_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::REJECTED, $params);
        $application_cooling_off_request_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::COOLING_OFF_REQUESTED, $params);
        $application_cooling_off_approved_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::COOLING_OFF_APPROVED, $params);
        $application_store_cancel_request_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::STORE_CANCELLATION_REQUESTED, $params);
        $application_store_cancel_approved_count = app(DashboardService::class)->getApplicationCount(ApplicationStatusEnum::STORE_CANCELLATION_APPROVED, $params);

        $app_inspect_status1_count = app(DashboardService::class)->getApplicationInspectionStatusCount(ApplicationInspectionStatusEnum::values1());
        $app_inspect_status2_count = app(DashboardService::class)->getApplicationInspectionStatusCount(ApplicationInspectionStatusEnum::values2());
        $app_inspect_status3_count = app(DashboardService::class)->getApplicationInspectionStatusCount(ApplicationInspectionStatusEnum::values3());
        $app_inspect_status_count_all = app(DashboardService::class)->getApplicationInspectionStatusCount(ApplicationInspectionStatusEnum::getValues());
        return $this->viewLivewireAdmin('dashboard.review-table', [
            'application_in_progress_count' => $application_in_progress_count,
            'application_before_review_count' => $application_before_review_count,
            'application_under_review_count' => $application_under_review_count,
            'application_incomplete_count' => $application_incomplete_count,
            'application_doc_check_other_count' => $application_doc_check_other_count,
            'application_identity_verification_scheduling_count' => $application_identity_verification_scheduling_count,
            'application_identity_verification_other_count' => $application_identity_verification_other_count,
            'application_guarantor_verification_scheduling_count' => $application_guarantor_verification_scheduling_count,
            'application_guarantor_verification_other_count' => $application_guarantor_verification_other_count,
            'application_approved_count' => $application_approved_count,
            'application_rejected_count' => $application_rejected_count,
            'application_cooling_off_request_count' => $application_cooling_off_request_count,
            'application_cooling_off_approved_count' => $application_cooling_off_approved_count,
            'application_store_cancel_request_count' => $application_store_cancel_request_count,
            'application_store_cancel_approved_count' => $application_store_cancel_approved_count,

            'app_inspect_status1_count' => $app_inspect_status1_count,
            'app_inspect_status2_count' => $app_inspect_status2_count,
            'app_inspect_status3_count' => $app_inspect_status3_count,
            'app_inspect_status_count_all' => $app_inspect_status_count_all,
        ]);
    }

}
