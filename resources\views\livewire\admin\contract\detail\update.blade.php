@extends('livewire.admin.contract.detail.index')
@section('detail-tab')
    @php
        $func = \App\Enums\FunctionEnum::CUSTOMER;
        $informationInputFlag = \App\Enums\InformationInputFlagEnum::dropdown();
        $bankFlag = \App\Enums\BankFlagEnum::dropdown();
        $bankAccountType = \App\Enums\BankAccountTypeEnum::dropdown();
        $paymentCompanyFlag = \App\Enums\PaymentCompanyFlagEnum::dropdown();
    @endphp
    <div class="card mb-3">
        <div class="card-body">
            <div class="container-min">
                <div id="selected-date"
                    data-selected-dates='@json($selectedHoliday)'>
                </div>
                <form role="form">
                    <div class="mb-4 d-flex justify-content-end">
                        <button wire:click.prevent="validateUpdate"
                            class="btn btn-dark btn-medium">{{ trans2('button.update') }}</button>
                    </div>
                    <table class="table table-edit">
                        <tbody>
                            <tr>
                                <th class="text-nowrap">{{ transm('_default.attributes.id') }}</th>
                                <td class="text-break">{{ data_get($contract, 'contract_id') }}</td>
                            </tr>
                            <tr>
                                <th class="text-nowrap">{{ transm('contract.attributes.full_name') }}</th>
                                <td class="text-break"><a href="{{ getRoute('customer.details',['id' => data_get($contract, 'customer.id') ]) }}">{{ data_get($contract, 'customer.full_name') }}</a></td>
                            </tr>
                            <tr>
                                <th class="text-nowrap">{{ transm('contract.attributes.full_name_kana') }}</th>
                                <td class="text-break">{{ data_get($contract, 'customer.full_name_kana') }}</td>
                            </tr>
                            <tr>
                                <th class="text-nowrap">{{ transm('contract.attributes.contract_company') }}</th>
                                <td class="text-break">{{ trans2('company') }}</td>
                            </tr>
                            <tr>
                                <th class="text-nowrap required">{{ transm('contract.attributes.shop_brand_id') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break">
                                    <div wire:ignore>
                                        <select name="" class="form-select2 form-required" data-placeholder="{{ trans2('select_default') }}" wire:model="updateForm.shop_brand_id"
                                            style="width:567px;">
                                            <option value=""></option>
                                            @foreach ($shopBrands as $shopBrand)
                                                <option value="{{ data_get($shopBrand, 'id' ) }}">{{ data_get($shopBrand, 'name' ) }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div>
                                        @error('updateForm.shop_brand_id') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap required">{{ transm('contract.attributes.item_type_name') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break">
                                    @foreach ($contract->courses as $course)
                                        <div>
                                            {{ data_get($course, 'itemType.name') }}
                                        </div>
                                    @endforeach
                                </td>
                            </tr>
                            <tr>
                                <th class="required">
                                    {{ trans2('screens.application.brand.general_product') }}<span class="required-icon">{{ trans2('required') }}</span>
                                </th>
                                <td>
                                    <div class="form-parts-group" id="brandItemsGroup">
                                        @foreach($productGeneral as $index => $item)
                                            <div class="form-parts d-flex pt-2 pb-2 align-items-end" data-parts-id="{{ $index }}">
                                                <div class="row align-items-start flex-grow-1">
                                                    <div class="col-auto flex-grow-1">
                                                        <div
                                                            class="text-center fw-medium fs-13 mb-2">{{ trans2('screens.application.brand.product_name') }}
                                                        </div>
                                                        <select wire:model="updateForm.product_general.{{ $index }}.course_id"
                                                                class="form-select2 flex-grow-1 course-general-selector" style="width:262px;"
                                                                data-placeholder="{{ trans2('select_default') }}">
                                                            <option value="" selected></option>
                                                            @foreach($courseGeneralList as $course)
                                                                <option value="{{ $course['id'] }}" data-unit-price="{{ $course['unit_price'] }}">{{ $course['name_application'] ?: $course['name_management'] }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="col-auto">
                                                        <div class="text-center fw-medium fs-13 mb-2">{{ trans2('screens.application.brand.quantity') }}</div>
                                                        <div class="js-qty">
                                                            <button type="button" class="js-qty__adjust js-qty__adjust--minus"
                                                                    wire:click="decrementQtyGeneral({{ $index }})">
                                                                <span aria-hidden="true">−</span>
                                                                <span
                                                                    class="u-hidden-visually">{{ trans2('screens.application.brand.sub_one') }}</span>
                                                            </button>
                                                            <input type="number" class="js-qty__num form-control"
                                                                wire:model="updateForm.product_general.{{ $index }}.count" min="0"
                                                                aria-label="count" pattern="[0-9]*" name="" step="1">
                                                            <button type="button" class="js-qty__adjust js-qty__adjust--plus"
                                                                    wire:click="incrementQtyGeneral({{ $index }})">
                                                                <span aria-hidden="true">+</span>
                                                                <span
                                                                    class="u-hidden-visually">{{ trans2('screens.application.brand.plus_one') }}</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="col-auto">
                                                        <div class="text-center fw-medium fs-13 mb-2">{{ trans2('screens.application.brand.amount') }}</div>
                                                            <div class="form-parts-unit">
                                                            <div class="form-unit">&yen;</div>
                                                            <input type="text" name="" wire:model="updateForm.product_general.{{ $index }}.amount"
                                                                class="form-control w-px-120" pattern="[0-9]*" readonly>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="ms-4">
                                                    @if ($loop->last)
                                                        <button type="button" class="btn btn-add add-form-parts" wire:click.prevent="addGeneral()"></button>
                                                    @else
                                                        <button type="button" class="btn btn-add form-delete-button delete-form-parts" wire:click.prevent="removeGeneral({{$index}})"></button>
                                                    @endif
                                                </div>
                                            </div>
                                            @error("updateForm.product_general.$index.course_id")
                                                <div>
                                                    <span class="message-error">{{ $message }}</span>
                                                </div>
                                            @enderror
                                            @error("updateForm.product_general.$index.count")
                                                <div>
                                                    <span class="message-error">{{ $message }}</span>
                                                </div>
                                            @enderror
                                            @error("updateForm.product_general.$index.amount")
                                            <div>
                                                <span class="message-error">{{ $message }}</span>
                                            </div>
                                            @enderror
                                        @endforeach
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="">{{ trans2('screens.application.brand.optional_product') }}</th>
                                <td>
                                    <div class="form-parts-group" id="brandOptionGroup">
                                        @foreach($productOptional as $index => $item)
                                            <div class="form-parts d-flex pt-2 pb-2 align-items-end" data-parts-id="101">
                                                <div class="row align-items-start flex-grow-1">
                                                    <div class="col-auto flex-grow-1">
                                                        <div
                                                            class="text-center fw-medium fs-13 mb-2">{{ trans2('screens.application.brand.product_name') }}</div>
                                                        <select wire:model="updateForm.product_optional.{{ $index }}.course_id"
                                                                class="form-select2 flex-grow-1 course-optional-selector" style="width:262px;"
                                                                data-placeholder="{{ trans2('select_default') }}">
                                                            <option value="" selected></option>
                                                            @foreach($courseOptionalList as $course)
                                                                <option value="{{ $course['id'] }}" data-unit-price="{{ $course['unit_price'] }}">{{ $course['name_application'] ?: $course['name_management'] }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="col-auto">
                                                        <div
                                                            class="text-center fw-medium fs-13 mb-2">{{ trans2('screens.application.brand.quantity') }}</div>
                                                        <div class="js-qty">
                                                            <button type="button" class="js-qty__adjust js-qty__adjust--minus"
                                                                    wire:click="decrementQtyOptional({{ $index }})">
                                                                <span aria-hidden="true">−</span>
                                                                <span
                                                                    class="u-hidden-visually">{{ trans2('screens.application.brand.sub_one') }}</span>
                                                            </button>
                                                            <input type="number" class="js-qty__num form-control"
                                                                wire:model="updateForm.product_optional.{{ $index }}.count" min="0"
                                                                aria-label="count" pattern="[0-9]*" name="" step="1">
                                                            <button type="button" class="js-qty__adjust js-qty__adjust--plus"
                                                                    wire:click="incrementQtyOptional({{ $index }})">
                                                                <span aria-hidden="true">+</span>
                                                                <span
                                                                    class="u-hidden-visually">{{ trans2('screens.application.brand.plus_one') }}</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="col-auto">
                                                        <div
                                                            class="text-center fw-medium fs-13 mb-2">{{ trans2('screens.application.brand.amount') }}</div>
                                                        <div class="form-parts-unit">
                                                            <div class="form-unit">&yen;</div>
                                                            <input type="text" name="" wire:model="updateForm.product_optional.{{ $index }}.amount"
                                                                class="form-control w-px-120" pattern="[0-9]*">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="ms-4">
                                                    @if ($loop->last)
                                                        <button type="button" class="btn btn-add add-form-parts" wire:click.prevent="addOptional()"></button>
                                                    @else
                                                        <button type="button" class="btn btn-add form-delete-button delete-form-parts" wire:click.prevent="removeOptional({{$index}})"></button>
                                                    @endif
                                                </div>
                                            </div>
                                            @error("updateForm.product_optional.$index.course_id")
                                                <div>
                                                    <span class="message-error">{{ $message }}</span>
                                                </div>
                                            @enderror
                                            @error("updateForm.product_optional.$index.count")
                                                <div>
                                                    <span class="message-error">{{ $message }}</span>
                                                </div>
                                            @enderror
                                            @error("updateForm.product_optional.$index.amount")
                                            <div>
                                                <span class="message-error">{{ $message }}</span>
                                            </div>
                                            @enderror
                                        @endforeach
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap required">{{ transm('contract.attributes.information_input_flag') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break">
                                    <div class="d-flex">
                                        @foreach ($informationInputFlag as $key => $text)
                                            <div class="form-radio me-5">
                                                <label>
                                                    <input type="radio" name="guarantor" value="{{ $key }}" wire:model="updateForm.information_input_flag"
                                                        class="form-radio-input">
                                                    <span class="form-radio-text">{{ $text }}</span>
                                                </label>
                                            </div>
                                        @endforeach
                                    </div>
                                    <div>
                                        @error('updateForm.information_input_flag') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap">{{ transm('contract.attributes.staff_name') }}</th>
                                <td class="text-break">
                                    <input type="text" name="" value="" class="form-control" wire:model="updateForm.staff_name">
                                    <div>
                                        @error('updateForm.staff_name') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap">{{ transm('contract.attributes.payment_company_flag') }}</th>
                                <td class="text-break">
                                    <div class="d-flex">
                                        <div wire:ignore>
                                            <select name="" class="form-select2 form-required" data-placeholder="{{ trans2('select_default') }}" wire:model="updateForm.payment_company_flag"
                                                style="width:567px;">
                                                <option value=""></option>
                                                @foreach ($paymentCompanyFlag as $key => $text)
                                                    <option value="{{ $key }}">{{ $text }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div>
                                        @error('updateForm.payment_company_flag') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.contract_status') }}</h2>
                    <table class="table table-edit">
                        <tbody>
                            <tr>
                                <th class="text-nowrap">{{ transm('contract.attributes.contract_status') }}</th>
                                <td class="text-break">{{ data_get($contract, 'contract_status.text') }}</td>
                            </tr>
                            <tr>
                                <th class="text-nowrap required">{{ transm('contract.attributes.application_date') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break">
                                    <input type="text" name="" value="" wire:model="updateForm.application_date"
                                        class="{{ empty($updateForm->application_date) ? 'form-required' : '' }} form-control datepicker"
                                        data-date-format="yy/mm/dd"
                                        x-init="setTimeout(() => initializeDatepickers(), 100)">
                                    <div>
                                        @error('updateForm.application_date') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap required">{{ transm('contract.attributes.contract_date') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break">
                                    <input type="text" name="" value="" wire:model="updateForm.contract_date"
                                        class="{{ empty($updateForm->contract_date) ? 'form-required' : '' }} form-control datepicker"
                                        data-date-format="yy/mm/dd"
                                        x-init="setTimeout(() => initializeDatepickers(), 100)">
                                    <div>
                                        @error('updateForm.contract_date') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap">{{ transm('contract.attributes.contract_cancel_date') }}</th>
                                <td class="text-break">
                                    <input type="text" name="" value="" class="form-control datepicker"
                                        wire:model="updateForm.contract_cancel_date"
                                        x-init="setTimeout(() => initializeDatepickers(), 100)"
                                        data-date-format="yy/mm/dd">
                                    <div>
                                        @error('updateForm.contract_cancel_date') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap">{{ transm('contract.attributes.contract_cancel_amount') }}</th>
                                <td class="text-break">
                                    <input type="text" name="" value="" class="form-control w-px-240" wire:model="updateForm.contract_cancel_amount">
                                    <div>
                                        @error('updateForm.contract_cancel_amount') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="d-flex flex-wrap">
                        <div class="form-check me-5">
                            <label>
                                <input type="checkbox" name="" class="form-check-input" wire:model="updateForm.isCheckContractStatus5">
                                <span class="form-check-text">{{ trans2('screens.contract.contract_tab.contract_status_5') }}</span>
                            </label>
                        </div>
                        <div class="form-check">
                            <label>
                                <input type="checkbox" name="" class="form-check-input" wire:model="updateForm.isCheckReInvoiceDocTargetFlag1">
                                <span class="form-check-text">{{ trans2('screens.contract.contract_tab.re_invoice_1') }}</span>
                            </label>
                        </div>
                    </div>
                    <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.payment_information') }}</h2>
                    <h3 class="heading-3 mt-0">{{ trans2('screens.contract.contract_tab.loan_contact_details') }}</h3>
                    <div class="row">
                        <div class="col-6">
                            <table class="table table-details mb-4">
                                <tbody>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.total_amount') }}</th>
                                        <td class="text-break"><span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'total_amount')) }}</td>
                                    </tr>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.principal_including_tax') }}</th>
                                        <td class="text-break"><span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'principal_including_tax')) }}</td>
                                    </tr>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.fee_amount') }}</th>
                                        <td class="text-break"><span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'fee_amount')) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-6">
                            <table class="table table-details mb-4">
                                <tbody>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.payment_count') }}</th>
                                        <td class="text-break">{{ data_get($contract, 'payment_count') }}{{ trans2('times') }}</td>
                                    </tr>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.payment_start_month') }}</th>
                                        <td class="text-break">{{ formatYearMonth(data_get($contract, 'payment_start_month')) }}</td>
                                    </tr>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.payment_last_month') }}</th>
                                        <td class="text-break">{{ formatYearMonth(data_get($contract, 'payment_last_month')) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-6">
                            <table class="table table-details">
                                <tbody>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.bonus_addition_amount') }}</th>
                                        <td class="text-break"><span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'bonus_addition_amount')) }}</td> {{-- bonus_month_payment_amount *  bonus_payment_amount--}}
                                    </tr>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.bonus_payment_month') }}</th>
                                        <td class="text-break">{{ data_get($contract, 'bonus_payment_month1.text') }}@if(data_get($contract, 'bonus_payment_month1') || data_get($contract, 'bonus_payment_month2'))・@endif
                                            {{ data_get($contract, 'bonus_payment_month2.text') }}</td>
                                    </tr>
                                    <tr>
                                        <th class="text-nowrap w-50">{{ transm('contract.attributes.bonus_payment_start_month') }}</th>
                                        <td class="text-break">{{ formatYearMonth(data_get($contract, 'bonus_payment_start_month')) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <h3 class="heading-3">{{ trans2('screens.contract.contract_tab.installment_payment_amount') }}</h3>
                    <table class="table table-border mb-5">
                        <thead>
                            <tr>
                                <th class="text-nowrap">{{ trans2('screens.contract.contract_tab.type') }}</th>
                                <th class="text-nowrap w-px-140 text-center">{{ transm('contract.attributes.total') }}</th>
                                <th class="text-nowrap w-px-140 text-center">{{ transm('contract.attributes.origin_include_tax') }}</th>
                                <th class="text-nowrap w-px-140 text-center">{{ transm('contract.attributes.split_fee') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="text-break">{{ transm('contract.attributes.first_payment_amount') }}</td>
                                <td class="text-break text-center">
                                <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'first_month_payment_amount')) }}
                                </td>
                                <td class="text-break text-center">
                                <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'first_payment_origin_include_tax')) }}
                                </td>
                                <td class="text-break text-center">
                                <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'split_fee')) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="text-break">{{ transm('contract.attributes.second_payment_amount') }}</td>
                                <td class="text-break text-center">
                                <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'second_month_payment_amount')) }}
                                </td>
                                <td class="text-break text-center">
                                <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'first_payment_origin_include_tax')) }}
                                </td>
                                <td class="text-break text-center">
                                <span class="me-1">&yen;</span>{{ number_format((int) data_get($contract, 'split_fee')) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <h2 class="heading-2">{{ data_get($contract, 'payment_company_flag.text') }}の情報</h2>
                    <table class="table table-edit">
                        <tbody>
                            <tr>
                                <th class="text-nowrap required">{{ data_get($contract, 'payment_company_flag.text') }}登録番号<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break">
                                    <input type="text" name="" value="" wire:model="updateForm.regist_number"
                                        class="form-control form-required w-px-240">
                                    <div>
                                        @error('updateForm.regist_number') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap required">{{ data_get($contract, 'payment_company_flag.text') }}登録日<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break">
                                    <input type="text" name="" value="" wire:model="updateForm.payment_company_regist_date"
                                        class="{{ empty($updateForm->payment_company_regist_date) ? 'form-required' : '' }} form-control datepicker"
                                        data-date-format="yy/mm/dd">
                                    <div>
                                        @error('updateForm.payment_company_regist_date') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.customer_information') }}</h2>
                    <table class="table table-edit">
                        <tbody>
                            <tr>
                                <th class="text-nowrap required">{{ transm('contract.attributes.bank_account_name') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break"><input type="text" name="" value="" wire:model="updateForm.bank_account_name"
                                        class="form-control form-required w-100">
                                    <div>
                                        @error('updateForm.bank_account_name') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap required">{{ transm('contract.attributes.bank_account_name_kana') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break"><input type="text" name="" value="" wire:model="updateForm.bank_account_name_kana"
                                        class="form-control form-required w-100">
                                    <div>
                                        @error('updateForm.bank_account_name_kana') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="text-nowrap required">{{ transm('contract.attributes.bank_flag') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                <td class="text-break">
                                    <div class="d-flex">
                                        @foreach ($bankFlag as $key => $text)
                                            <div class="form-radio me-5">
                                                <label>
                                                    <input type="radio" name="banking_institution" value="{{ $key }}" wire:model="updateForm.bank_flag"
                                                        class="form-radio-input select-banking-institution">
                                                    <span class="form-radio-text">{{ $text }}</span>
                                                </label>
                                            </div>
                                        @endforeach
                                    </div>
                                    <div>
                                        @error('updateForm.bank_flag') <span class="message-error">{{ $message }}</span> @enderror
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="banking-institution" data-bank-type="0">
                        <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.jp_post_bank') }}</h2>
                        <table class="table table-edit">
                            <tbody>
                                <tr>
                                    <th class="text-nowrap required">{{ transm('contract.attributes.bank_account_mark') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td class="text-break">
                                        <div class="row align-items-center">
                                            <div class="col-auto">
                                                <input type="text" name="" value="" wire:model="updateForm.bank_account_mark1"
                                                    class="form-control form-required w-px-120">
                                            </div>-
                                            <div class="col-auto">
                                                <input type="text" name="" value="" wire:model="updateForm.bank_account_mark2"
                                                    class="form-control form-required w-px-120">
                                            </div>
                                            <div class="col-auto px-0">
                                                <div class="position-relative">
                                                    <span class="position-absolute small"
                                                        style="left: .35em; top: .35em;">※</span>
                                                    <input type="text" name="" value="" wire:model="updateForm.bank_account_mark3"
                                                        class="form-control w-px-70 text-center">
                                                </div>
                                            </div>
                                            <div class="col-auto small">※6桁がある場合の6桁目</div>
                                            <div>
                                                @error('updateForm.bank_account_mark1') <span class="message-error">{{ $message }}</span> @enderror
                                            </div>
                                            <div>
                                                @error('updateForm.bank_account_mark2') <span class="message-error">{{ $message }}</span> @enderror
                                            </div>
                                            <div>
                                                @error('updateForm.bank_account_mark3') <span class="message-error">{{ $message }}</span> @enderror
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-nowrap required">{{ transm('contract.attributes.bank_account_number') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td class="text-break">
                                        <input type="text" name="" value="" wire:model="updateForm.bank_account_number"
                                            class="form-control form-required w-px-240">
                                        <div>
                                            @error('updateForm.bank_account_number') <span class="message-error">{{ $message }}</span> @enderror
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="banking-institution" data-bank-type="1">
                        <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.banks') }}</h2>
                        <table class="table table-edit">
                            <tbody>
                                <tr>
                                    <th class="text-nowrap required">{{ transm('contract.attributes.bank_code') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td class="text-break"><input type="text" name="" value="" wire:model="updateForm.bank_code"
                                            class="form-control form-required w-px-240">
                                        <div>
                                            @error('updateForm.bank_code') <span class="message-error">{{ $message }}</span> @enderror
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-nowrap required">{{ transm('contract.attributes.bank_name') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td class="text-break"><input type="text" name="" value="" wire:model="updateForm.bank_name"
                                            class="form-control form-required w-100">
                                        <div>
                                            @error('updateForm.bank_name') <span class="message-error">{{ $message }}</span> @enderror
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-nowrap required">{{ transm('contract.attributes.branch_code') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td class="text-break"><input type="text" name="" value="" wire:model="updateForm.branch_code"
                                            class="form-control form-required w-px-240">
                                        <div>
                                            @error('updateForm.branch_code') <span class="message-error">{{ $message }}</span> @enderror
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-nowrap required">{{ transm('contract.attributes.branch_name') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td class="text-break"><input type="text" name="" value="" wire:model="updateForm.branch_name"
                                            class="form-control form-required w-100">
                                        <div>
                                            @error('updateForm.branch_name') <span class="message-error">{{ $message }}</span> @enderror
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-nowrap required">{{ transm('contract.attributes.bank_account_type') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td class="text-break">
                                        <div class="d-flex">
                                            @foreach ($bankAccountType as $key => $text)
                                                <div class="form-radio me-5">
                                                    <label>
                                                        <input type="radio" name="deposit_type" value="{{ $key }}" wire:model="updateForm.bank_account_type"
                                                            class="form-radio-input">
                                                        <span class="form-radio-text">{{ $text }}</span>
                                                    </label>
                                                </div>
                                            @endforeach
                                        </div>
                                        <div>
                                            @error('updateForm.bank_account_type') <span class="message-error">{{ $message }}</span> @enderror
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-nowrap required">{{ transm('contract.attributes.bank_account_number') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td class="text-break"><input type="text" name="" value="" wire:model="updateForm.bank_account_number"
                                            class="form-control form-required w-px-240">
                                        <div>
                                            @error('updateForm.bank_account_number') <span class="message-error">{{ $message }}</span> @enderror
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <h2 class="heading-2">{{ trans2('screens.contract.contract_tab.remarks') }}</h2>
                        <div class="mb-5">
                            <textarea name="" class="form-control bg-light-blue" rows="8" wire:model="updateForm.contract_comment"></textarea>
                            <div>
                                @error('updateForm.contract_comment') <span class="message-error">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        <div class="mb-4 mt-4 d-flex justify-content-between align-items-center">
                            <a href="{{ getRoute('contract.details', ['id' => $contract?->id]) }}"
                                class="btn-text-prev small">{{ trans2('screens.customer.new.back') }}</a>
                            <button wire:click.prevent="validateUpdate"
                                class="btn btn-dark btn-medium">{{ trans2('button.update') }}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <style>
        .form-select2 {
            border-color: var(--bs-border-color);
            padding-left: 6px;
            min-height: 40px;
            overflow: auto;
            display: flex;
            align-items: center;
            max-width: 100%;
            width: 100%;
            -ms-overflow-style: none;
            scrollbar-width: none;
            border-radius: 3px;
            -webkit-appearance: none;
            /* For Safari and Chrome */
            -moz-appearance: none;
            /* For Firefox */
            appearance: none;
            /* Standard property */
            background: #fff url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNSIgaGVpZ2h0PSI3IiB2aWV3Qm94PSIwIDAgMTUgNyI+IDxwYXRoIGQ9Ik02LjgxOC42MzdhMSwxLDAsMCwxLDEuMzY1LDBsNC45NjMsNC42MzJBMSwxLDAsMCwxLDEyLjQ2Myw3SDIuNTM3YTEsMSwwLDAsMS0uNjgyLTEuNzMxWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTUgNykgcm90YXRlKDE4MCkiIGZpbGw9IiM4ZDhkOGQiLz48L3N2Zz4=");
            background-repeat: no-repeat;
            background-position: right 10px top 50%;
            background-size: 14px 8px;
        }
    </style>
    </div>
    @include('livewire.admin.contract.script')
@endsection
@push('scripts')
    <script>
        window.addEventListener('init-select2', function () {
            setTimeout(() => {
                initSelect2CourseBinding('course-general');
                initSelect2CourseBinding('course-optional');
            }, 50);
        });

        $(document).on('change', 'select.course-optional-selector', function() {
            initSelect2CourseBinding('course-optional');
        });

        $(document).on('change', 'select.course-general-selector', function() {
            initSelect2CourseBinding('course-general');
        });

        function initSelect2CourseBinding(target) {
            // take all selected
            let selectEls = $(`.${target}-selector`);
            let selectedOptions = [];

            $(`.${target}-selector`).each((index, el) => {
                let selectedValue = $(el).find('option:selected').val();
                selectedOptions.push(selectedValue);
            })

            // find selected option to hide
            $(`.${target}-selector`).each((index, el) => {
                let curOptions = Array.from(el.options).map(option => option.value);

                //remove all class to refresh select
                $(el).find('option').removeClass('option-invisible');

                for(let option of curOptions) {
                    if(!option || $.inArray(option, selectedOptions) === -1) {
                        continue;
                    }

                    //add hide class for selected option
                    $(el).find('option')
                        .filter(function () {
                            return $(this).val() === option && !$(this).is(':selected');
                        }).addClass('option-invisible');
                }

                // binding value to livewire attribute
                $(el).on('change', function (e) {
                    const value = $(this).val();
                    const model = el.getAttribute('wire:model');

                    if (model) {
                        const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                        Livewire.find(componentId).set(model, value);

                        // Add logic to update amount
                        if (model.includes('course_id')) {
                            const index = model.split('.')[1];
                            const isGeneral = model.includes('product_general');
                            const courseList = getCourseListFromDOM(isGeneral);

                            if (value && courseList) {
                                const course = courseList.find(c => c.id == value);
                                if (course) {
                                    const amountModel = model.replace('course_id', 'amount');
                                    Livewire.find(componentId).set(amountModel, course.unit_price ?? 0);
                                }
                            }
                        }
                    }

                }).select2({templateResult: resultState}); // Initialize Select2 with a custom templateResult to render options with a specific style
            })
        }

        function resultState(data, container) {
            if (data.element) {
                $(container).addClass($(data.element).attr("class"));
            }
            return data.text;
        }

        Livewire.hook('message.processed', () => {
            setTimeout(() => {
               initSelect2CourseBinding('course-general');
                initSelect2CourseBinding('course-optional');
            }, 10);
        });
    </script>
    <script>
        function getCourseListFromDOM(isGeneral) {
            const selector = isGeneral ? '.course-general-selector' : '.course-optional-selector';
            const select = document.querySelector(selector);
            if (!select) return [];

            const options = Array.from(select.options).filter(opt => opt.value);

            // return array object
            return options.map(opt => ({
                id: opt.value,
                name: opt.text,
                unit_price: opt.getAttribute('data-unit-price') ? Number(opt.getAttribute('data-unit-price')) : null
            }));
        }
    </script>
@endpush

