{"__meta": {"id": "01K03AMAECAH6QH7TK1J5YJET0", "datetime": "2025-07-14 11:01:11", "utime": **********.884937, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:01:11] LOG.debug: (Time: 62.93) SQL: select * from `administrators` where `email` = '<EMAIL>' and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.5636, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: (Time: 00.39) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.786403, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.252611, "end": **********.884953, "duration": 0.6323421001434326, "duration_str": "632ms", "measures": [{"label": "Booting", "start": **********.252611, "relative_start": 0, "end": **********.417714, "relative_end": **********.417714, "duration": 0.*****************, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.417723, "relative_start": 0.*****************, "end": **********.884955, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.425511, "relative_start": 0.*****************, "end": **********.427871, "relative_end": **********.427871, "duration": 0.002360105514526367, "duration_str": "2.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.840866, "relative_start": 0.****************, "end": **********.882849, "relative_end": **********.882849, "duration": 0.*****************, "duration_str": "41.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "1x livewire.admin.auth.login", "param_count": null, "params": [], "start": **********.83876, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/auth/login.blade.phplivewire.admin.auth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.auth.login"}, {"name": "1x components.loading-overlay", "param_count": null, "params": [], "start": **********.839349, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/loading-overlay.blade.phpcomponents.loading-overlay", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Floading-overlay.blade.php&line=1", "ajax": false, "filename": "loading-overlay.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.loading-overlay"}, {"name": "1x components.elements.flash_messages", "param_count": null, "params": [], "start": **********.839786, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/elements/flash_messages.blade.phpcomponents.elements.flash_messages", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Felements%2Fflash_messages.blade.php&line=1", "ajax": false, "filename": "flash_messages.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.elements.flash_messages"}]}, "route": {"uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Auth\\Login@login<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Auth/Login.php:30-39</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06332, "accumulated_duration_str": "63.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `email` = '\\'<EMAIL>\\'' and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": ["'<EMAIL>'", "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Admin/Auth/Login.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Auth\\Login.php", "line": 56}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Auth/Login.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Auth\\Login.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.5007758, "duration": 0.06293, "duration_str": "62.93ms", "memory": 0, "memory_str": null, "filename": "Login.php:56", "source": {"index": 16, "namespace": null, "name": "app/Livewire/Admin/Auth/Login.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Auth\\Login.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=56", "ajax": false, "filename": "Login.php", "line": "56"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 99.384}, {"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "core/Common/Common.php", "file": "C:\\xampp\\htdocs\\ladybird\\core\\Common\\Common.php", "line": 588}, {"index": 21, "namespace": null, "name": "core/Database/Eloquent/Model/BaseModel.php", "file": "C:\\xampp\\htdocs\\ladybird\\core\\Database\\Eloquent\\Model\\BaseModel.php", "line": 153}], "start": **********.786259, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 99.384, "width_percent": 0.616}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": {"admin.auth.login #6uh1kCp2HUJytPejfZto": "array:4 [\n  \"data\" => array:5 [\n    \"guest\" => true\n    \"loginForm\" => App\\Livewire\\Admin\\Auth\\LoginForm {#762\n      #component: App\\Livewire\\Admin\\Auth\\Login {#732\n        #__id: \"6uh1kCp2HUJytPejfZto\"\n        #__name: \"admin.auth.login\"\n        #listeners: []\n        #attributes: Livewire\\Features\\SupportAttributes\\AttributeCollection {#886\n          #items: array:4 [\n            0 => Livewire\\Attributes\\Layout {#758\n              #component: App\\Livewire\\Admin\\Auth\\Login {#732}\n              #subTarget: null\n              #subName: null\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#835\n                +name: \"ROOT\"\n              }\n              #levelName: null\n              +name: \"components.layouts.auth\"\n              +params: []\n            }\n            1 => Livewire\\Attributes\\Validate {#831\n              #component: App\\Livewire\\Admin\\Auth\\Login {#732}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#762}\n              #subName: \"email\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#829\n                +name: \"PROPERTY\"\n              }\n              #levelName: \"loginForm.email\"\n              +rule: \"required|check_email\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n            2 => Livewire\\Attributes\\Validate {#832\n              #component: App\\Livewire\\Admin\\Auth\\Login {#732}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#762}\n              #subName: \"password\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#829}\n              #levelName: \"loginForm.password\"\n              +rule: \"required\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n            3 => Livewire\\Attributes\\Validate {#830\n              #component: App\\Livewire\\Admin\\Auth\\Login {#732}\n              #subTarget: App\\Livewire\\Admin\\Auth\\LoginForm {#762}\n              #subName: \"remember\"\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#829}\n              #levelName: \"loginForm.remember\"\n              +rule: \"nullable\"\n              #attribute: null\n              #as: null\n              #message: null\n              #onUpdate: true\n              #translate: true\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n        #withValidatorCallback: null\n        #rulesFromOutside: []\n        #messagesFromOutside: []\n        #validationAttributesFromOutside: []\n        +guest: true\n        #viewData: []\n        +page: \"\"\n        +pageTitle: \"ログイン\"\n        +redirecting: true\n        +loginForm: App\\Livewire\\Admin\\Auth\\LoginForm {#762}\n      }\n      #propertyName: \"loginForm\"\n      #withValidatorCallback: null\n      #rulesFromOutside: array:3 [\n        0 => array:1 [\n          \"email\" => \"required|check_email\"\n        ]\n        1 => array:1 [\n          \"password\" => \"required\"\n        ]\n        2 => array:1 [\n          \"remember\" => \"nullable\"\n        ]\n      ]\n      #messagesFromOutside: []\n      #validationAttributesFromOutside: []\n      +email: \"<EMAIL>\"\n      +password: \"admin123\"\n      +remember: null\n      +disableLogin: null\n    }\n    \"page\" => \"\"\n    \"pageTitle\" => \"ログイン\"\n    \"redirecting\" => true\n  ]\n  \"name\" => \"admin.auth.login\"\n  \"component\" => \"App\\Livewire\\Admin\\Auth\\Login\"\n  \"id\" => \"6uh1kCp2HUJytPejfZto\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ijOLndFWNGcJNdFIRj4yCtVGCX5UnkXF8aPoDa9H", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/management/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Auth\\Login@login<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FLivewire%2FAdmin%2FAuth%2FLogin.php&line=30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Auth/Login.php:30-39</a>", "middleware": "web", "duration": "633ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1078300625 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1078300625\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-129233975 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KsF2SvrpMvyKYq4jjEGhUMQRwD5kBH2GRCXsEOX2</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"480 characters\">{&quot;data&quot;:{&quot;guest&quot;:true,&quot;loginForm&quot;:[{&quot;email&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;remember&quot;:false,&quot;disableLogin&quot;:false},{&quot;class&quot;:&quot;App\\\\Livewire\\\\Admin\\\\Auth\\\\LoginForm&quot;,&quot;s&quot;:&quot;form&quot;}],&quot;page&quot;:&quot;&quot;,&quot;pageTitle&quot;:&quot;\\u30ed\\u30b0\\u30a4\\u30f3&quot;,&quot;redirecting&quot;:false},&quot;memo&quot;:{&quot;id&quot;:&quot;6uh1kCp2HUJytPejfZto&quot;,&quot;name&quot;:&quot;admin.auth.login&quot;,&quot;path&quot;:&quot;management\\/login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;af4259bd5abdb36b3480527d5e876220e1b029135e568059c0e3dd9a0a2ae7d0&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>loginForm.email</span>\" => \"<span class=sf-dump-str title=\"15 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>loginForm.password</span>\" => \"<span class=sf-dump-str title=\"8 characters\">admin123</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"5 characters\">login</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129233975\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">778</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/management/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"844 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjA4U2lRM1UzNUFLZDdJS2w0RnA5OFE9PSIsInZhbHVlIjoiQWR3bDY3VUdUNVhpZmFycGtuTUEzUzBMVzhPNkJ3ZEJHelRHOUQ0eWVkR1dGbEZ4UmZRUkhNalpPb0UzY2NqWkx4ZTNlUmpGNHZRajU2Vm9DdEhTaDdqalJQRFBFTWMrOGdpYzZ0Mkd5c3VocllmYVBhMmJpcVhiMUZrUEJDd0pVYzlLV2o5ajZyOGlFVjc0Zy9ZWHBRPT0iLCJtYWMiOiIzM2Q4NGVmZmVkNmY3OGViYTVlMTdhNDlhNDNmODRmOGM5MmNhMDE5YzhmZWU1NzcwMTdhYmJjOTY4NDlmMTFlIiwidGFnIjoiIn0%3D; ladybird_session=Udb6ATAhTWSFlkEWdVLpLEtjKwtLxhT3N0MOIVhZ; XSRF-TOKEN=eyJpdiI6ImFxdHVKb1RBUFl5UkNseE92U0h4bVE9PSIsInZhbHVlIjoiRE94K3pzNVp5WjJMZ2tROXpjVTdnTXdGYU02OUlDaVF2WXFWcDMrdTAraStNRzFyazBNRFkyWVhUbnhGY1FOaVBUYTdxYlhnaDIyS1F2eWRkU2pnbkhnS1VuVjhJRUIxUmRZakFjTnFUVVVIMHN2TXRHZUdtQTRZZHlkdjd1UUciLCJtYWMiOiI1MmM4ZjkzMWZlMWNmZDIxMzQ0ZmMzZjk2Yjk5ODY1OTg2NDQ1MTU1MmIyZGNmYmY0MGYzZTVhMmVlMzk5MGIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1967892197 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KsF2SvrpMvyKYq4jjEGhUMQRwD5kBH2GRCXsEOX2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967892197\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1278083778 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 02:01:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278083778\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1286185209 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ijOLndFWNGcJNdFIRj4yCtVGCX5UnkXF8aPoDa9H</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/management/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286185209\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}