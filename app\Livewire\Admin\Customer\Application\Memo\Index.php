<?php

namespace App\Livewire\Admin\Customer\Application\Memo;

use App\Enums\ApplicationInspectionCancelStatusEnum;
use App\Enums\ApplicationInspectionStatusEnum;
use App\Enums\ApplicationInspectionToShopEnum;
use App\Enums\ApplicationStatusEnum;
use App\Enums\SidebarMenuEnum;
use App\Livewire\Admin\Customer\Forms\MemoForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Services\ApplicationService;
use App\Services\ApplicationServiceInspection;
use App\Services\ToastService;
use Livewire\Attributes\On;
use Livewire\Features\SupportFileUploads\WithFileUploads;

class Index extends BaseAdminPageComponent
{
    use WithFileUploads;

    public MemoForm $memoForm;

    public $customer_id;

    public $application_id;

    public $customer = null;

    public $application = null;

    public $isCanDelete;

    public $application_file;

    public $lastApplicationInspection;

    public $toShopIds;

    public $statuses;

    public $cancelStatuses;

    public $cancelStatuseAll;

    public $inspectionColors;

    public $applicationInspectionStatuses;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CUSTOMER;
        parent::__construct();

        $this->dispatch('init-select2');
    }

    public function mount($customer_id, $application_id)
    {
        $this->customer_id = $customer_id;
        $this->application_id = $application_id;

        $this->customer = app()->make(CustomerRepository::class)->getBasicInfoCustomer($customer_id);
        $this->application = app()->make(ApplicationRepository::class)->getApplicationForMemo($this->application_id);

        if (empty($this->customer) || empty($this->application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }

        if ($this->application?->status?->value === ApplicationStatusEnum::IN_PROGRESS) {
            return redirect()->route('admin.customer.application.detail.index', [$customer_id, $application_id]);
        }

        // set last application inspection
        $this->lastApplicationInspection = $this->application?->applicationInspectionStatus?->last() ?? null;

        // prepare data for memo form
        $this->prepareData();

        $this->isCanDelete = !app(ApplicationService::class)->getCountAppByCustomerId($this->customer_id);
    }

    private function prepareData()
    {
        $currentUser = auth()->user();
        $this->toShopIds = ApplicationInspectionToShopEnum::texts();
        $this->statuses = ApplicationInspectionStatusEnum::texts();
        $this->cancelStatuseAll = ApplicationInspectionCancelStatusEnum::texts();
        $this->inspectionColors = ApplicationInspectionStatusEnum::colors();
        $lastApplicationInspection = $this->lastApplicationInspection;

        if ($currentUser?->isBrand() || $currentUser?->isStore()) {
            // toShopIds
            $this->toShopIds = collect($this->toShopIds)->filter(function ($value, $key) {
                return $key == ApplicationInspectionToShopEnum::HEADQUARTERS;
            })->toArray();

            // cancelStatuses
            $this->cancelStatuses = collect($this->cancelStatuseAll)->filter(function ($value, $key) {
                return $key == ApplicationInspectionCancelStatusEnum::COOLING_OFF_REQUESTED || $key == ApplicationInspectionCancelStatusEnum::STORE_CANCELLATION_REQUESTED;
            })->toArray();

            // statuses
            $this->statuses = collect($this->statuses)->filter(function ($value, $key) use ($lastApplicationInspection) {

                // 2
                if($lastApplicationInspection?->status?->value == ApplicationInspectionStatusEnum::AWAITING_DOCUMENTS) {
                    return $key == ApplicationInspectionStatusEnum::DOCUMENTS_SUBMITTED;
                }

                // 4
                if($lastApplicationInspection?->status?->value == ApplicationInspectionStatusEnum::AWAITING_CONTACT_RESPONSE) {
                    return $key == ApplicationInspectionStatusEnum::CONTACT_RESPONSE_RECEIVED;
                }

                // 6
                if($lastApplicationInspection?->status?->value == ApplicationInspectionStatusEnum::AWAITING_IDENTITY_CONTACT_RESPONSE) {
                    return $key == ApplicationInspectionStatusEnum::IDENTITY_GUARANTOR_CONTACT_RESPONSE_RECEIVED;
                }

                // 7
                if($lastApplicationInspection?->status?->value == ApplicationInspectionStatusEnum::AWAITING_GUARANTOR_CONTACT_RESPONSE) {
                    return $key == ApplicationInspectionStatusEnum::IDENTITY_GUARANTOR_CONTACT_RESPONSE_RECEIVED;
                }

                // 10
                if($lastApplicationInspection?->status?->value == ApplicationInspectionStatusEnum::AWAITING_IDENTITY_REDESIGNATION) {
                    return $key == ApplicationInspectionStatusEnum::IDENTITY_REDESIGNATED;
                }

                // 12
                 if($lastApplicationInspection?->status?->value == ApplicationInspectionStatusEnum::AWAITING_GUARANTOR_REDESIGNATION) {
                    return $key == ApplicationInspectionStatusEnum::GUARANTOR_REDESIGNATED;
                }

                return null;

            })->filter()->toArray();

            // append current status
            // $currentStatus = [
            //     $lastApplicationInspection?->status?->value => $lastApplicationInspection?->status?->text
            // ];
            // $this->statuses = $currentStatus + $this->statuses;

        } else { // admin
            // cancelStatuses
            $this->cancelStatuses = collect($this->cancelStatuseAll)->filter(function ($value, $key) {
                return $key == ApplicationInspectionCancelStatusEnum::COOLING_OFF_APPROVED || $key == ApplicationInspectionCancelStatusEnum::STORE_CANCELLATION_APPROVED;
            })->toArray();
        }

        $this->loadApplicationInspection();
    }

    #[On('reload-memo-list')]
    public function loadApplicationInspection()
    {
        // get application inspection statuses
        $this->applicationInspectionStatuses = app(ApplicationServiceInspection::class)->getApplicationInspectionStatuses($this->application_id);
    }

    public function validateSave()
    {
        $this->memoForm->validate();

        $this->setDataRegistrationConfirmModal(getConstant('SCREENS.CUSTOMER'), 'save-memo');
    }

    #[On('save-memo')]
    public function save()
    {
        $body = $this->memoForm->toArray();

        // if files are not provided, use the existing files from the form
        if (empty($body['files']) || (is_array($body['files']) && empty($body['files'][0]))) {
            $body['files'] = $this->memoForm->files;
        }

        // save the memo information
        $app = app(ApplicationServiceInspection::class)->saveMemo($this->application_id, $body);

        if ($app) {
            $this->dispatch('showToastSuccess', __('messages.update_success'));

            // reload the application data
            $this->loadApplicationInspection();

            // reset form data
            $this->memoForm->files = [];
            $this->memoForm->comment = '';
        } else {
            $this->dispatch('showToastError', __('messages.update_failed'));
        }
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.application.memo.index', [
            'customer' => $this->customer,
            'application' => $this->application,
            'lastApplicationInspection' => $this->lastApplicationInspection,
            'applicationInspectionStatuses' => $this->applicationInspectionStatuses,
        ]);
    }
}
