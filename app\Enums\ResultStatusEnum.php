<?php
namespace App\Enums;

final class ResultStatusEnum extends Enum
{
    const TRANSFER_COMPLETED = 0;
    const INSUFFICIENT_FUNDS = 1;
    const NO_TRANSACTION = 2;
    const TRANSFER_SUSPENDED_DEPOSITOR = 3;
    const MISSING_TRANSFER_REQUEST_FORM = 4;
    const BANK_TRANSFER_ERROR = 7;
    const TRANSFER_SUSPENDED_COMPANY = 8;
    const OTHER = 9;
    public static function texts(): array
    {
        return [
            self::TRANSFER_COMPLETED => trans2('ResultStatusEnum.TRANSFER_COMPLETED'),
            self::INSUFFICIENT_FUNDS => trans2('ResultStatusEnum.INSUFFICIENT_FUNDS'),
            self::NO_TRANSACTION => trans2('ResultStatusEnum.NO_TRANSACTION'),
            self::TRANSFER_SUSPENDED_DEPOSITOR => trans2('ResultStatusEnum.TRANSFER_SUSPENDED_DEPOSITOR'),
            self::MISSING_TRANSFER_REQUEST_FORM => trans2('ResultStatusEnum.MISSING_TRANSFER_REQUEST_FORM'),
            self::BANK_TRANSFER_ERROR => trans2('ResultStatusEnum.BANK_TRANSFER_ERROR'),
            self::TRANSFER_SUSPENDED_COMPANY => trans2('ResultStatusEnum.TRANSFER_SUSPENDED_COMPANY'),
            self::OTHER => trans2('Enum.OTHER'),
        ];
    }

}
