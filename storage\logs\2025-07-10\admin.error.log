
[2025-07-10 10:56:33] SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `administrators` where `email` = <EMAIL> and `administrators`.`del_flag` = 0 limit 1)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(983): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(962): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(785): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(739): Illuminate\Database\Query\Builder->get(Array)
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(723): Illuminate\Database\Eloquent\Builder->getModels(Array)
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Eloquent\Builder->get(Array)
#11 C:\xampp\htdocs\ladybird\app\Livewire\Admin\Auth\Login.php(56): Core\Database\Eloquent\BaseEloquentBuilder->first()
#12 C:\xampp\htdocs\ladybird\app\Livewire\Admin\Auth\Login.php(34): App\Livewire\Admin\Auth\Login->authenticate()
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): App\Livewire\Admin\Auth\Login->login()
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#17 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Wrapped.php(23): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array)
#18 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Mechanisms\HandleComponents\HandleComponents.php(474): Livewire\Wrapped->__call('login', Array)
#19 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Mechanisms\HandleComponents\HandleComponents.php(101): Livewire\Mechanisms\HandleComponents\HandleComponents->callMethods(Object(App\Livewire\Admin\Auth\Login), Array, Object(Livewire\Mechanisms\HandleComponents\ComponentContext))
#20 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\LivewireManager.php(102): Livewire\Mechanisms\HandleComponents\HandleComponents->update(Array, Array, Array)
#21 C:\xampp\htdocs\ladybird\vendor\livewire\volt\src\LivewireManager.php(35): Livewire\LivewireManager->update(Array, Array, Array)
#22 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Mechanisms\HandleRequests\HandleRequests.php(94): Livewire\Volt\LivewireManager->update(Array, Array, Array)
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(46): Livewire\Mechanisms\HandleRequests\HandleRequests->handleUpdate()
#24 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(Livewire\Mechanisms\HandleRequests\HandleRequests), 'handleUpdate')
#25 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#26 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#28 C:\xampp\htdocs\ladybird\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#32 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#33 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#41 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#42 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#43 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#44 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#45 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#46 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#47 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Features\SupportDisablingBackButtonCache\DisableBackButtonCacheMiddleware.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Livewire\Features\SupportDisablingBackButtonCache\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\xampp\htdocs\ladybird\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#57 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#58 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#63 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#64 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#65 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#66 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#67 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#68 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#69 C:\xampp\htdocs\ladybird\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#70 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\xampp\\htdocs...')
#71 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829
#Request: URI=/livewire/update | ACTION=livewire:admin.auth.login@login | SCRIPT=/index.php | SERVER=127.0.0.1 | IP=127.0.0.1 | AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 | REFERER=http://127.0.0.1:8000/management/login |   

[2025-07-10 10:57:07] Unsupported operand types: int + string
#0 C:\xampp\htdocs\ladybird\app\Livewire\Admin\Balance\TableDataList.php(42): App\Services\CsvService->exportCsvBalance('\xE6\xAE\x8B\xE9\xAB\x98\xE7\xAE\xA1\xE7\x90\x86_20...', Array, Object(Illuminate\Database\Eloquent\Collection))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): App\Livewire\Admin\Balance\TableDataList->downloadCSV()
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#5 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Wrapped.php(23): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array)
#6 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Features\SupportEvents\SupportEvents.php(29): Livewire\Wrapped->__call('downloadCSV', Array)
#7 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\ComponentHook.php(41): Livewire\Features\SupportEvents\SupportEvents->call('downloadCSV', Array, Object(Closure))
#8 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\ComponentHookRegistry.php(110): Livewire\ComponentHook->callCall('__dispatch', Array, Object(Closure))
#9 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\ComponentHookRegistry.php(65): Livewire\ComponentHookRegistry::Livewire\{closure}('__dispatch', Array, Object(Closure))
#10 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\EventBus.php(60): Livewire\ComponentHookRegistry::Livewire\{closure}(Object(App\Livewire\Admin\Balance\TableDataList), '__dispatch', Array, Object(Livewire\Mechanisms\HandleComponents\ComponentContext), Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\helpers.php(98): Livewire\EventBus->trigger('call', Object(App\Livewire\Admin\Balance\TableDataList), '__dispatch', Array, Object(Livewire\Mechanisms\HandleComponents\ComponentContext), Object(Closure))
#12 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Mechanisms\HandleComponents\HandleComponents.php(453): Livewire\trigger('call', Object(App\Livewire\Admin\Balance\TableDataList), '__dispatch', Array, Object(Livewire\Mechanisms\HandleComponents\ComponentContext), Object(Closure))
#13 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Mechanisms\HandleComponents\HandleComponents.php(101): Livewire\Mechanisms\HandleComponents\HandleComponents->callMethods(Object(App\Livewire\Admin\Balance\TableDataList), Array, Object(Livewire\Mechanisms\HandleComponents\ComponentContext))
#14 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\LivewireManager.php(102): Livewire\Mechanisms\HandleComponents\HandleComponents->update(Array, Array, Array)
#15 C:\xampp\htdocs\ladybird\vendor\livewire\volt\src\LivewireManager.php(35): Livewire\LivewireManager->update(Array, Array, Array)
#16 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Mechanisms\HandleRequests\HandleRequests.php(94): Livewire\Volt\LivewireManager->update(Array, Array, Array)
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(46): Livewire\Mechanisms\HandleRequests\HandleRequests->handleUpdate()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(Livewire\Mechanisms\HandleRequests\HandleRequests), 'handleUpdate')
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#22 C:\xampp\htdocs\ladybird\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#28 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#29 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#32 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#33 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#36 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#37 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#38 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#39 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#40 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#41 C:\xampp\htdocs\ladybird\vendor\livewire\livewire\src\Features\SupportDisablingBackButtonCache\DisableBackButtonCacheMiddleware.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Livewire\Features\SupportDisablingBackButtonCache\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\xampp\htdocs\ladybird\vendor\barryvdh\laravel-debugbar\src\Middleware\InjectDebugbar.php(66): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#44 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Barryvdh\Debugbar\Middleware\InjectDebugbar->handle(Object(Illuminate\Http\Request), Object(Closure))
#45 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#46 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(36): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#51 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#52 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#57 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#58 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#59 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#60 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#61 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#62 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#63 C:\xampp\htdocs\ladybird\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#64 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\resources\server.php(16): require_once('C:\\xampp\\htdocs...')
#65 {main}","[F]C:\xampp\htdocs\ladybird\app\Services\CsvService.php",[L]59
#Request: URI=/livewire/update | ACTION=livewire:admin.balance.table-data-list@export_csv | SCRIPT=/index.php | SERVER=127.0.0.1 | IP=127.0.0.1 | AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 | REFERER=http://127.0.0.1:8000/management/balances |   
