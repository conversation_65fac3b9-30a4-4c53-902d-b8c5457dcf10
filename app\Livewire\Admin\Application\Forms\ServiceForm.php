<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\CustomerFlagEnum;
use App\Livewire\Base\BaseAdminForm;
use Illuminate\Validation\Rule;

class ServiceForm extends BaseAdminForm
{
    public $service_handover_date;

    public $service_start_date;

    public $service_end_date;

    public $service_count;

    public $customer_flag = CustomerFlagEnum::SELF;

    public $customer_other;


    public function rules()
    {
        $customerOtherRule = $this->customer_flag == CustomerFlagEnum::OTHER ? 'required' : 'nullable';
        return [
            'service_handover_date' => 'required|date',
            'service_start_date' => 'required|date',
            'service_end_date' => 'required|date|after:service_start_date',
            'service_count' => 'required|' . $this->smallIntegerRule(),
            'customer_flag' => ['required', Rule::in(CustomerFlagEnum::getValues())],
            'customer_other' => $customerOtherRule . '|string|max:512',
        ];
    }

    public function getValidationAttributes()
    {
        return __('models.service.attributes');
    }
}
