<?php

namespace App\Models\Presenters;

use Carbon\Carbon;


trait PCustomer
{
    public function convertToCsvRow(): array
    {
        return [
            $this->id,
            $this->first_name,
            $this->last_name,
            $this->first_name_kana,
            $this->last_name_kana,
            $this->birthday,
            $this->tel1,
            $this->tel2,
            $this->tel3,
            $this->application_status,
            $this->application_date,
            $this->brand_name,
            $this->ins_date_for_csv ?? '',
            $this->upd_date_for_csv ?? '',
        ];
    }

    /**
     * @return string
     * full name
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->last_name . ' ' . $this->first_name);
    }

    /**
     * @return string
     * full name kana
     */
    public function getFullNameKanaAttribute(): string
    {
        return trim($this->last_name_kana . ' ' . $this->first_name_kana);
    }

    /**
     * @return string
     * full phone
     */
    public function getFullPhoneAttribute(): string
    {
        $parts = array_filter([$this->tel1, $this->tel2, $this->tel3]);
        return implode('-', $parts);
    }

    public function getBankAccountMarkAttribute()
    {
       return $this->attributes['bank_account_mark1'] . '-' . $this->attributes['bank_account_mark2'];
    }

    public function getBirthdayAttribute()
    {
        return $this->attributes['birthday']
            ? Carbon::parse($this->attributes['birthday'])->format('Y/m/d')
            : null;
    }

    public function getContactHopeDate1PDF()
    {
        return $this->attributes['contact_hope_date1']
            ? Carbon::parse($this->attributes['contact_hope_date1'])->format('Y年m月d日')
            : null;
    }

    public function getServiceHandoverDatePDF()
    {
        return $this->attributes['service_handover_date']
            ? Carbon::parse($this->attributes['service_handover_date'])->format('Y年m月d日')
            : null;
    }

    public function getServiceStartDatePDF()
    {
        return $this->attributes['service_start_date']
            ? Carbon::parse($this->attributes['service_start_date'])->format('Y年m月d日')
            : null;
    }

    public function getServiceEndDatePDF()
    {
        return $this->attributes['service_end_date']
            ? Carbon::parse($this->attributes['service_end_date'])->format('Y年m月d日')
            : null;
    }

    public function getContactHopeDate2PDF()
    {
        return $this->attributes['contact_hope_date2']
            ? Carbon::parse($this->attributes['contact_hope_date2'])->format('Y年m月d日')
            : null;
    }

    public function getContactHopeStartTime1PDF()
    {
        return $this->attributes['contact_hope_start_time1']
            ? Carbon::createFromFormat('Hi', $this->attributes['contact_hope_start_time1'])->format('H時i分')
            : null;
    }

    public function getContactHopeStartTime2PDF()
    {
        return $this->attributes['contact_hope_start_time2']
            ? Carbon::createFromFormat('Hi', $this->attributes['contact_hope_start_time2'])->format('H時i分')
            : null;
    }

    public function getContactHopeEndTime1PDF()
    {
        return $this->attributes['contact_hope_end_time1']
            ? Carbon::createFromFormat('Hi', $this->attributes['contact_hope_end_time1'])->format('H時i分')
            : null;
    }

    public function getContactHopeEndTime2PDF()
    {
        return $this->attributes['contact_hope_end_time2']
            ? Carbon::createFromFormat('Hi', $this->attributes['contact_hope_end_time2'])->format('H時i分')
            : null;
    }

    public function getCustomerBirthdayAge()
    {
        $birthday = '';

        if ($this->attributes['birthday']) {
            $date = Carbon::parse($this->attributes['birthday'])->format('Y年m月d日');
            $age = Carbon::parse($this->attributes['birthday'])->age ? '(' . Carbon::parse($this->attributes['birthday'])->age . '歳)' : '';
            $birthday = $date . ' ' . $age;
        }

        return $birthday;
    }

    public function getCustomerGwBirthday() 
    {
        $birthday = '';

        if ($this->attributes['gw_birthday']) {
            $date = Carbon::parse($this->attributes['gw_birthday'])->format('Y年m月d日');
            $age = Carbon::parse($this->attributes['gw_birthday'])->age ? '(' . Carbon::parse($this->attributes['gw_birthday'])->age . '歳)' : '';
            $birthday = $date . ' ' . $age;
        }

        return $birthday;
    }
}
