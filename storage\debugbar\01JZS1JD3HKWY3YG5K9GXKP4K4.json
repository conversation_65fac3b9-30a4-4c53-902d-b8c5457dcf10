{"__meta": {"id": "01JZS1JD3HKWY3YG5K9GXKP4K4", "datetime": "2025-07-10 11:10:27", "utime": **********.570437, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 9, "messages": [{"message": "[11:10:27] LOG.debug: (Time: 02.03) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.51267, "xdebug_link": null, "collector": "log"}, {"message": "[11:10:27] LOG.debug: (Time: 00.44) SQL: select * from `shops` where `shops`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.523386, "xdebug_link": null, "collector": "log"}, {"message": "[11:10:27] LOG.debug: (Time: 00.48) SQL: select * from `brands` where `brands`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.527245, "xdebug_link": null, "collector": "log"}, {"message": "[11:10:27] LOG.debug: (Time: 00.44) SQL: select * from `shops` where `shops`.`id` in (1, 2) {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.535244, "xdebug_link": null, "collector": "log"}, {"message": "[11:10:27] LOG.debug: (Time: 00.45) SQL: select * from `brands` where `brands`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.538699, "xdebug_link": null, "collector": "log"}, {"message": "[11:10:27] LOG.debug: (Time: 00.85) SQL: select DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') AS payment_month,\n\n            SUM(loan_schedules.total_amount - loan_schedules.amount_paid) AS loan_balance,\n\n            SUM(loan_schedules.amount_paid) AS paid_amount,\n\n            SUM(IFNULL(loan_arrears.delay_damage_amount, 0)) AS delay_damage_total,\n            SUM(IFNULL(loan_arrears.reissue_fee, 0)) AS reissue_fee_total,\n\n            SUM(DISTINCT IFNULL(applications.sub_total_amount, 0)) AS principal_total,\n            SUM(DISTINCT IFNULL(applications.fee_amount, 0)) AS fee_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.total_amount, 0), 0\n            )) AS current_month_contract_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.sub_total_amount, 0), 0\n            )) AS current_month_principal_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.contract_cancel_amount, 0), 0\n            )) AS cancel_amount_total,\n\n            SUM(DISTINCT IF(\n                DATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\n                IFNULL(applications.forced_contract_cancel_amount, 0), 0\n            )) AS forced_cancel_amount_total,\n\n            SUM(\n                (loan_schedules.total_amount - loan_schedules.amount_paid)\n                * (IFNULL(applications.sub_total_amount, 0) / NULLIF(applications.total_amount, 0))\n            ) AS remaining_principal_estimated from `loan_schedules` left join `loan_arrears` on `loan_arrears`.`loan_schedule_id` = `loan_schedules`.`id` and `loan_arrears`.`del_flag` = 0 left join `applications` on `loan_schedules`.`application_id` = `applications`.`id` and `applications`.`del_flag` = 0 where DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') >= '202504' and DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') <= '202507' and `loan_schedules`.`del_flag` = '0' group by DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') order by `payment_month` asc {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.563338, "xdebug_link": null, "collector": "log"}, {"message": "[11:10:27] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\088b009d0fa183a9dc89417d41ff2ebb.php on line 25", "message_html": null, "is_string": false, "label": "warning", "time": **********.567002, "xdebug_link": null, "collector": "log"}, {"message": "[11:10:27] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\088b009d0fa183a9dc89417d41ff2ebb.php on line 25", "message_html": null, "is_string": false, "label": "warning", "time": **********.567249, "xdebug_link": null, "collector": "log"}, {"message": "[11:10:27] LOG.warning: number_format(): Passing null to parameter #1 ($num) of type float is deprecated in C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\088b009d0fa183a9dc89417d41ff2ebb.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.56743, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.329051, "end": **********.570461, "duration": 0.2414100170135498, "duration_str": "241ms", "measures": [{"label": "Booting", "start": **********.329051, "relative_start": 0, "end": **********.481851, "relative_end": **********.481851, "duration": 0.****************, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.48186, "relative_start": 0.*****************, "end": **********.570462, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "88.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.489025, "relative_start": 0.****************, "end": **********.490813, "relative_end": **********.490813, "duration": 0.0017879009246826172, "duration_str": "1.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.568134, "relative_start": 0.*****************, "end": **********.568757, "relative_end": **********.568757, "duration": 0.0006229877471923828, "duration_str": "623μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "1x volt-livewire::admin.balance.search", "param_count": null, "params": [], "start": **********.55217, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/admin/balance/search.blade.phpvolt-livewire::admin.balance.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fbalance%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::admin.balance.search"}, {"name": "1x livewire.admin.balance.table-data-list", "param_count": null, "params": [], "start": **********.566163, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/balance/table-data-list.blade.phplivewire.admin.balance.table-data-list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fbalance%2Ftable-data-list.blade.php&line=1", "ajax": false, "filename": "table-data-list.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.balance.table-data-list"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Volt\\Component@anonymous\u0000C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0ceb4ce65af6e605135ffdc5d4e76fb6.php:8$478f@search", "middleware": "web", "as": "livewire.update"}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.004689999999999999, "accumulated_duration_str": "4.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.51075, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 43.284}, {"sql": "select * from `shops` where `shops`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 61}, {"index": 16, "namespace": null, "name": "resources/views/livewire/admin/balance/search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\balance\\search.blade.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentResolver.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentResolver.php", "line": 39}], "start": **********.5230482, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ShopRepository.php:61", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopRepository.php&line=61", "ajax": false, "filename": "ShopRepository.php", "line": "61"}, "connection": "local-ladybird", "explain": null, "start_percent": 43.284, "width_percent": 9.382}, {"sql": "select * from `brands` where `brands`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 54}, {"index": 16, "namespace": null, "name": "resources/views/livewire/admin/balance/search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\balance\\search.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentResolver.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentResolver.php", "line": 39}], "start": **********.526868, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BrandRepository.php:54", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FBrandRepository.php&line=54", "ajax": false, "filename": "BrandRepository.php", "line": "54"}, "connection": "local-ladybird", "explain": null, "start_percent": 52.665, "width_percent": 10.235}, {"sql": "select * from `shops` where `shops`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.5349061, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "EloquentCollectionSynth.php:70", "source": {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FEloquentCollectionSynth.php&line=70", "ajax": false, "filename": "EloquentCollectionSynth.php", "line": "70"}, "connection": "local-ladybird", "explain": null, "start_percent": 62.9, "width_percent": 9.382}, {"sql": "select * from `brands` where `brands`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92}], "start": **********.538347, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EloquentCollectionSynth.php:70", "source": {"index": 15, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportModels/EloquentCollectionSynth.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\EloquentCollectionSynth.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportModels%2FEloquentCollectionSynth.php&line=70", "ajax": false, "filename": "EloquentCollectionSynth.php", "line": "70"}, "connection": "local-ladybird", "explain": null, "start_percent": 72.281, "width_percent": 9.595}, {"sql": "select DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') AS payment_month,\nSUM(loan_schedules.total_amount - loan_schedules.amount_paid) AS loan_balance,\nSUM(loan_schedules.amount_paid) AS paid_amount,\nSUM(IFNULL(loan_arrears.delay_damage_amount, 0)) AS delay_damage_total,\nSUM(IFNULL(loan_arrears.reissue_fee, 0)) AS reissue_fee_total,\nSUM(DISTINCT IFNULL(applications.sub_total_amount, 0)) AS principal_total,\nSUM(DISTINCT IFNULL(applications.fee_amount, 0)) AS fee_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.total_amount, 0), 0\n)) AS current_month_contract_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.payment_start_month, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.sub_total_amount, 0), 0\n)) AS current_month_principal_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.contract_cancel_amount, 0), 0\n)) AS cancel_amount_total,\nSUM(DISTINCT IF(\nDATE_FORMAT(applications.contract_cancel_date, '%Y%m') = DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m'),\nIFNULL(applications.forced_contract_cancel_amount, 0), 0\n)) AS forced_cancel_amount_total,\nSUM(\n(loan_schedules.total_amount - loan_schedules.amount_paid)\n* (IFNULL(applications.sub_total_amount, 0) / NULLIF(applications.total_amount, 0))\n) AS remaining_principal_estimated from `loan_schedules` left join `loan_arrears` on `loan_arrears`.`loan_schedule_id` = `loan_schedules`.`id` and `loan_arrears`.`del_flag` = 0 left join `applications` on `loan_schedules`.`application_id` = `applications`.`id` and `applications`.`del_flag` = 0 where DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') >= '\\'202504\\'' and DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') <= '\\'202507\\'' and `loan_schedules`.`del_flag` = '\\'0\\'' group by DATE_FORMAT(loan_schedules.payment_plan_date, '%Y%m') order by `payment_month` asc", "type": "query", "params": [], "bindings": [0, 0, "'202504'", "'202507'", "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 689}, {"index": 16, "namespace": null, "name": "app/Services/BalanceService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\BalanceService.php", "line": 12}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Balance/TableDataList.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Balance\\TableDataList.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.5625901, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "LoanScheduleRepository.php:689", "source": {"index": 15, "namespace": null, "name": "app/Repositories/LoanScheduleRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\LoanScheduleRepository.php", "line": 689}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FLoanScheduleRepository.php&line=689", "ajax": false, "filename": "LoanScheduleRepository.php", "line": "689"}, "connection": "local-ladybird", "explain": null, "start_percent": 81.876, "width_percent": 18.124}]}, "models": {"data": {"App\\Models\\Brand": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Shop": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FShop.php&line=1", "ajax": false, "filename": "Shop.php", "line": "?"}}, "App\\Models\\LoanSchedule": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FLoanSchedule.php&line=1", "ajax": false, "filename": "LoanSchedule.php", "line": "?"}}, "App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 27, "is_counter": true}, "livewire": {"data": {"admin.balance.search #1Ul2OAzrlTSQ4ainwgb6": "array:4 [\n  \"data\" => array:7 [\n    \"authType\" => \"\"\n    \"brandId\" => \"\"\n    \"storeId\" => \"\"\n    \"from\" => \"2025年4月\"\n    \"to\" => \"2025年7月\"\n    \"stores\" => Illuminate\\Database\\Eloquent\\Collection {#899\n      #items: array:2 [\n        0 => App\\Models\\Shop {#898\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Shop {#897\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"brands\" => Illuminate\\Database\\Eloquent\\Collection {#917\n      #items: array:10 [\n        0 => App\\Models\\Brand {#916\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Brand {#915\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        2 => App\\Models\\Brand {#914\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        3 => App\\Models\\Brand {#913\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        4 => App\\Models\\Brand {#912\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        5 => App\\Models\\Brand {#911\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        6 => App\\Models\\Brand {#910\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        7 => App\\Models\\Brand {#909\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        8 => App\\Models\\Brand {#908\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        9 => App\\Models\\Brand {#907\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"admin.balance.search\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0ceb4ce65af6e605135ffdc5d4e76fb6.php:8$478f\"\n  \"id\" => \"1Ul2OAzrlTSQ4ainwgb6\"\n]", "admin.balance.table-data-list #1wFY1vXaOZjZdKTgonsH": "array:4 [\n  \"data\" => array:8 [\n    \"authType\" => \"\"\n    \"from\" => \"2025年4月\"\n    \"to\" => \"2025年7月\"\n    \"brandId\" => \"\"\n    \"storeId\" => \"\"\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.balance.table-data-list\"\n  \"component\" => \"App\\Livewire\\Admin\\Balance\\TableDataList\"\n  \"id\" => \"1wFY1vXaOZjZdKTgonsH\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01JZS1JBHZFDTZ13VGC4DEP8FB&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Volt\\Component@anonymous\u0000C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0ceb4ce65af6e605135ffdc5d4e76fb6.php:8$478f@search", "middleware": "web", "duration": "243ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1874858174 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1874858174\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1598875721 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"695 characters\">{&quot;data&quot;:{&quot;authType&quot;:&quot;&quot;,&quot;brandId&quot;:&quot;&quot;,&quot;storeId&quot;:&quot;&quot;,&quot;from&quot;:&quot;2025\\u5e747\\u6708&quot;,&quot;to&quot;:&quot;2025\\u5e747\\u6708&quot;,&quot;stores&quot;:[null,{&quot;keys&quot;:[1,2],&quot;class&quot;:&quot;Illuminate\\\\Database\\\\Eloquent\\\\Collection&quot;,&quot;modelClass&quot;:&quot;App\\\\Models\\\\Shop&quot;,&quot;s&quot;:&quot;elcln&quot;}],&quot;brands&quot;:[null,{&quot;keys&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;class&quot;:&quot;Illuminate\\\\Database\\\\Eloquent\\\\Collection&quot;,&quot;modelClass&quot;:&quot;App\\\\Models\\\\Brand&quot;,&quot;s&quot;:&quot;elcln&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;1Ul2OAzrlTSQ4ainwgb6&quot;,&quot;name&quot;:&quot;admin.balance.search&quot;,&quot;path&quot;:&quot;management\\/balances&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-1147524950-0&quot;:[&quot;div&quot;,&quot;1wFY1vXaOZjZdKTgonsH&quot;]},&quot;scripts&quot;:[&quot;1147524950-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;0fc2fc0ae26263487df3551a3cf49fdfc52260e1bf7b3fe9a938e17ae92c9d6e&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => \"<span class=sf-dump-str title=\"7 characters\">2025&#24180;4&#26376;</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">search</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"494 characters\">{&quot;data&quot;:{&quot;authType&quot;:&quot;&quot;,&quot;from&quot;:&quot;2025\\u5e747\\u6708&quot;,&quot;to&quot;:&quot;2025\\u5e747\\u6708&quot;,&quot;brandId&quot;:&quot;&quot;,&quot;storeId&quot;:&quot;&quot;,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;1wFY1vXaOZjZdKTgonsH&quot;,&quot;name&quot;:&quot;admin.balance.table-data-list&quot;,&quot;path&quot;:&quot;management\\/balances&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:true,&quot;props&quot;:[&quot;authType&quot;,&quot;from&quot;,&quot;to&quot;,&quot;brandId&quot;,&quot;storeId&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;18922585a754c7f3139079d35eafc3a2d378b7f19f749eb70c29f9dc38dd017d&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1598875721\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1226443184 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1604</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/management/balances</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjliemhKQ2NSQmVpOWwrb2VBQnhKaVE9PSIsInZhbHVlIjoiSXhDNmJqdmtHU3Uvbm8ybWVHYzdoSFU1Ym1qZWF0ai9lRnlRb3JiMDFISVQ4N0dYTFJkcUNZL1M5OVJiVmRhaTJwclM3SXNjOStSRnpFZzcvb3BoUkY5RHZHQTZkdkJ1czZUaGNBZmQ5S0hpZEFrUGp5MTlJSU1aWnNUNWRtVjVvQTRLK1M4NG5JaHBpUzRWdE1vc2tBPT0iLCJtYWMiOiI0YzdhMTM3MWFlNWUxODUwNTJiOWM3NDkzMWU5NDY2NGI5MjljYmU0ZTgxN2QxYjVkYmY1OTg1ZTY5YmM5ZTJkIiwidGFnIjoiIn0%3D; ladybird_session=wlXlRruX2PfaGJavVPNjRis2N3hxByorNqLQ96CL; XSRF-TOKEN=eyJpdiI6Im4wdG90dFgyUytnNW5uTWxVVGRSUnc9PSIsInZhbHVlIjoiNUxXWEV6QWY3bnQ3aG02RkRQYzhOZnJSN00yb0Q1cXNDNEZ2ZldheEM3RFZtZVZzQmZnYVVYcEp5QkhDYUVBSTJFcVp0ekJGY0Y4bE5hZ3VtUi9TY21YdTJRek9ScDFPRTNWU0xOUlZ2TjhpSjYvSis3Zld1VXdabHlBcGR0NzIiLCJtYWMiOiI1NWI3ZDIxM2YyOTFmMWVkMzAwNzk1ZGY3YjgxY2EwZTg3MDQxMTc2MTVmNWVmNDJiMmY4OWE5NDZlZTFiYjNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226443184\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-730065614 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730065614\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-172494592 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 02:10:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172494592\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1306963355 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01JZS1JBHZFDTZ13VGC4DEP8FB&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1306963355\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}