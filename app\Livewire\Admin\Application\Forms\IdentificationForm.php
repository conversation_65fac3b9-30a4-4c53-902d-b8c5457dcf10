<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\ConfirmationDocFlagEnum;
use App\Livewire\Base\BaseAdminForm;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

class IdentificationForm extends BaseAdminForm
{
    public $confirmation_doc_flag = ConfirmationDocFlagEnum::DRIVER_LICENSE;

    public $confirmation_doc_other;

    public $file_url;

    public $files;


    public function rules()
    {
        $ruleDocOther = $this->confirmation_doc_flag == ConfirmationDocFlagEnum::OTHER ? 'required' : 'nullable';
        return [
            'confirmation_doc_flag' => ['required', Rule::in(ConfirmationDocFlagEnum::getValues())],
            'confirmation_doc_other' => $ruleDocOther . '|string|max:512',
            'file_url' => 'nullable|url|max:512',
            'files' => 'nullable|array',
            'files.*' => [
                'file',
                'max:20480', // 20MB
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $originalName = $value->getClientOriginalName();
                        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
                      
                        if (!in_array($extension, ['pdf', 'jpeg', 'png'])) {
                            $fail(__('validation.file_exts'));
                        }
                    }
                }
            ],
        ];
    }

    public function getValidationAttributes()
    {
        return __('models.identification.attributes');
    }
}
