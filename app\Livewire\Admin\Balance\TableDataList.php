<?php

namespace App\Livewire\Admin\Balance;

use App\Livewire\Base\BaseDataListComponent;
use App\Services\BalanceService;
use App\Services\CsvService;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;

class TableDataList extends BaseDataListComponent
{
    #[Reactive]
    public $authType;

    #[Reactive]
    public $from;

    #[Reactive]
    public $to;

    #[Reactive]
    public $brandId;

    #[Reactive]
    public $storeId;

    #[On('export_csv')]
    public function downloadCSV()
    {
        $params = [
            'auth_type' => $this->authType,
            'from' => $this->from,
            'to' => $this->to,
            'brand_id' => $this->brandId,
            'store_id' => $this->storeId
        ];
        $dataList = app(BalanceService::class)->getListBalances($params);
        $headers = getConfig('csv.balances.header');
        $filename = getConfig('csv.balances.filename') . '.csv';
        
        return app(CsvService::class)->exportCsvBalance($filename, $headers, $dataList);
    }

    public function render()
    {
        $params = [
            'auth_type' => $this->authType,
            'from' => $this->from,
            'to' => $this->to,
            'brand_id' => $this->brandId,
            'store_id' => $this->storeId
        ];
        $dataList = app(BalanceService::class)->getListBalances($params);
        $total = $dataList->reduce(function ($carry, $item) {
            foreach ($item->toArray() as $key => $value) {
                if (is_numeric($value)) {
                    $carry[$key] = ($carry[$key] ?? 0) + $value;
                }
            }
            return $carry;
        }, []);
        return $this->viewLivewireAdmin('balance.table-data-list', [
            'dataList' => $dataList,
            'total' => $total,
        ]);
    }
}
