<?php

namespace App\Services;

use App\Repositories\LoanPaymentRepository;
use App\Repositories\LoanTransactionLogRepository;
use App\Repositories\LoanWithdrawalRepository;

class LoanWithdrawalService extends CustomService
{
    public function __construct(
        public LoanWithdrawalRepository $loanWithdrawalRepository,
    ) {
        parent::__construct();
    }

    public function getLoanWithdrawalsWithLogsByApplicationId($applicationId)
    {
        return $this->loanWithdrawalRepository->getLoanWithdrawalsWithLogsByApplicationId($applicationId);
    }

    public function getWithdrawal($id){
        return $this->loanWithdrawalRepository->find($id);
    }
}
