<?php

namespace App\Models\Presenters;

use Carbon\Carbon;

trait PBalance
{
    public function convertBalanceToCsvRow(): array
    {
        return [
            $this->payment_month ? Carbon::createFromFormat('Ym', $this->payment_month)->format('Y.m') : '',
            '¥ ' . number_format(data_get($this->attributes, 'loan_balance')),
            '¥ ' . number_format(data_get($this->attributes, 'remaining_principal_estimated')),
            '¥ ' . number_format(data_get($this->attributes, 'paid_amount')),
            '¥ ' . number_format(data_get($this->attributes, 'principal_total')),
            '¥ ' . number_format(data_get($this->attributes, 'fee_total')),
            '¥ ' . number_format(data_get($this->attributes, 'delay_damage_total')),
            '¥ ' . number_format(data_get($this->attributes, 'reissue_fee_total')),
            '¥ ' . number_format(data_get($this->attributes, 'current_month_contract_total')),
            '¥ ' . number_format(data_get($this->attributes, 'current_month_principal_total')),
            '¥ ' . number_format(data_get($this->attributes, 'cancel_amount_total')),
            '¥ ' . number_format(data_get($this->attributes, 'forced_cancel_amount_total')),
        ];
    }
}
