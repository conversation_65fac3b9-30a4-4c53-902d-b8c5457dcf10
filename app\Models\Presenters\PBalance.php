<?php

namespace App\Models\Presenters;

use Carbon\Carbon;

trait PBalance
{
    public function convertBalanceToCsvRow(): array {
        return [
            $this->payment_month ? Carbon::createFromFormat('Ym', $this->payment_month)->format('Y.m') : '',
            formatCurrency($this->loan_balance) ?? '',
            formatCurrency($this->remaining_principal_estimated) ?? '',
            formatCurrency($this->paid_amount) ?? '',
            formatCurrency($this->principal_total) ?? '',
            formatCurrency($this->fee_total) ?? '',
            formatCurrency($this->delay_damage_total) ?? '',
            formatCurrency($this->reissue_fee_total) ?? '',
            formatCurrency($this->current_month_contract_total) ?? '',
            formatCurrency($this->current_month_principal_total) ?? '',
            formatCurrency($this->cancel_amount_total) ?? '',
            formatCurrency($this->forced_cancel_amount_total) ?? '',
        ];
    }
}
