<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Admin\BaseAdminController;
use App\Services\ApplicationService;
use App\Services\ToastService;
use Illuminate\Http\Response;

class ApplicationExportPDFController extends BaseAdminController
{
    protected $applicationService;

    public function __construct(ApplicationService $applicationService)
    {
        $this->applicationService = $applicationService;
        parent::__construct();
    }

    public function export($applicationId)
    {
        try {
            $pdfUrl = $this->applicationService->exportPDF($applicationId);

            if($pdfUrl) {
                return response()->json(['url' => $pdfUrl]);
            };

            return response()->json(['url' => ''], Response::HTTP_NOT_FOUND);
        } catch (\Throwable $e) {
            logError($e);
            return response()->json(['url' => ''], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
