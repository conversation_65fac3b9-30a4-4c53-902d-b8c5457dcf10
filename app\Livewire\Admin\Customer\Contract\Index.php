<?php

namespace App\Livewire\Admin\Customer\Contract;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\ApplicationCourseService;
use App\Services\ApplicationService;
use App\Services\CustomerService;
use App\Services\ToastService;

class Index extends BaseAdminPageComponent
{
    public $customer_id;
    public $isCanDelete;
    public $customer;
    protected $applications = null;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CUSTOMER;
        parent::__construct();
    }

    public function mount($customer_id)
    {
        $this->customer_id = $customer_id;
        $this->customer = app(CustomerService::class)->getCustomer($customer_id);
        $this->applications = app()->make(ApplicationService::class)->getApplicationsForContractByCustomer($customer_id);

        if (empty($this->customer)) {
            app(ToastService::class)->error(__('messages.no_data'));
            return $this->redirect(route('admin.customer.index'));
        }

        $countApp = app(ApplicationService::class)->getCountAppByCustomerId($this->customer_id);
        $this->isCanDelete = !$countApp;
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.contract.index', [
            'applications' => $this->applications,
        ]);
    }
}
