<?php

namespace App\Livewire\Admin\Payment\Forms;
use App\Livewire\Base\BaseAdminForm;

class RegistrationForm extends BaseAdminForm
{
    public $payment_date;
    public $comment = '';

    public function rules()
    {
        $rules = [
            'payment_date' => 'required|date',
            'comment' => 'nullable|max:1000',
        ];

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.registration.attributes');
    }
}
