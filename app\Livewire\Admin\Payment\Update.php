<?php

namespace App\Livewire\Admin\Contract;

use App\Enums\ContractStatusEnum;
use App\Enums\ReInvoiceDocTargetFlagEnum;
use App\Enums\SidebarMenuEnum;
use App\Enums\TypeEnum;
use App\Livewire\Admin\Contract\Forms\UpdateForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\CustomerRepository;
use App\Services\ApplicationService;
use App\Services\ShopBrandService;
use App\Services\CustomerService;
use App\Services\ContractService;
use App\Services\HolidayService;
use App\Services\CourseService;
use App\Services\ApplicationCourseService;
use Gate;
use Livewire\Attributes\On;
use App\Services\ToastService;


class Update extends BaseAdminPageComponent
{
    public UpdateForm $updateForm;

    public $id;
    public $isCanDelete;
    public $selectedHoliday;
    public $contract = null;
    public $productGeneral = [
        [
            'course_id' => '',
            'count' => 0,
            'amount' => '',
        ]
    ];

    public $productOptional = [
        [
            'course_id' => '',
            'count' => 0,
            'amount' => '',
        ]
    ];

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CONTRACT;
        parent::__construct();
        $subTitle = trans2('screens.contract.edit.page_title');
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle = $this->pageTitle . $subTitle . $suffixTitle;
        $this->dispatch('init-select2');
    }

    public function mount($id)
    {
        $this->id = $id;

        $this->selectedHoliday = app(HolidayService::class)->getAllHolidays();

        $this->contract = app()->make(ApplicationService::class)->getContractByApplication($id);

        if (empty($this->contract)) {
            app(ToastService::class)->error(__('messages.no_data'));
            return $this->redirect(route('admin.contract.index'));
        }

        $this->updateForm->fill($this->contract->toArray());
        $this->updateForm->information_input_flag = data_get($this->contract, 'customer.information_input_flag.value');
        $this->updateForm->bank_account_name = $this->contract->customer->bank_account_name;
        $this->updateForm->bank_account_name_kana = $this->contract->customer->bank_account_name_kana;
        $this->updateForm->bank_flag = data_get($this->contract, 'customer.bank_flag.value');
        $this->updateForm->bank_account_mark1 = $this->contract->customer->bank_account_mark1;
        $this->updateForm->bank_account_mark2 = $this->contract->customer->bank_account_mark2;
        $this->updateForm->bank_account_mark3 = $this->contract->customer->bank_account_mark3;
        $this->updateForm->bank_account_number = $this->contract->customer->bank_account_number;
        $this->updateForm->bank_code = $this->contract->customer->bank_code;
        $this->updateForm->bank_name = $this->contract->customer->bank_name;
        $this->updateForm->branch_code = $this->contract->customer->branch_code;
        $this->updateForm->branch_name = $this->contract->customer->branch_name;
        $this->updateForm->bank_account_type = data_get($this->contract, 'customer.bank_account_type.value');
        $this->updateForm->contract_comment = $this->contract->contract_comment;
        $general = app(ApplicationCourseService::class)->getCoursesByTypeAndApplication($this->id, TypeEnum::NORMAL);
        $optional = app(ApplicationCourseService::class)->getCoursesByTypeAndApplication($this->id, TypeEnum::OPTIONAL);
        $this->updateForm->product_general = $general->map(function ($item) {
            return [
                'course_id' => $item->course_id,
                'count' => $item->count,
                'amount' => $item->amount,
            ];
        })->toArray();
        $this->productGeneral += $general->map(function ($item) {
            return [
                'course_id' => $item->course_id,
                'count' => $item->count,
                'amount' => $item->amount,
            ];
        })->toArray();
        $this->updateForm->product_optional = $optional->map(function ($item) {
            return [
                'course_id' => $item->course_id,
                'count' => $item->count,
                'amount' => $item->amount,
            ];
        })->toArray();
        $this->productOptional += $optional->map(function ($item) {
            return [
                'course_id' => $item->course_id,
                'count' => $item->count,
                'amount' => $item->amount,
            ];
        })->toArray();
        if (data_get($this->contract, 'contract_status.value') == ContractStatusEnum::REQUEST_CANCELLATION) {
            $this->updateForm->isCheckContractStatus5 = true;
        }

        if (data_get($this->contract, 're_invoice_doc_target_flag.value') == ReInvoiceDocTargetFlagEnum::ELIGIBLE) {
            $this->updateForm->isCheckReInvoiceDocTargetFlag1 = true;
        }

        $this->isCanDelete = $this->isCanDelete();
    }

    protected function isCanDelete()
    {
        if ($this->contract->contract_status) {
            return in_array($this->contract->contract_status->value, ContractStatusEnum::canDeleteStatus());
        }
        return true;
    }

    public function validateUpdate()
    {

        $this->updateForm->validate();

        $this->setDataEditConfirmModal(getConstant('SCREENS.CONTRACT'), 'contract-update');
    }

    #[On('contract-update')]
    public function update()
    {
        try {
            $params = $this->updateForm->all();

            $customer = app(CustomerService::class)->getCustomer($this->contract->customer_id);
            $application = app(ApplicationService::class)->getApplication($this->id);
            $customer->snapshotForTracking();

            if (empty($customer) || empty($application)) {
                app(ToastService::class)->error(__('messages.no_data'));
                $this->redirect(route('admin.contract.index'));
                return;
            }

            $update = app(ContractService::class)->updateContract($this->id, $this->contract->customer_id, $params);

            if ($update) {
                app(ToastService::class)->updateSuccess();
            } else {
                app(ToastService::class)->updateError();
            }

            return redirect()->route('admin.contract.details', $this->id);
        } catch (\Throwable $th) {
            logError($th);
            return redirect()->route('admin.contract.details', $this->id);
        }
    }

    public function render()
    {
        $this->courseGeneralList = app()->make(CourseService::class)->getAllCourseByType(TypeEnum::NORMAL, $this->contract?->fee_type->value);
        $this->courseOptionalList = app()->make(CourseService::class)->getAllCourseByType(TypeEnum::OPTIONAL, $this->contract?->fee_type->value);
        return $this->viewLivewireAdmin('contract.detail.update', [
            'contract' => $this->contract,
            'shopBrands' => app(ShopBrandService::class)->getAllShopBrands(),
            'courseGeneralList' => $this->courseGeneralList,
            'courseOptionalList' => $this->courseOptionalList,
        ]);
    }

    public function addGeneral()
    {
        $this->productGeneral[] = [
            'course_id' => '',
            'count' => 0,
            'amount' => '',
        ];
    }

    public function removeGeneral($index)
    {
        unset($this->productGeneral[$index]);
        unset($this->updateForm->product_general[$index]);
    }

    public function addOptional()
    {
        $this->productOptional[] = [
            'course_id' => '',
            'count' => 0,
            'amount' => '',
        ];
    }

    public function removeOptional($index)
    {
        unset($this->productOptional[$index]);
        unset($this->updateForm->product_optional[$index]);
    }

    public function incrementQtyGeneral($index)
    {
        if (isset($this->productGeneral[$index]['count'])) {
            $this->productGeneral[$index]['count'] += 1;
            $this->updateForm->product_general[$index]['count'] = $this->productGeneral[$index]['count'];
        }
    }

    public function decrementQtyGeneral($index)
    {
        if (isset($this->productGeneral[$index]['count'])) {
            $this->productGeneral[$index]['count'] -= 1;
            $this->updateForm->product_general[$index]['count'] = $this->productGeneral[$index]['count'];
        }
    }

    public function incrementQtyOptional($index)
    {
        if (isset($this->productOptional[$index]['count'])) {
            $this->productOptional[$index]['count'] += 1;
            $this->updateForm->product_optional[$index]['count'] = $this->productOptional[$index]['count'];
        }
    }

    public function decrementQtyOptional($index)
    {
        if (isset($this->productOptional[$index]['count'])) {
            $this->productOptional[$index]['count'] -= 1;
            $this->updateForm->product_optional[$index]['count'] = $this->productOptional[$index]['count'];
        }
    }
}
