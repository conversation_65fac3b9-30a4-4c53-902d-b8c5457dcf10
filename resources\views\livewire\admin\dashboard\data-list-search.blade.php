<?php

use function Livewire\Volt\{state};
use function Livewire\Volt\{computed};
use App\Services\ShopService;
use App\Services\ShopBrandService;
use App\Enums\AuthTypeEnum;
use App\Repositories\BrandRepository;
use App\Repositories\ShopRepository;

state([
    'authType' => getCurrentUser()->isBrand() ? AuthTypeEnum::BRAND : '',
    'brandId' => '',
    'storeId' => '',
    'from' => '',
    'to' => '',
    'stores' => app()->make(ShopRepository::class)->getShopsByAdmin() ?? [],
    'brands' => app()->make(BrandRepository::class)->getBrandsByAdmin() ?? [],
]);

$search = function () {
    $this->dispatch('reset-page');
};
$options = computed(function () {
    $role = getCurrentUser()->auth_type;

    return match ($role->value) {
        AuthTypeEnum::ADMIN => [
            AuthTypeEnum::ADMIN => trans2('screens.dashboard.admin_base'),
            AuthTypeEnum::BRAND => trans2('screens.dashboard.brand_base'),
            AuthTypeEnum::STORE => trans2('screens.dashboard.store_base'),
        ],
        AuthTypeEnum::BRAND => [
            AuthTypeEnum::BRAND => trans2('screens.dashboard.brand_base'),
            AuthTypeEnum::STORE => trans2('screens.dashboard.store_base'),
        ],
        AuthTypeEnum::STORE => [
            AuthTypeEnum::STORE => trans2('screens.dashboard.store_base'),
        ],
        default => [],
    };
});

?>

<!-- search -->
<div class="contents-container">
    <div class="container-fluid">
        <div class="d-flex align-items-center justify-content-between mb-5 setting-form-control">
            <div class="d-flex">
                @if(!getCurrentUser()->isStore())
                    <div class="me-5" wire:ignore>
                        <select name="auth_type" class="form-select2" style="width: 250px" wire:model="authType">
                            @foreach ($this->options as $key => $text)
                                <option value="{{ $key }}">{{ $text }}</option>
                            @endforeach
                        </select>
                    </div>
                @endif

                @if(!getCurrentUser()->isStore())
                    <div style="display: {{ $authType == AuthTypeEnum::STORE ? '' : 'none'}} ">
                        <div class="me-5" wire:ignore>
                            <select name="store_id" class="form-select2" style="width: 250px" wire:model="storeId">
                                <option value="">店舗を選択</option>

                                @foreach($stores as $s)
                                    <option value="{{ $s->id }}">{{ $s->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                @endif

                @if(!getCurrentUser()->isStore())
                    <div style="display: {{ $authType == AuthTypeEnum::BRAND ? '' : 'none'}} ">
                        <div class="me-5" wire:ignore>
                            <select name="brand_id" class="form-select2" style="width: 250px" wire:model="brandId" >
                                <option value="">ブランドを選択</option>

                                @foreach($brands as $b)
                                    <option value="{{ $b->id }}">{{ $b->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                @endif

                <div class="d-flex align-items-center" wire:ignore>
                    <div class="me-3 text-nowrap">{{ trans2('screens.dashboard.display_period') }}</div>
                    <input type="text" name="" value="" class="monthpicker monthpicker-secondary" wire:model="from" readonly >
                    <div class="mx-3">〜</div>
                    <input type="text" name="" value="" class="monthpicker monthpicker-secondary" wire:model="to" readonly>
                </div>
            </div>
            <div class="ms-auto">
                <button type="button" class="btn-csv-download" wire:click.prevent="$dispatch('export_csv')">{{ trans2('button.CSV_download') }}</button>
            </div>
        </div>

        <livewire:admin.dashboard.data-list lazy :$authType :$from :$to :$brandId :$storeId />
    </div>
</div>
@script
    <script>
        document.addEventListener('livewire:initialized', () => {
            initializeMonthpickers();
            initSelect2Binding();
        });

        function initializeMonthpickers() {
            document.querySelectorAll('.monthpicker').forEach(function(el) {
                const currentYear = new Date().getFullYear();
                const currentMonth = (new Date().getMonth() + 1).toString().padStart(2, '0');
                const minYear = currentYear - 200;

                // Set default value if empty
                if (!el.value) {
                    el.value = `${currentYear}年${currentMonth}月`;

                    const model = el.getAttribute('wire:model');
                    if (model) {
                        const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                        
                        setTimeout(() => {
                            Livewire.find(componentId).set(model, el.value);
                        }, 50);
                    }
                }

                $(el).datepicker({
                        dateFormat: 'yy/mm',
                        yearRange: `${minYear}:${currentYear}`,
                        changeYear: true,
                    })
                    .focus(function() {
                        $('.ui-datepicker-calendar').hide();
                        $('#ui-datepicker-div').hide();
                    })
                    .on('change', function() {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');
                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                            Livewire.find(componentId).call('search');
                        }
                    });
            });
        }

        function initSelect2Binding() {

            document.querySelectorAll('select.form-select2').forEach(function(el) {

                if (!$(el).hasClass('select2-hidden-accessible')) {
                    $(el).select2().on('change', function(e) {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');

                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                            console.log(1);

                            Livewire.find(componentId).call('search');

                        }
                    });
                }
            });
        }
    </script>
@endscript
