<div x-data="{ bankFlag: @entangle('accountForm.bank_flag') }">
    <h1 class="application-page-title">6.お客様登録画面 / 口座情報</h1>
    <h3 class="heading-3">口座情報</h3>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">口座名義人<span class="required-icon">必須</span></th>
                <td>
                    <input type="text" name="" value="" class="form-control form-required w-100"
                        wire:model="accountForm.bank_account_name" maxlength="512">
                    @error('accountForm.bank_account_name')
                    <span class="message-error">{{ $message }}</span>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="required">口座名義人（フリガナ）<span class="required-icon">必須</span></th>
                <td>
                    <input type="text" name="" value="" class="form-control form-required w-100"
                        wire:model="accountForm.bank_account_name_kana" maxlength="512">
                    @error('accountForm.bank_account_name_kana')
                    <span class="message-error">{{ $message }}</span>
                    @enderror
                </td>
            </tr>
            <tr>
                <th class="required">金融機関<span class="required-icon">必須</span></th>
                <td>
                    <div class="d-flex">
                        <div class="form-radio me-5">
                            <label>
                                <input type="radio" value="1" x-model.number="bankFlag"
                                    wire:model="accountForm.bank_flag" class="form-radio-input">
                                <span class="form-radio-text">ゆうちょ銀行</span>
                            </label>
                        </div>
                        <div class="form-radio">
                            <label>
                                <input type="radio" value="2" x-model.number="bankFlag"
                                    wire:model="accountForm.bank_flag" class="form-radio-input">
                                <span class="form-radio-text">ゆうちょ銀行以外の金融機関</span>
                            </label>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>

    <div class="banking-institution"
        x-bind:class="bankFlag == 1 ? 'opacity-100 pointer-events-auto' : 'opacity-40 pointer-events-none'" x-cloak>
        <h3 class="heading-3 pt-4">ゆうちょ銀行</h3>
        <table class="table table-edit">
            <tbody>
                <tr>
                    <th class="required">通帳記号<span class="required-icon">必須</span></th>
                    <td>
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <input type="text" name="" value="" class="form-control form-required w-px-240"
                                    wire:model="accountForm.bank_account_mark1">
                            </div>
                            <div class="col-auto px-0">
                                <div class="position-relative">
                                    <span class="position-absolute small" style="left: .35em;">※</span>
                                    <input type="text" name="" value="" class="form-control w-px-70 text-center"
                                        wire:model="accountForm.bank_account_mark2">
                                </div>
                            </div>
                            <div class="col-auto small">※6桁がある場合の6桁目</div>
                        </div>
                        <div>
                            <div>
                                @error('accountForm.bank_account_mark1')
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                @error('accountForm.bank_account_mark2')
                                <span class="message-error">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th class="required">通帳番号<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="" value="" class="form-control form-required w-px-240"
                            wire:model="accountForm.bank_account_number_1">
                        @error('accountForm.bank_account_number_1')
                        <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="small">
            ※通帳記号が1から始まる方は、通帳番号の最後の1は入力しないでください。<br>
            ※通帳記号が0から始まる方は、通帳番号の最後に1がある場合でも省略せず入力してください。<br>
            なお、通帳記号の1桁目の固定の「1」は「0」に読み替え、通帳番号の8行目の固定の「1」は無視します。
        </div>
    </div>

    <div class="banking-institution"
        x-bind:class="bankFlag == 2 ? 'opacity-100 pointer-events-auto' : 'opacity-40 pointer-events-none'" x-cloak>
        <h3 class="heading-3 pt-4">ゆうちょ銀行以外の金融機関</h3>
        <table class="table table-edit">
            <tbody>
                <tr>
                    <th class="required">金融機関コード<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="" value="" class="form-control form-required w-px-240"
                            wire:model="accountForm.bank_code" wire:blur="updateBankNameByCode">
                        @error('accountForm.bank_code')
                        <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
                <tr>
                    <th class="required">金融機関名<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="" value="" class="form-control form-required w-100"
                            wire:model="accountForm.bank_name">
                        @error('accountForm.bank_name')
                        <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
                <tr>
                    <th class="required">支店コード<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="" value="" class="form-control form-required w-px-240"
                            wire:model="accountForm.branch_code" wire:blur="updateBranchNameByCode">
                        @error('accountForm.branch_code')
                        <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
                <tr>
                    <th class="required">支店名<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="" value="" class="form-control form-required w-100"
                            wire:model="accountForm.branch_name">
                        @error('accountForm.branch_name')
                        <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
                <tr>
                    <th class="required">預金種別<span class="required-icon">必須</span></th>
                    <td>
                        <div class="d-flex">
                            <div class="form-radio me-5">
                                <label>
                                    <input type="radio" name="deposit_type" value="1" class="form-radio-input"
                                        wire:model="accountForm.bank_account_type" checked>
                                    <span class="form-radio-text">普通（総合口座）</span>
                                </label>
                            </div>
                            <div class="form-radio">
                                <label>
                                    <input type="radio" name="deposit_type" value="2" class="form-radio-input"
                                        wire:model="accountForm.bank_account_type">
                                    <span class="form-radio-text">当座</span>
                                </label>
                            </div>
                        </div>
                        @error('accountForm.bank_account_type')
                        <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
                <tr>
                    <th class="required">口座番号<span class="required-icon">必須</span></th>
                    <td>
                        <input type="text" name="" value="" class="form-control form-required w-px-240"
                            wire:model="accountForm.bank_account_number_2">
                        @error('accountForm.bank_account_number_2')
                        <span class="message-error">{{ $message }}</span>
                        @enderror
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="pt-5 text-center">
        <button wire:click.prevent="validateSave" class="btn btn-dark btn-large">{{ trans2('button.next') }}</button>
    </div>
</div>
@include('livewire.admin.application.customer.script')