<?php

namespace App\Livewire\Admin\Customer\Application\Detail;

use App\Enums\ApplicationStatusEnum;
use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Services\ApplicationService;
use App\Services\ApplicationServiceInspection;
use App\Services\ToastService;

class Index extends BaseAdminPageComponent
{
    public $customer_id;

    public $customer;

    public $application;

    public $isCanDelete;

    public $application_id;

    public $applicationInspectionStatuses;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CUSTOMER;
        parent::__construct();
    }

    public function mount($customer_id, $application_id)
    {
        $this->customer_id = $customer_id;

        $this->application_id = $application_id;

        $this->customer = app()->make(CustomerRepository::class)->getBasicInfoCustomer($customer_id);
        $this->application = app()->make(ApplicationRepository::class)->getApplicationForDetail($this->application_id);

        if (empty($this->customer || empty($this->application))) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }

        $this->isCanDelete = !app(ApplicationService::class)->getCountAppByCustomerId($this->customer_id);

        // get application inspection statuses
        $this->applicationInspectionStatuses = app(ApplicationServiceInspection::class)->getApplicationInspectionStatuses($this->application_id);
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.application.detail.index', [
            'customer' => $this->customer,
            'application' => $this->application,
            'applicationInspectionStatuses' => $this->applicationInspectionStatuses,
        ]);
    }
}
