<?php

namespace App\Services;

use App\Enums\ApplicationStatusEnum;
use App\Enums\TypeEnum;
use App\Repositories\ApplicationCourseRepository;
use App\Repositories\ShopBrandRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApplicationCourseService extends CustomService
{
    public function __construct(
        public ApplicationCourseRepository $applicationCourseRepository
    ) {
        parent::__construct();
    }

    public function getApplicationCoursesForContractByCustomer($customer_id)
    {
        return $this->applicationCourseRepository->getApplicationCoursesForContractByCustomer($customer_id);
    }

    public function getContract($id)
    {
        return $this->applicationCourseRepository->getContract($id);
    }

    public function getCoursesByTypeAndApplication($application_id, $type)
    {
        return $this->applicationCourseRepository->getCoursesByTypeAndApplication($application_id, $type);
    }

    public function getCoursesByApplication($application_id)
    {
        return $this->applicationCourseRepository->getCoursesByApplication($application_id);
    }
}
