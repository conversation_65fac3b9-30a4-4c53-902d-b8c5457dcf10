<?php

namespace App\Enums;


final class ApplicationTabEnum extends Enum
{
    const BRAND = 'brand';
    const PAYMENT = 'payment';
    const SERVICE = 'service';
    const IDENTIFICATION = 'identification';
    const CUSTOMER = 'customer';
    const CUSTOMER_SETUP = 'setup';
    const CHECK = 'check';
    const COMPLETE = 'complete';

    private static $tabOrder = [
        self::BRAND => 1,
        self::PAYMENT => 2,
        self::SERVICE => 3,
        self::IDENTIFICATION => 4,
        self::CUSTOMER => 5,
        self::CUSTOMER_SETUP => 6,
        self::CHECK => 7,
    ];

    private const CLASS_CURRENT = 'current';
    private const CLASS_COMPLETED = 'completed';
    private const CLASS_DEFAULT = '';

    public static function texts(): array
    {
        return [
            self::BRAND => trans2('ApplicationTabEnum.BRAND'),
            self::PAYMENT => trans2('ApplicationTabEnum.PAYMENT'),
            self::SERVICE => trans2('ApplicationTabEnum.SERVICE'),
            self::IDENTIFICATION => trans2('ApplicationTabEnum.IDENTIFICATION'),
            self::CUSTOMER => trans2('ApplicationTabEnum.CUSTOMER'),
            self::CUSTOMER_SETUP => trans2('ApplicationTabEnum.CUSTOMER_SETUP'),
            self::CHECK => trans2('ApplicationTabEnum.CHECK'),
            self::COMPLETE => trans2('ApplicationTabEnum.COMPLETE'),
        ];
    }

    public static function checkCurrentOrCompleteTab(string $tab): string
    {
        if (!array_key_exists($tab, self::$tabOrder)) {
            return self::CLASS_DEFAULT;
        }

        $currentStep = 0;
        foreach (self::$tabOrder as $tabKey => $step) {
            if (request()->routeIs('admin.customer.application.' . $tabKey . '*')) {
                $currentStep = $step;
                break;
            }
        }

        if (request()->routeIs('admin.customer.application.' . $tab . '*')) {
            return self::CLASS_CURRENT;
        }

        if ($currentStep > 0 && self::$tabOrder[$tab] < $currentStep) {
            return self::CLASS_COMPLETED;
        }

        return self::CLASS_DEFAULT;
    }
}
