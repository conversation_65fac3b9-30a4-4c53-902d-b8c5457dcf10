$(function(){

  $('.select-user-authority').on('select2:select', function (e) {
    const val = $(this).val();
    
    const $row = $('.row-store-charge');
    const $th = $row.find('th');
    const $select = $row.find('select');

    if(val === 'store') {
      const requiredIcon = '<span class="required-icon">必須</span>';
      $th.append(requiredIcon);

      $select.addClass('form-required');
    } else {
      $th.find('.required-icon').remove();
      $select.removeClass('form-required');
    }
    
  });
});
