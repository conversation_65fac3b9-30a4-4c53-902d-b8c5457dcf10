<?php
namespace App\Services;

use App\Enums\DepreciationRateTypeEnum;
use App\Services\PaymentService;
use App\Services\ContractService;
use App\Validators\CsvValidator;
use Illuminate\Support\Facades\DB;

class CsvImportService extends CustomService
{
    public function importNoHeaderCsv($path, $screen)
    {
        switch ($screen){
            case getConstant('SCREENS.PAYMENT'):
                $headerMapping  = getConfig('csv_sample_payment_headers');
                $service = app(PaymentService::class);
                break;
        }

        try {
            $content = file_get_contents($path);
            $encoding = mb_detect_encoding($content, ['UTF-8', 'SJIS'], true);
            if ($encoding === false) {
                $encoding = 'SJIS-win';
            }
            $utf8Content = mb_convert_encoding($content, 'UTF-8', $encoding);
            $lines = array_filter(explode("\n", $utf8Content), 'trim');
            if (empty($lines)) {
                return ['status' => false, 'error' => [__('messages.csv_data_invalid')]];
            }
            $datas = array_map(function ($line) {
                return $this->csvParse($line);
            }, $lines);

            // check validate data import
            $errors = [];
            $dataCsv = [];
            $line = trans2('line');
            foreach ($datas as $rowIndex => $data) {
                if (count($headerMapping) !== count($data)) {
                    $errors[] = "[$line: $rowIndex]" . ": not mapping count";
                    return ['status' => false, 'errors' => $errors];
                }
                $mapped = array_combine(array_keys($headerMapping), $data);

                $validator = new CsvValidator();
                $validator = $validator->validateData($mapped, $screen, $headerMapping);

                if ($validator->fails()) {
                    $validateErrs = $validator->errors()->all();
                    foreach ($validateErrs as $err) {
                        $errors[] = "[$line: $rowIndex]" .$err;
                    }
                }
                $dataCsv[] = $mapped;
            }

            if (!empty($errors)) {
                return ['status' => false, 'errors' => $errors];
            }

            // import data
            DB::beginTransaction();
            foreach (array_chunk($dataCsv, 500) as $datas) {
                foreach ($datas as $data) {
                    $service->handleImportCsv($data);
                }
            }
            DB::commit();
            return ['status' => true, 'success' => __('messages.import_success')];
        } catch (\Throwable $exception) {
            logError($exception);
            DB::rollBack();
            return ['status' => false, 'error' => [__('messages.import_csv_failed')]];
        }
    }

    private function csvParse(string $line) {
        $delimiter = strpos($line, ';') !== false ? ';' : ',';
        return str_getcsv($line, $delimiter);
    }
}
