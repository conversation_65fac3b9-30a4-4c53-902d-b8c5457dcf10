<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\BankAccountTypeEnum;
use App\Enums\BankFlagEnum;
use App\Livewire\Base\BaseAdminForm;
use App\Models\Customer;
use Illuminate\Validation\Rule;

class AccountForm extends BaseAdminForm
{
    public $bank_account_name = '';
    public $bank_account_name_kana = '';
    public $bank_flag = BankFlagEnum::JAPAN;
    public $bank_account_mark1 = '';
    public $bank_account_mark2 = '';
    public $bank_account_number = '';
    public $bank_account_number_1 = '';
    public $bank_account_number_2 = '';
    public $bank_code = '';
    public $bank_name = '';
    public $branch_code = '';
    public $branch_name = '';
    public $bank_account_type = BankAccountTypeEnum::ORDINARY;

    public function rules()
    {
        $rules = [
            'bank_flag' => ['required', Rule::in(BankFlagEnum::getValues())],
            'bank_account_name' => 'required|string|max:512',
            'bank_account_name_kana' => $this->bankAccountKanaValidationRule(),
        ];

        if ($this->bank_flag == BankFlagEnum::JAPAN) {
            $rules += [
                'bank_account_mark1' => 'required|max:32',
                'bank_account_mark2' => 'nullable|max:32',
                'bank_account_number_1' => 'required|max:32',
            ];
        }

        if ($this->bank_flag == BankFlagEnum::OTHER) {
            $rules += [
                'bank_code' => 'required|number|max:32',
                'bank_name' => 'required|string|max:32',
                'branch_code' => 'required|number|max:32',
                'branch_name' => 'required|string|max:32',
                'bank_account_type' => ['required', Rule::in(BankAccountTypeEnum::getValues())],
                'bank_account_number_2' => 'required|max:32',
            ];
        }

        return $rules;
    }

    public function getValidationAttributes()
    {
        return __('models.account.attributes');
    }

    public function bankAccountKanaValidationRule(): array
    {
        return [
            'required',
            'string',
            'max:512',
            function ($attribute, $value, $fail) {
                if (!preg_match('/^[ｱ-ﾝﾞﾟA-Z0-9\(\)\.\-\/ ]+$/u', $value)) {
                    $fail(__('口座名義（フリガナ）には、半角カタカナ／半角英数字（大文字）／記号を入力してください。'));
                }
            },
        ];
    }

    public function fillFromCustomer(Customer $customer): void
    {
        $this->bank_account_name = $customer->bank_account_name ?? '';
        $this->bank_account_name_kana = $customer->bank_account_name_kana ?? '';
        $this->bank_flag = $customer->bank_flag?->value ?? (string) BankFlagEnum::JAPAN;
        $this->bank_account_mark1 = $customer->bank_account_mark1 ?? '';
        $this->bank_account_mark2 = $customer->bank_account_mark2 ?? '';
        $this->bank_code = $customer->bank_code ?? '';
        $this->bank_name = $customer->bank_name ?? '';
        $this->branch_code = $customer->branch_code ?? '';
        $this->branch_name = $customer->branch_name ?? '';
        $this->bank_account_type = $customer->bank_account_type?->value ?? (string) BankAccountTypeEnum::ORDINARY;
        $this->bank_account_number = $customer->bank_account_number ?? '';
        if ($this->bank_flag == BankFlagEnum::JAPAN) {
            $this->bank_account_number_1 = $this->bank_account_number;
        } else {
            $this->bank_account_number_2 = $this->bank_account_number;
        }
    }
}
