<?php

namespace App\Repositories;

use App\Models\LoanPayment;
use App\Models\LoanPaymentAllocation;

class LoanPaymentAllocationRepository extends CustomRepository
{
    protected $model = LoanPaymentAllocation::class;

    public function getMaxLoanScheduleIdByLoanPayment($loanPaymentId)
    {
        return $this->where($this->modelField('loan_payment_id'), $loanPaymentId)->max($this->modelField('loan_schedule_id'));
    }

    public function insertPaymentAllocation($data)
    {
        return  $this->create($data);
    }

    public function getLoanPaymentAllocationsByloanSchedule($loanScheduleId)
    {
        return $this->where($this->modelField('loan_schedule_id'), $loanScheduleId)
            ->where($this->modelField('del_flag'), '0')
            ->get();
    }

    public function countLoanPaymentAllocations($loanPaymentId, $loanScheduleId)
    {
        return $this->where($this->modelField('loan_payment_id'), $loanPaymentId)
            ->where($this->modelField('loan_schedule_id'), '!=', $loanScheduleId)
            ->where($this->modelField('del_flag'), '0')
            ->count();
    }

    public function getAllByLoanPaymentId($loanPaymentId)
    {
        return $this->select([
            $this->modelField('id'),
            $this->modelField('loan_schedule_id'),
            $this->modelField('before_payment_status'),
            $this->modelField('amount'),
        ])->where($this->modelField('loan_payment_id'), $loanPaymentId)->get();
    }
}
