<?php

namespace App\Enums;

class PaymentCompanyFlagEnum extends Enum
{
    const JACCS = 1;
    const APLUS = 2;
    const MIZUHO_FACTOR = 3;
    const OTHER = 9;

    public static function texts(): array{
        return [
            self::JACCS => trans2('PaymentCompanyFlagEnum.JACCS'),
            self::APLUS => trans2('PaymentCompanyFlagEnum.APLUS'),
            self::MIZUHO_FACTOR => trans2('PaymentCompanyFlagEnum.MIZUHO_FACTOR'),
            self::OTHER => trans2('Enum.OTHER'),
        ];
    }
}
