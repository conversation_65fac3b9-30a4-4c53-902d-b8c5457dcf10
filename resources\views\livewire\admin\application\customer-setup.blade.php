<div>
    <h1 class="application-page-title">6.お客様登録画面 / 基本情報</h1>
    <table class="table table-edit">
        <tbody>
            <tr>
                <th class="required">お名前<span class="required-icon">必須</span></th>
                <td>
                    <div class="row">
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control form-required" placeholder="姓"
                                wire:model="basicForm.last_name" maxlength="128">
                            <div>
                                @error('basicForm.last_name') <span class="message-error">{{ $message }}</span>@enderror
                            </div>
                        </div>
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control form-required" placeholder="名"
                                wire:model="basicForm.first_name" maxlength="128">
                            <div>
                                @error('basicForm.first_name')
                                <span class="message-error">{{ $message}}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">お名前（フリガナ）<span class="required-icon">必須</span></th>
                <td>
                    <div class="row">
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control form-required" placeholder="セイ"
                                wire:model="basicForm.last_name_kana" maxlength="128">
                            <div>
                                @error('basicForm.last_name_kana') <span class="message-error">{{ $message
                                    }}</span>@enderror
                            </div>
                        </div>
                        <div class="col-6">
                            <input type="text" name="" value="" class="form-control form-required" placeholder="メイ"
                                wire:model="basicForm.first_name_kana" maxlength="128">
                            <div>
                                @error('basicForm.first_name_kana')
                                <span class="message-error">{{ $message}}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">性別<span class="required-icon">必須</span></th>
                <td>
                    <div class="d-flex">
                        <div class="form-radio me-5">
                            <label>
                                <input type="radio" wire:model="basicForm.sex" name="sex" value="1"
                                    class="form-radio-input">
                                <span class="form-radio-text">{{ trans2('SexEnum.FEMALE') }}</span>
                            </label>
                        </div>
                        <div class="form-radio me-5">
                            <label>
                                <input type="radio" wire:model="basicForm.sex" name="sex" value="2"
                                    class="form-radio-input">
                                <span class="form-radio-text">{{ trans2('SexEnum.MALE') }}</span>
                            </label>
                        </div>
                        <div class="form-radio">
                            <label>
                                <input type="radio" wire:model="basicForm.sex" name="sex" value="3"
                                    class="form-radio-input">
                                <span class="form-radio-text">{{ trans2('Enum.OTHER') }}</span>
                            </label>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">誕生日<span class="required-icon">必須</span></th>
                <td>
                    <input type="text" name="" value="" class="form-control datepicker form-required"
                        wire:model="basicForm.birthday">
                    <div>
                        @error('basicForm.birthday')
                        <span class="message-error">{{ $message}}</span>
                        @enderror
                    </div>
                </td>

            </tr>
            <tr>
                <th>メールアドレス</th>
                <td>
                    <input type="email" name="" value="" class="form-control" wire:model="basicForm.email">
                    <div>
                        @error('basicForm.email')
                        <span class="message-error">{{ $message}}</span>
                        @enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">電話番号<span class="required-icon">必須</span></th>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <input type="text" name="" value="" class="form-control form-required w-px-150"
                                wire:model="basicForm.tel1" maxlength="32">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="" value="" class="form-control form-required w-px-150"
                                wire:model="basicForm.tel2" maxlength="32">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="" value="" class="form-control form-required w-px-150"
                                wire:model="basicForm.tel3" maxlength="32">
                        </div>
                    </div>
                    <div>
                        <div>
                            @error('basicForm.tel1') <span class="message-error">{{ $message }}</span>@enderror
                        </div>
                        <div>
                            @error('basicForm.tel2') <span class="message-error">{{ $message }}</span>@enderror
                        </div>
                        <div>
                            @error('basicForm.tel3') <span class="message-error">{{ $message }}</span>@enderror
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">郵便番号<span class="required-icon">必須</span></th>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <input type="text" name="zip1" value="" class="form-control form-required w-px-150"
                                maxlength="4" wire:model="basicForm.zip1">
                        </div>
                        <div class="mx-2">-</div>
                        <div>
                            <input type="text" name="zip2" value="" class="form-control form-required w-px-150"
                                maxlength="4" wire:model="basicForm.zip2">
                        </div>
                    </div>
                    <div>
                        <div>
                            @error('basicForm.zip1') <span class="message-error">{{ $message }}</span>@enderror
                        </div>
                        <div>
                            @error('basicForm.zip2') <span class="message-error">{{ $message }}</span>@enderror
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">都道府県<span class="required-icon">必須</span></th>
                <td>
                    <div wire:ignore>
                        <select name="pref_id" class="form-select2 form-required" wire:model="basicForm.pref_id"
                            style="width:280px;" data-placeholder="{{ trans2('select_default') }}">
                            <option value=""></option>
                            @foreach ($listPrefs as $key => $pref)
                            <option value="{{ data_get($pref,'id') }}">{{ data_get($pref,'name') }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        @error('basicForm.pref_id') <span class="message-error">{{ $message }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">市区町村<span class="required-icon">必須</span></th>
                <td>
                    <input type="text" name="city" value="" class="form-control form-required"
                        wire:model="basicForm.city" maxlength="1000">
                    <div>
                        @error('basicForm.city') <span class="message-error">{{ $message }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">町名・番地<span class="required-icon">必須</span></th>
                <td>
                    <input type="text" name="address" value="" class="form-control form-required"
                        wire:model="basicForm.address" maxlength="1000">
                    <div>
                        @error('basicForm.address') <span class="message-error">{{ $message }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th>建物名・部屋番号</th>
                <td>
                    <input type="text" name="building" value="" class="form-control" wire:model="basicForm.building"
                        maxlength="1000">
                    <div>
                        @error('basicForm.building') <span class="message-error">{{ $message }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">都道府県カナ<span class="required-icon">必須</span></th>
                <td>
                    <div wire:ignore>
                        <select name="pref_kana_id" class="form-select2 form-required"
                            wire:model="basicForm.pref_kana_id" style="width:280px;"
                            data-placeholder="{{ trans2('select_default') }}">
                            <option value=""></option>
                            @foreach ($listPrefs as $key => $pref)
                            <option value="{{ data_get($pref,'id') }}">{{ data_get($pref,'name_kana') }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        @error('basicForm.pref_kana_id') <span class="message-error">{{ $message }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">市区町村カナ<span class="required-icon">必須</span></th>
                <td>
                    <input type="text" name="city_kana" value="" class="form-control form-required"
                        wire:model="basicForm.city_kana" maxlength="1000">
                    <div>
                        @error('basicForm.city_kana') <span class="message-error">{{ $message }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th class="required">番地カナ<span class="required-icon">必須</span></th>
                <td>
                    <input type="text" name="address_kana" value="" class="form-control form-required"
                        wire:model="basicForm.address_kana" maxlength="1000">
                    <div>
                        @error('basicForm.address_kana') <span class="message-error">{{ $message }}</span>@enderror
                    </div>
                </td>
            </tr>
            <tr>
                <th>建物名・部屋番号カナ</th>
                <td>
                    <input type="text" name="building_kana" value="" class="form-control"
                        wire:model="basicForm.building_kana" maxlength="1000">
                    <div>
                        @error('basicForm.building_kana') <span class="message-error">{{ $message }}</span>@enderror
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <div class="pt-5 text-center">
        <button wire:click.prevent="validateSave" class="btn btn-dark btn-large">{{ trans2('button.next') }}</button>
    </div>
</div>
@include('livewire.admin.application.customer.script')
