$(function(){
  if(document.getElementById('confirmEntryCustomerModal')) {
    const confirmEntryCustomerModal = new bootstrap.Modal(document.getElementById('confirmEntryCustomerModal'));
    $('.new-customer-btn').on('click', function (e) {
      e.preventDefault();
      confirmEntryCustomerModal.show();
    });
    $('.customer-list-table tbody td').on('click', function (e) {
      e.preventDefault();
      confirmEntryCustomerModal.show();
    });
  }

  const bankingInstitution = $('.select-banking-institution:checked').val();
  activeBankingInstitution(bankingInstitution);

  $('.select-banking-institution').on('change', function () {
    const name = $(this).attr('name');
    const val = $('[name="'+name+'"]:checked').val();
    activeBankingInstitution(val);
  });

  function activeBankingInstitution(type) {
    $('.banking-institution').each(function() {
      const bank_type = $(this).data('bank-type');
      if(bank_type == type) {
        $(this).removeAttr("style");
      } else {
        $(this).css({
          'opacity': .4,
          'pointer-events': 'none'
        })
      }
    });
  }

  const requiredBgColor = '#FFEFE9';
  function setRequiredBgColor(el) {
    el.find('.form-control.form-required').on('keydown keyup keypress change focus blur', function(){
      if($(this).val() == ''){
          $(this).css({backgroundColor: requiredBgColor});
      } else {
          $(this).css({backgroundColor:'#fff'});
      }
    }).change();
  }

  // 財務状況・勤務先情報 本人年収
  if(document.getElementById('inputIncome')) {
    const inputIncomeElement = document.getElementById('inputIncome');
    inputIncomeElement.addEventListener('blur', function() {
      let rawValue = inputIncome.value;
      let numericValue = rawValue.replace(/,/g, '');
      const income = Number(numericValue);

      const $row = $('.check-required-row');
      const $th = $row.find('th');
      const $input = $row.find('input');
      const $select = $row.find('select');

      if(income > 0) {
        const requiredIcon = '<span class="required-icon">必須</span>';
        $th.append(requiredIcon);
        $select.addClass('form-required');
        $input.each(function() {
          if($(this).val() == ''){
            $(this).css({backgroundColor: requiredBgColor});
          } else {
            $(this).css({backgroundColor:'#fff'});
          }
          $input.on('keydown.formEvent keyup.formEvent keypress.formEvent change.formEvent focus.formEvent blur.formEvent', function(){
            if($(this).val() == ''){
              $(this).css({backgroundColor: requiredBgColor});
            } else {
              $(this).css({backgroundColor:'#fff'});
            }
          }).change();
        });
      } else {
        $th.find('.required-icon').remove();
        $select.removeClass('form-required');

        $input.off('.formEvent');
        $input.each(function() {
          $(this).css({backgroundColor:'#fff'});
        });
      }
      
    });
  }

});
