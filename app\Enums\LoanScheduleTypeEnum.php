<?php
namespace App\Enums;

final class LoanScheduleTypeEnum extends Enum
{
    const NOW = 0;
    const NEW = 1;
    const CHANGED = 2;

    public static function texts(): array
    {
        return [
            self::NOW => trans2('LoanScheduleTypeEnum.NOW'),
            self::NEW => trans2('LoanScheduleTypeEnum.NEW'),
            self::CHANGED => trans2('LoanScheduleTypeEnum.CHANGED'),
        ];
    }

}
