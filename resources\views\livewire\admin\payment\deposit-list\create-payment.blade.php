<div class="modal fade" id="newPaymentsModal" tabindex="-1" aria-labelledby="newPaymentsModalLabel" aria-hidden="true" wire:ignore.self>
    @php
    use App\Enums\PaymentTypeEnum;
    use App\Enums\LoanTransactionTypeEnum;
    @endphp
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{ trans2('screens.payment.deposit_list_tab.create_modal.title') }}</h5>
      </div>
      <div class="modal-body">
        <form action="/payments/list" role="form" class="modal-form">
          <div class="form-block pt-3">
            <div class="row">
              <div class="form-group col-3">
                <div class="form-label text-center">{{ transm('payment.attributes.payment_date') }}</div>
                <div class="form-field">
                  <input type="text" name="" value=""
                  class="{{ empty($createForm->payment_date) ? 'form-required' : '' }} form-control datepicker w-100"
                  data-date-format="yy/mm/dd"
                  x-init="setTimeout(() => initializeDatepickers(), 100)" wire:model="createForm.payment_date">
                </div>
                <div>
                    @error('createForm.payment_date') <span class="message-error">{{ $message }}</span> @enderror
                </div>
              </div>
              <div class="form-group col-3">
                <div class="form-label text-center">{{ transm('payment.attributes.type') }}</div>
                <div class="form-field" wire:ignore>
                  <select name="" class="form-select2 form-required" style="width: 100%" data-placeholder="{{ trans2('select_default') }}" wire:model="createForm.type">
                    {{-- loan_payments --}}
                    <option value=""></option>
                        @foreach (LoanTransactionTypeEnum::getActiveTransactionTypes() as $key => $text)
                            <option value="{{ $key }}">{{ $text }}</option>
                        @endforeach
                  </select>
                </div>
                <div>
                    @error('createForm.type') <span class="message-error">{{ $message }}</span> @enderror
                </div>
              </div>
              <div class="form-group col-3">
                <div class="form-label text-center">{{ transm('payment.attributes.deposit_withdrawal_method') }}</div>
                <div class="form-field" wire:ignore>
                  <select name="" class="form-select2 form-required" style="width: 100%" data-placeholder="{{ trans2('select_default') }}" wire:model="createForm.payment_type">
                    {{-- loan_schedules --}}
                    <option value=""></option>
                        @foreach (PaymentTypeEnum::dropdown() as $key => $text)
                            <option value="{{ $key }}">{{ $text }}</option>
                        @endforeach
                  </select>
                </div>
                <div>
                    @error('createForm.payment_type') <span class="message-error">{{ $message }}</span> @enderror
                </div>
              </div>
              <div class="form-group col-3">
                <div class="form-label text-center">{{ transm('payment.attributes.deposit_withdrawal_amount') }}</div>
                <div class="form-field">
                  <div class="form-parts-unit">
                    <div class="form-unit">&yen;</div>
                    <input type="text" name="" value="" class="form-control form-required w-100" wire:model="createForm.amount">
                  </div>
                </div>
                <div>
                    @error('createForm.amount') <span class="message-error">{{ $message }}</span> @enderror
                </div>
              </div>
            </div>
          </div>
          <div class="text-center m-3 position-relative">
            <button type="button" class="btn-text-prev position-absolute start-0 top-50 translate-middle-y" data-bs-dismiss="modal">{{ trans2('screens.payment.deposit_list_tab.create_modal.back') }}</button>
            <button type="submit" wire:click.prevent="validateCreate" class="btn btn-dark btn-medium">{{ trans2('button.registration') }}</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
@script
<script>
    document.getElementById('newPaymentsModal').addEventListener('hidden.bs.modal', function () {
        if(shouldDispatchReset == true)
            Livewire.dispatch('reset-data');
    });
</script>
@endscript
