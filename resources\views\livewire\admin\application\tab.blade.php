@php
use App\Enums\ApplicationTabEnum;
 @endphp
<ol class="application-steps-list">
    <li class="{{ ApplicationTabEnum::checkCurrentOrCompleteTab(ApplicationTabEnum::BRAND) }}">
        <span class="num">1</span>
        <span class="txt">{{ trans2('screens.application.brand.title') }}</span>
    </li>
    <li class="{{ ApplicationTabEnum::checkCurrentOrCompleteTab(ApplicationTabEnum::PAYMENT) }}">
        <span class="num">2</span>
        <span class="txt">{{ trans2('screens.application.payment.title') }}</span>
    </li>
    <li class="{{ ApplicationTabEnum::checkCurrentOrCompleteTab(ApplicationTabEnum::SERVICE) }}">
        <span class="num">3</span>
        <span class="txt">{{ trans2('screens.application.service.title') }}</span>
    </li>
    <li class="{{ ApplicationTabEnum::checkCurrentOrCompleteTab(ApplicationTabEnum::IDENTIFICATION) }}">
        <span class="num">4</span>
        <span class="txt">{{ trans2('screens.application.identification.title') }}</span>
    </li>
    <li class="{{ ApplicationTabEnum::checkCurrentOrCompleteTab(ApplicationTabEnum::CUSTOMER) }}">
        <span class="num">5</span>
        <span class="txt">{{ trans2('screens.application.customer.title') }}</span>
    </li>
    <li class="{{ ApplicationTabEnum::checkCurrentOrCompleteTab(ApplicationTabEnum::CUSTOMER_SETUP) }}">
        <span class="num">6</span>
        <span class="txt">{{ trans2('screens.application.customer_setup.title') }}</span>
    </li>
    <li class="{{ ApplicationTabEnum::checkCurrentOrCompleteTab(ApplicationTabEnum::CHECK) }}">
        <span class="num">7</span>
        <span class="txt">{{ trans2('screens.application.check.title') }}</span>
    </li>
</ol>
