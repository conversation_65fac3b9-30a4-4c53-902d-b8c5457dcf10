<?php

namespace App\Livewire\Admin\Payment;

use App\Enums\ContractStatusEnum;
use App\Enums\PaymentCompanyFlagEnum;
use App\Enums\PaymentStatusEnum;
use App\Enums\PaymentTypeEnum;
use App\Enums\ResultStatusEnum;
use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\CustomerRepository;
use App\Repositories\LoanPaymentRepository;
use App\Repositories\LoanRefundRepository;
use App\Repositories\LoanScheduleRepository;
use App\Services\ApplicationCourseService;
use App\Services\ApplicationService;
use App\Services\LoanScheduleService;
use App\Services\ToastService;
use Gate;

class Show extends BaseAdminPageComponent
{
    public $id;
    public $isCanDelete;

    public $loanSchedule;
    public $paymentDate;
    public $loanScheduleTransaction;
    public $applicationCourse;
    public $loanScheduleLatestPayment;
    public $payment_status;
    public $payment_status_text;
    public $payment_status_badge_class;
    public $payment_type_text;
    public $contract_status_text;
    public $contract_status_colors;
    public $payment_company_flag_text;
    public $perPaymentAmount;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::PAYMENT;
        parent::__construct();
        $subTitle = trans2('screens.payment.detail.page_title');
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle = $this->pageTitle . $subTitle . $suffixTitle;
    }

    public function mount($id)
    {
        $this->id = $id;
        $this->loanSchedule = app()->make(LoanScheduleService::class)->getloanScheduleById($id);
        
        if (empty($this->loanSchedule->application->contract_status->value)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.payment.index'));
            return;
        }

        $brandIds = collect($this->loanSchedule->application?->brand_id);
        $shopIds = collect($this->loanSchedule->application?->shop_id);
        Gate::authorize('allowed-update', [SidebarMenuEnum::PAYMENT, $brandIds, $shopIds]);

        if (empty($this->loanSchedule)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.payment.index'));
            return;
        }

        $loanSchedule = $this->loanSchedule;
        $this->payment_status = $loanSchedule->payment_status->value;
        $this->payment_status_text = PaymentStatusEnum::texts()[$loanSchedule->payment_status->value] ?? '不明';
        $this->payment_status_badge_class = PaymentStatusEnum::colors()[$loanSchedule->payment_status->value] ?? 'status-secondary';
        $this->payment_type_text = PaymentTypeEnum::texts()[$loanSchedule->payment_type->value] ?? '不明';
        $this->contract_status_text = ContractStatusEnum::texts()[$loanSchedule->application->contract_status->value] ?? '不明';
        $this->contract_status_colors = ContractStatusEnum::colors()[$loanSchedule->application->contract_status->value] ?? 'status-primary';
        $this->payment_company_flag_text = PaymentCompanyFlagEnum::texts()[$loanSchedule->payment_company_flag->value] ?? '';
        $this->perPaymentAmount = $this->calculatePerPaymentAmount();

        $applicationId = $this->loanSchedule->application_id;

        $this->loanScheduleTransaction = app()->make(LoanScheduleRepository::class)->getLoanScheduleTransactionsById($id);
        $this->paymentDate = app()->make(LoanPaymentRepository::class)->getPaymentDatebyApplicationId($applicationId);
        $this->applicationCourse = app()->make(ApplicationCourseService::class)->getCoursesByApplication($applicationId);
        $this->loanScheduleLatestPayment = app()->make(LoanScheduleRepository::class)->getLoanSchedulesWithArrearsAndLatestPayment($applicationId);
        $this->isCanDelete = true;
    }

    public function render()
    {
        return $this->viewLivewireAdmin('payment.detail.show');
    }

    public function calculatePerPaymentAmount(): ?float
    {
        if (!$this->loanSchedule->application->fee_amount || !$this->loanSchedule->application->payment_count || $this->loanSchedule->application->payment_count == 0) {
            return null;
        }

        return $this->loanSchedule->application->fee_amount / $this->loanSchedule->application->payment_count;
    }
}
