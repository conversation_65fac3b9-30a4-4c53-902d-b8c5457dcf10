@php
$admin = auth()->user();
$applicationStatusText = \App\Enums\ApplicationStatusEnum::texts();
$applicationStatusColor = \App\Enums\ApplicationStatusEnum::colors();
@endphp
@extends('livewire.admin.customer.detail.index')
@section('detail-tab')
    <div class="card mb-3">
        <div class="card-body">
            @if($admin->isStore())
                <div class="mb-4 d-flex justify-content-end">
                    <a href="{{ getRoute('customer.application.brand', ['customer_id' => $customer?->id]) }}" target="_blank" class="btn btn-dark btn-small">{{ trans2('screens.customer.application.index.button_register') }}</a>
                </div>
            @endif

                <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
                    @if($applications->isNotEmpty())
                        <table class="table table-borderless table-thead-bordered table-align-middle table-database"
                               id="dataListTable">
                            <thead>
                            <tr>
                                <th scope="col">{{ trans2('screens.customer.application.index.application_id') }}</th>
                                <th scope="col">{{ trans2('screens.customer.application.index.status') }}</th>
                                <th scope="col">{{ trans2('screens.customer.application.index.brand') }}</th>
                                <th scope="col">{{ trans2('screens.customer.application.index.application_date') }}</th>
                                <th scope="col">{{ trans2('screens.customer.application.index.application_detail') }}</th>
                            </tr>
                            </thead>
                            <tbody>
                                @foreach($applications as $app)
                                    @php
                                        $routeNavigate = ($app->status?->value !== \App\Enums\ApplicationStatusEnum::IN_PROGRESS) ? getRoute('customer.application.memo.index', [$customer?->id, $app->id]) : getRoute('customer.application.detail.index', [$customer?->id, $app->id]);
                                    @endphp
                                    <tr onclick="window.location.href='{{ $routeNavigate }}'" class="cursor-pointer">
                                        <td>{{ $app->id }}</td>
                                        <td>
                                            <span class="badge badge-status badge-lg rounded-pill badge-{{ $applicationStatusColor[$app->status?->value] }}">
                                                {{ $applicationStatusText[$app->status?->value] }}
                                            </span>
                                        </td>
                                        <td>{{ $app->brand?->name }}</td>
                                        <td>{{ $app->application_date ? \Carbon\Carbon::parse($app->application_date)->format('Y/m/d') : '' }}</td>
                                        <td>
                                            @if($app->applicationCourses?->isNotEmpty())
                                                @foreach($app->applicationCourses as $appCourse)
                                                    <div>{{ $app->shopBrand?->name }}／{{ $appCourse->course?->itemType?->name }}／{{ $appCourse->course?->name_application }}</div>
                                                @endforeach
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        @include('components.no-result-found')
                    @endif
                </div>
        </div>
    </div>
@endsection

