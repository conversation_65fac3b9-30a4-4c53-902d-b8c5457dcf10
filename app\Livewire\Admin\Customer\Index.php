<?php

namespace App\Livewire\Admin\Customer;

use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\HolidayService;

class Index extends BaseAdminPageComponent
{
    public $selectedHoliday;
    
    public function __construct()
    {
        $this->page = SidebarMenuEnum::CUSTOMER;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
    }

    public function mount()
    {
        $this->selectedHoliday = app(HolidayService::class)->getAllHolidays();
    }

    public function render()
    {
        return $this->viewLivewireAdmin('customer.index');
    }
}
