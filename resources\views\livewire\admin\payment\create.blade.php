<div id="contents" class="scroll-validate">
    <div class="contents-container">
        <div class="container-fluid">
            <div class="page-detail-header d-flex justify-content-between align-items-center mt-5 mb-5">
                <h1 class="page-detail-header-name align-items-center">
                    <div class="name-group">
                        <div class="name-field">{{ trans2('screens.customer.new.title') }}</div>
                    </div>
                </h1>
                <div class="page-detail-header-link p-0">
                    <a href="{{ getRoute('customer.index') }}"
                       class="btn-text-prev small">{{ trans2('screens.customer.index.back') }}</a>
                </div>
            </div>
            <div class="card mb-3">
                <div class="card-body">
                    <div class="container-min pt-3">
                        <form role="form">
                            <table class="table table-edit">
                                <tbody>
                                <tr>
                                    <th class="required">{{ transm('customer.attributes.name') }}<span
                                            class="required-icon">{{ trans2('required') }}</span></th>
                                    <td>
                                        <div class="row">
                                            <div class="col-6">
                                                <input type="text" wire:model="createForm.last_name" name="" value=""
                                                       class="form-control form-required" placeholder="姓" maxlength="128">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.last_name') }}</span>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <input type="text" wire:model="createForm.first_name" name="" value=""
                                                       class="form-control form-required" placeholder="名" maxlength="128">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.first_name') }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="required">{{ transm('customer.attributes.name_hiragana') }}<span
                                            class="required-icon">{{ trans2('required') }}</span></th>
                                    <td>
                                        <div class="row">
                                            <div class="col-6">
                                                <input type="text" wire:model="createForm.last_name_kana" name=""
                                                       value="" class="form-control form-required" placeholder="セイ" maxlength="128">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.last_name_kana') }}</span>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <input type="text" wire:model="createForm.first_name_kana" name=""
                                                       value="" class="form-control form-required" placeholder="メイ" maxlength="128">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.first_name_kana') }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="required">{{ transm('customer.attributes.sex') }}<span
                                            class="required-icon">{{ trans2('required') }}</span></th>
                                    <td>
                                        <div class="d-flex">
                                            <div class="form-radio me-5">
                                                <label>
                                                    <input type="radio" wire:model="createForm.sex" name="sex" value="1"
                                                           class="form-radio-input" checked>
                                                    <span class="form-radio-text">{{ trans2('SexEnum.FEMALE') }}</span>
                                                </label>
                                            </div>
                                            <div class="form-radio">
                                                <label>
                                                    <input type="radio" wire:model="createForm.sex" name="sex" value="2"
                                                           class="form-radio-input">
                                                    <span class="form-radio-text">{{ trans2('SexEnum.MALE') }}</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="message-error">{{ $errors->first('createForm.sex') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="required">
                                        {{ transm('customer.attributes.birthday') }}<span class="required-icon">{{ trans2('required') }}</span>
                                    </th>
                                    <td>
                                        <input type="text" wire:model="createForm.birthday" name="" value=""
                                               class="form-control datepicker form-required"
                                               x-init="setTimeout(() => initializeDatepickers(), 100)"
                                               data-date-format="{{ trans2('placeholder_date_ymd') }}">
                                        <div>
                                            <span class="message-error">{{ $errors->first('createForm.birthday') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{{ transm('customer.attributes.email') }}</th>
                                    <td>
                                        <input type="email" wire:model="createForm.email" name="" value=""
                                               class="form-control" maxlength="128">
                                        <div>
                                            <span class="message-error">{{ $errors->first('createForm.email') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="required">{{ transm('customer.attributes.tel') }}<span
                                            class="required-icon">{{ trans2('required') }}</span></th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <input type="text" wire:model="createForm.tel1" name="" value="" class="form-control form-required w-px-150" maxlength="32">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.tel1') }}</span>
                                                </div>
                                            </div>
                                            <div class="mx-2">-</div>
                                            <div>
                                                <input type="text" wire:model="createForm.tel2" name="" value="" class="form-control form-required w-px-150" maxlength="32">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.tel2') }}</span>
                                                </div>
                                            </div>
                                            <div class="mx-2">-</div>
                                            <div>
                                                <input type="text" wire:model="createForm.tel3" name="" value="" class="form-control form-required w-px-150" maxlength="32">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.tel3') }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="required">
                                        {{ transm('customer.attributes.postcode') }}<span class="required-icon">{{ trans2('required') }}</span></th>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <input type="text" wire:model="createForm.zip1" name="zip1" value="" class="form-control form-required w-px-150" maxlength="4">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.zip1') }}</span>
                                                </div>
                                            </div>
                                            <div class="mx-2">-</div>
                                            <div>
                                                <input type="text" wire:model="createForm.zip2" name="zip2" value="" class="form-control form-required w-px-150" maxlength="4">
                                                <div>
                                                    <span class="message-error">{{ $errors->first('createForm.zip2') }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="required">{{ transm('customer.attributes.prefecture') }}<span
                                            class="required-icon">{{ trans2('required') }}</span></th>
                                    <td>
                                        <div wire:ignore>
                                            <select name="pref_id" class="form-select2 form-required" wire:model="createForm.pref_id" style="width:280px;" data-placeholder="{{ trans2('select_default') }}">
                                                <option value=""></option>
                                                @foreach ($listPrefs as $key => $pref)
                                                    <option value="{{ data_get($pref,'id') }}">{{ data_get($pref,'name') }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div>
                                            @error('createForm.pref_id') <span class="message-error">{{ $message }}</span> @enderror
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="required">{{ transm('customer.attributes.city') }}<span
                                            class="required-icon">{{ trans2('required') }}</span></th>
                                    <td>
                                        <input type="text" wire:model="createForm.city" name="city" value=""
                                               class="form-control form-required">
                                        <div>
                                            <span class="message-error">{{ $errors->first('createForm.city') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="required">{{ transm('customer.attributes.address') }}<span
                                            class="required-icon">{{ trans2('required') }}</span></th>
                                    <td>
                                        <input type="text" wire:model="createForm.address" name="address" value=""
                                               class="form-control form-required">
                                        <div>
                                            <span class="message-error">{{ $errors->first('createForm.address') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{{ transm('customer.attributes.building') }}</th>
                                    <td>
                                        <input type="text" wire:model="createForm.building" name="building" value=""
                                               class="form-control">
                                        <div><span
                                                class="message-error">{{ $errors->first('createForm.building') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{{ transm('customer.attributes.contract_count') }}</th>
                                    <td>
                                        <div class="form-parts-unit d-inline-block">
                                            <input type="text" wire:model="createForm.contract_count" name="" value=""
                                                   class="form-control w-px-140">
                                            <div class="form-unit">{{ trans2('times') }}</div>
                                        </div>
                                        <div>
                                            <span class="message-error">{{ $errors->first('createForm.contract_count') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>{{ transm('customer.attributes.black') }}</th>
                                    <td>
                                        <div class="form-check">
                                            <label>
                                                <input wire:model="createForm.black_flag" type="checkbox" name=""
                                                       value="" class="form-check-input">
                                                <span
                                                    class="form-check-text">{{ transm('customer.attributes.black') }}</span>
                                            </label>
                                        </div>
                                        <div>
                                            <span class="message-error">{{ $errors->first('createForm.black_flag') }}</span>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <div class="mb-4 mt-4 d-flex justify-content-between align-items-center">
                                <a href="{{ getRoute('customer.index') }}"
                                   class="btn-text-prev small">{{ trans2('screens.customer.new.back') }}</a>
                                <button wire:click.prevent="validateCreate" class="btn btn-dark btn-medium">{{ trans2('screens.customer.new.back') }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ getRoute('customer.index') }}"
                   class="btn-text-prev small">{{ trans2('screens.customer.new.back_to_list') }}</a>
            </div>
        </div>
    </div>
</div>
@include('livewire.admin.customer.script')
