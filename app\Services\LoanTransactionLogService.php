<?php

namespace App\Services;

use App\Repositories\LoanTransactionLogRepository;

class LoanTransactionLogService extends CustomService
{
    public function __construct(
        public LoanTransactionLogRepository $loanTransactionLogRepository,
    ) {
        parent::__construct();
    }

    public function getLoanTransactionLogByApplication($application_id)
    {
        return $this->loanTransactionLogRepository->getLoanTransactionLogByApplication($application_id);
    }

    public function getLog($id)
    {
        return $this->loanTransactionLogRepository->find($id);
    }
}
