<?php

namespace App\Enums;

class ApplicationInspectionStatusEnum extends Enum
{
    const BEFORE_REVIEW = 1;
    const AWAITING_DOCUMENTS = 2;
    const DOCUMENTS_SUBMITTED = 3;
    const AWAITING_CONTACT_RESPONSE = 4;
    const CONTACT_RESPONSE_RECEIVED = 5;
    const AWAITING_IDENTITY_CONTACT_RESPONSE = 6;
    const AWAITING_GUARANTOR_CONTACT_RESPONSE = 7;
    const IDENTITY_GUARANTOR_CONTACT_RESPONSE_RECEIVED = 8;
    const IDENTITY_GUARANTOR_VERIFICATION_IN_PROGRESS = 9;
    const AWAITING_IDENTITY_REDESIGNATION = 10;
    const IDENTITY_REDESIGNATED = 11;
    const AWAITING_GUARANTOR_REDESIGNATION = 12;
    const GUARANTOR_REDESIGNATED = 13;
    const REJECTED = 14;
    const APPROVED = 15;

    public static function texts(): array
    {
        return [
            self::BEFORE_REVIEW => trans2('ApplicationInspectionStatusEnum.BEFORE_REVIEW'),
            self::AWAITING_DOCUMENTS => trans2('ApplicationInspectionStatusEnum.AWAITING_DOCUMENTS'),
            self::DOCUMENTS_SUBMITTED => trans2('ApplicationInspectionStatusEnum.DOCUMENTS_SUBMITTED'),
            self::AWAITING_CONTACT_RESPONSE => trans2('ApplicationInspectionStatusEnum.AWAITING_CONTACT_RESPONSE'),
            self::CONTACT_RESPONSE_RECEIVED => trans2('ApplicationInspectionStatusEnum.CONTACT_RESPONSE_RECEIVED'),
            self::AWAITING_IDENTITY_CONTACT_RESPONSE => trans2('ApplicationInspectionStatusEnum.AWAITING_IDENTITY_CONTACT_RESPONSE'),
            self::AWAITING_GUARANTOR_CONTACT_RESPONSE => trans2('ApplicationInspectionStatusEnum.AWAITING_GUARANTOR_CONTACT_RESPONSE'),
            self::IDENTITY_GUARANTOR_CONTACT_RESPONSE_RECEIVED => trans2('ApplicationInspectionStatusEnum.IDENTITY_GUARANTOR_CONTACT_RESPONSE_RECEIVED'),
            self::IDENTITY_GUARANTOR_VERIFICATION_IN_PROGRESS => trans2('ApplicationInspectionStatusEnum.IDENTITY_GUARANTOR_VERIFICATION_IN_PROGRESS'),
            self::AWAITING_IDENTITY_REDESIGNATION => trans2('ApplicationInspectionStatusEnum.AWAITING_IDENTITY_REDESIGNATION'),
            self::IDENTITY_REDESIGNATED => trans2('ApplicationInspectionStatusEnum.IDENTITY_REDESIGNATED'),
            self::AWAITING_GUARANTOR_REDESIGNATION => trans2('ApplicationInspectionStatusEnum.AWAITING_GUARANTOR_REDESIGNATION'),
            self::GUARANTOR_REDESIGNATED => trans2('ApplicationInspectionStatusEnum.GUARANTOR_REDESIGNATED'),
            self::REJECTED => trans2('ApplicationInspectionStatusEnum.REJECTED'),
            self::APPROVED => trans2('ApplicationInspectionStatusEnum.APPROVED'),
        ];
    }

    public static function colors(): array
    {
        return [
            self::BEFORE_REVIEW => 'warning',
            self::AWAITING_DOCUMENTS => 'warning',
            self::DOCUMENTS_SUBMITTED => 'warning',
            self::AWAITING_CONTACT_RESPONSE => 'primary',
            self::CONTACT_RESPONSE_RECEIVED => 'primary',
            self::AWAITING_IDENTITY_CONTACT_RESPONSE => 'primary',
            self::AWAITING_GUARANTOR_CONTACT_RESPONSE => 'primary',
            self::IDENTITY_GUARANTOR_CONTACT_RESPONSE_RECEIVED => 'primary',
            self::IDENTITY_GUARANTOR_VERIFICATION_IN_PROGRESS => 'primary',
            self::AWAITING_IDENTITY_REDESIGNATION => 'warning',
            self::IDENTITY_REDESIGNATED => 'primary',
            self::AWAITING_GUARANTOR_REDESIGNATION => 'warning',
            self::GUARANTOR_REDESIGNATED => 'primary',
            self::REJECTED => 'success',
            self::APPROVED => 'success',
        ];
    }

    public static function values1()
    {
        return [
            self::AWAITING_DOCUMENTS,
            self::DOCUMENTS_SUBMITTED,
            self::AWAITING_CONTACT_RESPONSE,
            self::CONTACT_RESPONSE_RECEIVED,
        ];
    }

    public static function values2()
    {
        return [
            self::AWAITING_IDENTITY_CONTACT_RESPONSE,
            self::IDENTITY_GUARANTOR_CONTACT_RESPONSE_RECEIVED,
            self::IDENTITY_GUARANTOR_VERIFICATION_IN_PROGRESS,
            self::AWAITING_IDENTITY_REDESIGNATION,
            self::IDENTITY_REDESIGNATED,
        ];
    }

    public static function values3()
    {
        return [
            self::AWAITING_GUARANTOR_CONTACT_RESPONSE,
            self::AWAITING_GUARANTOR_REDESIGNATION,
            self::GUARANTOR_REDESIGNATED,
        ];
    }
}
