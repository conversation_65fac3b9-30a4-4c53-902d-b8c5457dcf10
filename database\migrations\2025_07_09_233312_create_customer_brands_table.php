<?php

use App\Database\Migration\BlueprintCustom;
use App\Database\Migration\SchemaCustom;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        SchemaCustom::create('customer_brands', function (BlueprintCustom $table) {
            $table->increments('id')->comment('ID');
            $table->integer('customer_id')->comment('顧客ID')->index();
            $table->integer('brand_id')->comment('ブランドID')->index();
            $table->defaultFields();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SchemaCustom::dropIfExists('customer_brands');
    }
};
