<?php

namespace App\Models\Presenters;

use Carbon\Carbon;


trait PLoanTransactionLog
{
    public function getTransactionDatetimeAttribute(){
        return $this->attributes['ins_date']
            ? Carbon::parse($this->attributes['ins_date'])->format('Y/m/d')
            : null;
    }
    public function getPaymentDatetimeAttribute(){
        return $this->attributes['payment_date']
            ? Carbon::parse($this->attributes['payment_date'])->format('Y/m/d')
            : null;
    }
}
