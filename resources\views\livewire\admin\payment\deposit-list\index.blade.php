@extends('livewire.admin.payment.detail.index')
@section('detail-tab')
    @php
        $paymentStatusText = \App\Enums\PaymentStatusEnum::texts();
        $paymentStatusColor = \App\Enums\PaymentStatusEnum::colors();
        $paymentCompanyFlagText = \App\Enums\PaymentCompanyFlagEnum::texts();
        $paymentTypeText = \App\Enums\PaymentTypeEnum::texts();
        $func = \App\Enums\FunctionEnum::PAYMENT;
        use \App\Enums\LoanTransactionTypeEnum;
    @endphp
    <div>
        <div class="card mb-3">
            <div class="card-body">
                <div class="card-container border-bottom mb-5">
                    <h2 class="heading-2 mt-0">{{ trans2('screens.payment.deposit_list_tab.deposit_list') }}</h2>
                    <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
                        <table class="table table-borderless table-database">
                            <thead>
                                <tr>
                                    <th scope="col">{{ transm('payment.attributes.id') }}</th>
                                    <th scope="col">{{ transm('payment.attributes.payment_plan_date') }}</th>
                                    <th scope="col">{{ transm('payment.attributes.payment_status') }}</th>
                                    <th scope="col">{{ transm('payment.attributes.deposit_amount') }}</th>
                                    <th scope="col">{{ transm('payment.attributes.deposit_payment_type') }}</th>
                                    <th scope="col">{{ transm('payment.attributes.balance_difference') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($depositList as $deposit)
                                    <tr wire:navigate
                                        href="{{ getRoute('payment.details', ['id' => $deposit->loan_schedule_id]) }}"
                                        data-link="">
                                        <td>{{ data_get($deposit, 'loan_schedule_id') }}</td>
                                        <td>{{ data_get($deposit, 'payment_plan_date') }}</td>
                                        <td>
                                            @if ($deposit->payment_status)
                                                <span
                                                    class="badge badge-status rounded-pill badge-lg badge-{{ $paymentStatusColor[$deposit->payment_status?->value] }}">{{ $paymentStatusText[$deposit->payment_status?->value] }}</span>
                                            @endif
                                        </td>
                                        <td><span class="me-1">&yen;</span>{{ formatNumber(data_get($deposit, 'total_amount')) }}</td>
                                        <td>{{ data_get($deposit, 'payment_type.text') }}</td>
                                        <td><span class="me-1">&yen;</span>{{ formatNumber(data_get($deposit, 'balance_difference')) }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <h2 class="heading-2 mt-4">{{ trans2('screens.payment.deposit_list_tab.deposit_list') }}</h2>
                    <table class="table table-deposit mb-5">
                        <thead>
                            <tr>
                                <th colspan="2">{{ trans2('screens.payment.index.status.waiting_for_payment') }}</th>
                                <th colspan="2">{{ trans2('screens.payment.index.status.payment_complete') }}</th>
                                <th colspan="2">{{ trans2('screens.payment.index.status.unpaid') }}</th>
                                <th colspan="2">{{ trans2('screens.payment.index.status.other') }}</th>
                                <th colspan="2">{{ trans2('screens.payment.index.total') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <th>{{ trans2('screens.payment.index.number') }}</th>
                                <th>{{ trans2('screens.payment.index.amount') }}</th>
                                <th>{{ trans2('screens.payment.index.number') }}</th>
                                <th>{{ trans2('screens.payment.index.amount') }}</th>
                                <th>{{ trans2('screens.payment.index.number') }}</th>
                                <th>{{ trans2('screens.payment.index.amount') }}</th>
                                <th>{{ trans2('screens.payment.index.number') }}</th>
                                <th>{{ trans2('screens.payment.index.amount') }}</th>
                                <th>{{ trans2('screens.payment.index.number') }}</th>
                                <th>{{ trans2('screens.payment.index.amount') }}</th>
                            </tr>
                            <tr>
                                <td>{{ data_get($payment, 'waiting_count') ?? 0 }}{{ trans2('subject') }}</td>
                                <td><span class="me-1">&yen;</span>{{ formatNumber(data_get($payment, 'waiting_total_amount')) }}</td>
                                <td>{{ data_get($payment, 'deposited_count') ?? 0 }}{{ trans2('subject') }}</td>
                                <td><span class="me-1">&yen;</span>{{ formatNumber(data_get($payment, 'deposited_total_amount')) }}</td>
                                <td>{{ data_get($payment, 'unpaid_count') ?? 0 }}{{ trans2('subject') }}</td>
                                <td><span class="me-1">&yen;</span>{{ formatNumber(data_get($payment, 'unpaid_total_amount')) }}</td>
                                <td>{{ data_get($payment, 'other_count') ?? 0 }}{{ trans2('subject') }}</td>
                                <td><span class="me-1">&yen;</span>{{ formatNumber(data_get($payment, 'other_total_amount')) }}</td>
                                <td>{{ data_get($payment, 'grand_count') ?? 0 }}{{ trans2('subject') }}</td>
                                <td><span class="me-1">&yen;</span>{{ formatNumber(data_get($payment, 'grand_total_amount')) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex align-items-end justify-content-between mb-3">
                    <h2 class="heading-2 mt-0 mb-0">{{ trans2('screens.payment.deposit_list_tab.deposit_withdrawal_log') }}</h2>
                    <a href="#" class="btn btn-dark" data-bs-toggle="modal"
                        data-bs-target="#newPaymentsModal">{{ trans2('screens.payment.deposit_list_tab.create_modal.title') }}</a>
                </div>
                <div class="overflow-auto" data-simplebar data-simplebar-auto-hide="false">
                    <table class="table table-borderless">
                        <thead>
                            <tr>
                                <th scope="col">{{ transm('payment.attributes.payment_date') }}</th>
                                <th scope="col">{{ transm('payment.attributes.deposit_withdrawal_amount') }}</th>
                                <th scope="col">{{ transm('payment.attributes.type') }}</th>
                                <th scope="col">{{ transm('payment.attributes.deposit_withdrawal_method') }}</th>
                                <th scope="col">{{ transm('payment.attributes.deposit_id') }}</th>
                                <th scope="col">{{ transm('payment.attributes.deposit_status') }}</th>
                                <th scope="col"></th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($mergesCollection as $data)
                                <tr>
                                    <td>{{ data_get($data, 'payment_date') }}</td>
                                    <td>
                                        <span class="me-1">&yen;</span>
                                        @if (data_get($data, 'type') == LoanTransactionTypeEnum::WITHDRAWAL)
                                            -
                                        @endif
                                        {{ formatNumber(data_get($data, 'amount')) }}
                                    </td>
                                    <td>{{ data_get($data, 'transaction_type') }}</td>
                                    <td>{{ $paymentTypeText[data_get($data, 'payment_type')] }}</td>
                                    <td>
                                        @if($data->loanPaymentAllocations)
                                            @foreach (data_get($data, 'loanPaymentAllocations') as $log)
                                                <div class="d-flex align-items-center mt-2" style="min-height: 32px;">
                                                    @if ($log->loan_schedule_id)
                                                        <a wire:navigate href="{{ getRoute('payment.details', ['id' => $log->loan_schedule_id]) }}">
                                                            {{ data_get($log, 'loan_schedule_id') }}
                                                        </a>
                                                    @endif
                                                </div>
                                            @endforeach
                                        @endif
                                    </td>
                                    <td>
                                        @if($data->loanPaymentAllocations)
                                            @foreach (data_get($data, 'loanPaymentAllocations') as $log)
                                                <div class="d-flex align-items-center mt-2" style="min-height: 32px;">
                                                    @if ($log->loanSchedule->payment_status && $log->before_payment_status)
                                                        <span
                                                            class="badge badge-status badge-lg rounded-pill badge-{{ $paymentStatusColor[$log->before_payment_status->value] }}">
                                                            {{ $paymentStatusText[$log->before_payment_status->value] }}
                                                        </span>
                                                        <div class="arrow mx-3">
                                                            <img src="{{ asset('assets/admin/img/ui/ui_history_arrow.svg') }}" alt="">
                                                        </div>
                                                        <span
                                                            class="badge badge-status badge-lg rounded-pill badge-{{ $paymentStatusColor[$log->loanSchedule->payment_status->value] }}">
                                                            {{ $paymentStatusText[$log->loanSchedule->payment_status->value] }}
                                                        </span>
                                                    @endif
                                                </div>
                                            @endforeach
                                        @endif
                                    </td>

                                    <td class="text-end">
                                        <a href="#"
                                            wire:click.prevent="getData({{ data_get($data, 'id') }}, {{ data_get($data, 'type') }})">
                                            {{ trans2('button.edit') }}
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="text-end">
                    <span class="small me-4">{{ transm('_default.attributes.ins_date') }}：{{ data_get($loanSchedule, 'ins_date') }}</span>
                    <span class="small me-4">{{ transm('_default.attributes.upd_date') }}：{{ data_get($loanSchedule, 'upd_date') }}</span>
                </div>
                @include('livewire.common.message-modal')
            </div>
        </div>
    </div>
    <livewire:admin.payment.deposit-list.create-payment :applicationId="data_get($this->loanSchedule, 'application_id')" :customerId="data_get($this->loanSchedule, 'customer_id')"/>
    <livewire:admin.payment.deposit-list.update-payment :applicationId="data_get($this->loanSchedule, 'application_id')" :customerId="data_get($this->loanSchedule, 'customer_id')"/>
@endsection
@include('livewire.admin.payment.script')
