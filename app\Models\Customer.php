<?php

namespace App\Models;

use App\Enums\BankAccountTypeEnum;
use App\Enums\BankFlagEnum;
use App\Enums\BlackFlagEnum;
use App\Enums\ContactFlagEnum;
use App\Enums\FunctionEnum;
use App\Enums\InformationInputFlagEnum;
use App\Enums\RelationshipFlagEnum;
use App\Enums\SexEnum;
use App\Models\Base\CustomModel;
use App\Models\Concerns\HasNameLang;
use App\Models\Presenters\PCustomer;
use App\Traits\TrackingUpdateHistory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends CustomModel
{
    use PCustomer;
    use HasNameLang;
    use TrackingUpdateHistory;

    /**
     * The table associated with the model.
     *
     * @var string
     */

    protected $table = 'customers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'last_name',
        'first_name',
        'last_name_kana',
        'first_name_kana',
        'sex',
        'birthday',
        'email',
        'tel1',
        'tel2',
        'tel3',
        'zip1',
        'zip2',
        'pref_id',
        'city',
        'address',
        'building',
        'pref_kana_id',
        'city_kana',
        'address_kana',
        'building_kana',
        'contract_count',
        'black_flag',
        'emergency_last_name',
        'emergency_first_name',
        'emergency_last_name_kana',
        'emergency_first_name_kana',
        'emergency_tel1',
        'emergency_tel2',
        'emergency_tel3',
        'emergency_zip1',
        'emergency_zip2',
        'emergency_pref_id',
        'emergency_city',
        'emergency_address',
        'emergency_building',
        'relationship_flag',
        'relationship_other',
        'annual_income',
        'company_name',
        'company_name_kana',
        'company_tel1',
        'company_tel2',
        'company_tel3',
        'company_zip1',
        'company_zip2',
        'company_pref_id',
        'company_city',
        'company_address',
        'company_building',
        'information_input_flag',
        'gw_last_name',
        'gw_first_name',
        'gw_last_name_kana',
        'gw_first_name_kana',
        'gw_sex',
        'gw_birthday',
        'gw_tel1',
        'gw_tel2',
        'gw_tel3',
        'gw_zip1',
        'gw_zip2',
        'gw_pref_id',
        'gw_city',
        'gw_address',
        'gw_building',
        'gw_relationship_flag',
        'gw_relationship_other',
        'gw_company_name',
        'gw_company_name_kana',
        'gw_company_tel1',
        'gw_company_tel2',
        'gw_company_tel3',
        'gw_company_zip1',
        'gw_company_zip2',
        'gw_company_pref_id',
        'gw_company_city',
        'gw_company_address',
        'gw_company_building',
        'bank_account_name',
        'bank_account_name_kana',
        'bank_flag',
        'bank_account_mark1',
        'bank_account_mark2',
        'bank_account_mark3',
        'bank_account_number',
        'bank_code',
        'bank_name',
        'branch_code',
        'branch_name',
        'bank_account_type',
        'contact_flag',
        'contact_hope_date1',
        'contact_hope_start_time1',
        'contact_hope_end_time1',
        'contact_hope_date2',
        'contact_hope_start_time2',
        'contact_hope_end_time2',
    ];
    public function applications(): HasMany
    {
        return $this->hasMany(Application::class, 'customer_id', 'id');
    }
    public function customerShops(): HasMany
    {
        return $this->hasMany(CustomerShop::class, 'customer_id', 'id');
    }
    public function customerBrands(): HasMany
    {
        return $this->hasMany(CustomerBrand::class, 'customer_id', 'id');
    }
    public function loanSchedules(): HasMany
    {
        return $this->hasMany(LoanSchedule::class, 'customer_id', 'id');
    }

    public function pref(): BelongsTo
    {
        return $this->belongsTo(Pref::class, 'pref_id', 'id');
    }

    public function prefKana(): BelongsTo
    {
        return $this->belongsTo(Pref::class, 'pref_kana_id', 'id');
    }

    public function emergencyPref(): BelongsTo
    {
        return $this->belongsTo(Pref::class, 'emergency_pref_id', 'id');
    }

    public function companyPref(): BelongsTo
    {
        return $this->belongsTo(Pref::class, 'company_pref_id', 'id');
    }

    public function gwPref(): BelongsTo
    {
        return $this->belongsTo(Pref::class, 'gw_pref_id', 'id');
    }

    public function gwCompanyPref(): BelongsTo
    {
        return $this->belongsTo(Pref::class, 'gw_company_pref_id', 'id');
    }

    public function shops(): BelongsToMany
    {
        return $this->belongsToMany(Shop::class, 'customer_shops', 'customer_id', 'shop_id');
    }
    
    public function brands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class, 'customer_brands', 'customer_id', 'brand_id');
    }

    public function setNameAttribute($value)
    {
        $this->attributes['name'] = trim($value);
    }
    protected $casts = [
        'sex' => SexEnum::class,
        'black_flag' => BlackFlagEnum::class,
        'relationship_flag' => RelationshipFlagEnum::class,
        'information_input_flag' => InformationInputFlagEnum::class,
        'bank_flag' => BankFlagEnum::class,
        'bank_account_type' => BankAccountTypeEnum::class,
        'contact_flag' => ContactFlagEnum::class,
    ];

    public static function getModuleFunction(): string
    {
        return FunctionEnum::CUSTOMER;
    }

    public static function getFieldsTracking(): array
    {
        return [
            'last_name',
            'first_name',
            'last_name_kana',
            'first_name_kana',
            'sex',
            'birthday',
            'email',
            'tel1',
            'tel2',
            'tel3',
            'zip1',
            'zip2',
            'pref_id',
            'city',
            'address',
            'building',
            'contract_count',
            'black_flag'
        ];
    }

    public static function getFieldRelationshipMap() :array
    {
        return [
            'pref_id' => 'pref',
        ];
    }
}
