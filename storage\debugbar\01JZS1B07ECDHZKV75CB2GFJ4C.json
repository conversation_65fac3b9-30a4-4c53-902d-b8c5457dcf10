{"__meta": {"id": "01JZS1B07ECDHZKV75CB2GFJ4C", "datetime": "2025-07-10 11:06:25", "utime": **********.007298, "method": "GET", "uri": "/management/balances", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[11:06:24] LOG.debug: (Time: 02.24) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.944531, "xdebug_link": null, "collector": "log"}, {"message": "[11:06:24] LOG.debug: (Time: 00.47) SQL: select * from `shops` where `shops`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.962575, "xdebug_link": null, "collector": "log"}, {"message": "[11:06:24] LOG.debug: (Time: 00.47) SQL: select * from `brands` where `brands`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.966331, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.737166, "end": **********.007314, "duration": 0.27014803886413574, "duration_str": "270ms", "measures": [{"label": "Booting", "start": **********.737166, "relative_start": 0, "end": **********.914939, "relative_end": **********.914939, "duration": 0.*****************, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.914946, "relative_start": 0.****************, "end": **********.007315, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "92.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.922382, "relative_start": 0.*****************, "end": **********.924412, "relative_end": **********.924412, "duration": 0.002029895782470703, "duration_str": "2.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.004996, "relative_start": 0.*****************, "end": **********.005109, "relative_end": **********.005109, "duration": 0.00011301040649414062, "duration_str": "113μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.005655, "relative_start": 0.*****************, "end": **********.005689, "relative_end": **********.005689, "duration": 3.3855438232421875e-05, "duration_str": "34μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x livewire.admin.balance.index", "param_count": null, "params": [], "start": **********.958128, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/balance/index.blade.phplivewire.admin.balance.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fbalance%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.balance.index"}, {"name": "1x volt-livewire::admin.balance.search", "param_count": null, "params": [], "start": **********.977317, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/admin/balance/search.blade.phpvolt-livewire::admin.balance.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fbalance%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::admin.balance.search"}, {"name": "1x components.lazy-placeholder", "param_count": null, "params": [], "start": **********.981796, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/lazy-placeholder.blade.phpcomponents.lazy-placeholder", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flazy-placeholder.blade.php&line=1", "ajax": false, "filename": "lazy-placeholder.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.lazy-placeholder"}, {"name": "1x __components::dd35d746c9844c565a44df02d1d6acb6", "param_count": null, "params": [], "start": **********.982774, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\storage\\framework\\views/dd35d746c9844c565a44df02d1d6acb6.blade.php__components::dd35d746c9844c565a44df02d1d6acb6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fstorage%2Fframework%2Fviews%2Fdd35d746c9844c565a44df02d1d6acb6.blade.php&line=1", "ajax": false, "filename": "dd35d746c9844c565a44df02d1d6acb6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd35d746c9844c565a44df02d1d6acb6"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.987197, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.master", "param_count": null, "params": [], "start": **********.987706, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/master.blade.phpcomponents.layouts.master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.master"}, {"name": "1x components.layouts.structures.head", "param_count": null, "params": [], "start": **********.988186, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/head.blade.phpcomponents.layouts.structures.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.head"}, {"name": "1x components.layouts.structures.sidebar", "param_count": null, "params": [], "start": **********.989964, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/sidebar.blade.phpcomponents.layouts.structures.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.sidebar"}, {"name": "1x volt-livewire::common.nav-bar", "param_count": null, "params": [], "start": **********.992811, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/nav-bar.blade.phpvolt-livewire::common.nav-bar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fnav-bar.blade.php&line=1", "ajax": false, "filename": "nav-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.nav-bar"}, {"name": "1x volt-livewire::common.logout-modal", "param_count": null, "params": [], "start": **********.995119, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/logout-modal.blade.phpvolt-livewire::common.logout-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Flogout-modal.blade.php&line=1", "ajax": false, "filename": "logout-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.logout-modal"}, {"name": "1x volt-livewire::common.confirm", "param_count": null, "params": [], "start": **********.998302, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/confirm.blade.phpvolt-livewire::common.confirm", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.confirm"}, {"name": "1x volt-livewire::common.toast-message", "param_count": null, "params": [], "start": **********.000222, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/toast-message.blade.phpvolt-livewire::common.toast-message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Ftoast-message.blade.php&line=1", "ajax": false, "filename": "toast-message.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.toast-message"}, {"name": "1x components.layouts.structures.footer", "param_count": null, "params": [], "start": **********.000998, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer.blade.phpcomponents.layouts.structures.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer"}, {"name": "1x components.layouts.structures.footer_js", "param_count": null, "params": [], "start": **********.001305, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_js.blade.phpcomponents.layouts.structures.footer_js", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_js.blade.php&line=1", "ajax": false, "filename": "footer_js.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_js"}, {"name": "1x components.layouts.structures.footer_autoload", "param_count": null, "params": [], "start": **********.00214, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_autoload.blade.phpcomponents.layouts.structures.footer_autoload", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_autoload.blade.php&line=1", "ajax": false, "filename": "footer_autoload.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_autoload"}, {"name": "1x livewire.common.event-handle", "param_count": null, "params": [], "start": **********.00266, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/common/event-handle.blade.phplivewire.common.event-handle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fevent-handle.blade.php&line=1", "ajax": false, "filename": "event-handle.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.common.event-handle"}]}, "route": {"uri": "GET management/balances", "middleware": "admin, locale, auth:admin", "uses": "Closure() {#506\n  class: \"Livewire\\Volt\\VoltManager\"\n  this: Livewire\\Volt\\VoltManager {#493 …}\n  use: {\n    $componentName: \"App\\Livewire\\Admin\\Balance\\Index\"\n  }\n  file: \"C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\VoltManager.php\"\n  line: \"34 to 41\"\n}", "as": "admin.balance.index", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>"}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00318, "accumulated_duration_str": "3.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9423962, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 70.44}, {"sql": "select * from `shops` where `shops`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 61}, {"index": 16, "namespace": null, "name": "resources/views/livewire/admin/balance/search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\balance\\search.blade.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentResolver.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentResolver.php", "line": 39}], "start": **********.9622052, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ShopRepository.php:61", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ShopRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopRepository.php&line=61", "ajax": false, "filename": "ShopRepository.php", "line": "61"}, "connection": "local-ladybird", "explain": null, "start_percent": 70.44, "width_percent": 14.78}, {"sql": "select * from `brands` where `brands`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 54}, {"index": 16, "namespace": null, "name": "resources/views/livewire/admin/balance/search.blade.php", "file": "C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\admin\\balance\\search.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/livewire/volt/src/ComponentFactory.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentFactory.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/livewire/volt/src/ComponentResolver.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\ComponentResolver.php", "line": 39}], "start": **********.9659622, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BrandRepository.php:54", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\BrandRepository.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FBrandRepository.php&line=54", "ajax": false, "filename": "BrandRepository.php", "line": "54"}, "connection": "local-ladybird", "explain": null, "start_percent": 85.22, "width_percent": 14.78}]}, "models": {"data": {"App\\Models\\Brand": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Shop": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FShop.php&line=1", "ajax": false, "filename": "Shop.php", "line": "?"}}, "App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 13, "is_counter": true}, "livewire": {"data": {"admin.balance #E6IH1knlP1AG7p02NYo2": "array:4 [\n  \"data\" => array:4 [\n    \"page\" => \"balance\"\n    \"pageTitle\" => \"残高管理｜LadyBird\"\n    \"redirecting\" => false\n    \"guest\" => false\n  ]\n  \"name\" => \"admin.balance\"\n  \"component\" => \"App\\Livewire\\Admin\\Balance\\Index\"\n  \"id\" => \"E6IH1knlP1AG7p02NYo2\"\n]", "admin.balance.search #quCd0cyY8HCNuLf4O4Aa": "array:4 [\n  \"data\" => array:7 [\n    \"authType\" => \"\"\n    \"brandId\" => \"\"\n    \"storeId\" => \"\"\n    \"from\" => \"\"\n    \"to\" => \"\"\n    \"stores\" => Illuminate\\Database\\Eloquent\\Collection {#971\n      #items: array:2 [\n        0 => App\\Models\\Shop {#970\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 1\n            \"view_id\" => \"S-1\"\n            \"name\" => \"aaaa\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"000\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:52:51\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:52:51\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Shop {#968\n          #connection: \"mysql\"\n          #table: \"shops\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #original: array:12 [\n            \"id\" => 2\n            \"view_id\" => \"S-2\"\n            \"name\" => \"bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb\"\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"荒川区町屋\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:53:03\"\n            \"ins_id\" => 2\n            \"upd_date\" => \"2025-06-16 11:53:03\"\n            \"upd_id\" => 2\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:12 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"zip1\"\n            4 => \"zip2\"\n            5 => \"pref_id\"\n            6 => \"address\"\n            7 => \"del_flag\"\n            8 => \"ins_id\"\n            9 => \"ins_date\"\n            10 => \"upd_id\"\n            11 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"brands\" => Illuminate\\Database\\Eloquent\\Collection {#992\n      #items: array:10 [\n        0 => App\\Models\\Brand {#991\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 1\n            \"view_id\" => \"B-1\"\n            \"name\" => \"aaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:50:59\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:50:59\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        1 => App\\Models\\Brand {#989\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 2\n            \"view_id\" => \"B-2\"\n            \"name\" => \"bbb\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-16 11:51:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-16 11:51:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        2 => App\\Models\\Brand {#988\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 3\n            \"view_id\" => \"B-3\"\n            \"name\" => \"aaaa\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:00\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:00\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        3 => App\\Models\\Brand {#987\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 4\n            \"view_id\" => \"B-4\"\n            \"name\" => \"ccccc\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:09\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:09\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        4 => App\\Models\\Brand {#986\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 5\n            \"view_id\" => \"B-5\"\n            \"name\" => \"ádsada\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:17\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:17\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        5 => App\\Models\\Brand {#985\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 6\n            \"view_id\" => \"B-6\"\n            \"name\" => \"fdgdg\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:27\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:27\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        6 => App\\Models\\Brand {#984\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 7\n            \"view_id\" => \"B-7\"\n            \"name\" => \"gffghfh\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:33:35\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:33:35\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        7 => App\\Models\\Brand {#983\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 8\n            \"view_id\" => \"B-8\"\n            \"name\" => \"ưqsd\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:36:26\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:36:26\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        8 => App\\Models\\Brand {#982\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 9\n            \"view_id\" => \"B-9\"\n            \"name\" => \"fsdf\"\n            \"year_rate\" => \"1.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:41\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:41\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        9 => App\\Models\\Brand {#981\n          #connection: \"mysql\"\n          #table: \"brands\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #original: array:9 [\n            \"id\" => 10\n            \"view_id\" => \"B-10\"\n            \"name\" => \"sadasd\"\n            \"year_rate\" => \"2.00\"\n            \"del_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-27 13:37:49\"\n            \"ins_id\" => 1\n            \"upd_date\" => \"2025-06-27 13:37:49\"\n            \"upd_id\" => 1\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:9 [\n            0 => \"id\"\n            1 => \"view_id\"\n            2 => \"name\"\n            3 => \"year_rate\"\n            4 => \"del_flag\"\n            5 => \"ins_id\"\n            6 => \"ins_date\"\n            7 => \"upd_id\"\n            8 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"admin.balance.search\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0ceb4ce65af6e605135ffdc5d4e76fb6.php:8$3442\"\n  \"id\" => \"quCd0cyY8HCNuLf4O4Aa\"\n]", "common.nav-bar #k4enRuQeI5xo2T9XfGCx": "array:4 [\n  \"data\" => array:2 [\n    \"breadcrumbs\" => array:2 [\n      0 => array:2 [\n        \"label\" => \"ダッシュボード\"\n        \"url\" => \"http://127.0.0.1:8000/management\"\n      ]\n      1 => array:2 [\n        \"label\" => \"残高管理\"\n        \"url\" => \"http://127.0.0.1:8000/management/balances\"\n      ]\n    ]\n    \"isHideBreadcrumb\" => false\n  ]\n  \"name\" => \"common.nav-bar\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire\\common\\nav-bar.blade.php:8$345d\"\n  \"id\" => \"k4enRuQeI5xo2T9XfGCx\"\n]", "common.logout-modal #wSZKOQJkN907ITk4A5ej": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.logout-modal\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\acd2c66864e16c9a44c5d2061051a042.php:8$345e\"\n  \"id\" => \"wSZKOQJkN907ITk4A5ej\"\n]", "common.confirm #88wh4iNT0y2Rld64PnPR": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.confirm\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\21a91b4a087079d3e133812909232442.php:8$3461\"\n  \"id\" => \"88wh4iNT0y2Rld64PnPR\"\n]", "common.toast-message #zLd3RW5Xpew7PEJ53WBA": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.toast-message\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0264863d287fd82f4cefe2d814020f86.php:8$3462\"\n  \"id\" => \"zLd3RW5Xpew7PEJ53WBA\"\n]"}, "count": 6}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01JZS17WNHG9WMD5H0YSR2MDW7&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/management/balances_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management/balances", "action_name": "admin.balance.index", "controller_action": "Closure", "uri": "GET management/balances", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>", "middleware": "admin, locale, auth:admin", "duration": "272ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1770533214 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1770533214\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2020371513 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2020371513\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1529973204 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/management/payments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjliemhKQ2NSQmVpOWwrb2VBQnhKaVE9PSIsInZhbHVlIjoiSXhDNmJqdmtHU3Uvbm8ybWVHYzdoSFU1Ym1qZWF0ai9lRnlRb3JiMDFISVQ4N0dYTFJkcUNZL1M5OVJiVmRhaTJwclM3SXNjOStSRnpFZzcvb3BoUkY5RHZHQTZkdkJ1czZUaGNBZmQ5S0hpZEFrUGp5MTlJSU1aWnNUNWRtVjVvQTRLK1M4NG5JaHBpUzRWdE1vc2tBPT0iLCJtYWMiOiI0YzdhMTM3MWFlNWUxODUwNTJiOWM3NDkzMWU5NDY2NGI5MjljYmU0ZTgxN2QxYjVkYmY1OTg1ZTY5YmM5ZTJkIiwidGFnIjoiIn0%3D; ladybird_session=wlXlRruX2PfaGJavVPNjRis2N3hxByorNqLQ96CL; XSRF-TOKEN=eyJpdiI6ImZyNjNFSlUrQTVEV2FZZ1pmMllUUnc9PSIsInZhbHVlIjoieis3Q0lIR21mekJDVVk3djkyLzFyeUU3d0F6eVNzZlFhZE10d2tvNFM5NHJoU2JUZS9jZmthdVI1WmZZcTZIMmJvQmgwTUFJWk93bDF6alQzT3dIQ2VjaERHTlVXRi9vaE5TUnFxbzNXdzVlczhnM3hhTHM0dlhmbGhCYmpRYXkiLCJtYWMiOiJjZGIyNDZmNGFjMGIxNjEwZTRkM2FiYzBlOTIzOGZjN2NlM2EyY2ZiMmUyZDcyOTVhYWQ0MzJlNGRjMzYyMThhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529973204\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1856777060 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856777060\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-409787318 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 02:06:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Iml4OU5OM2hNU21UaFd4ZkVSUm5nSXc9PSIsInZhbHVlIjoiU2VvdC9TTFRaZEwzNEJqdEZEckRySzh0bnBBdzlwNGJnV0xGbVJCZ1RpNVNkR0tQdWdDT2tDTFRBa2x3aUl4Tnpma2o2MG5IRzFxTnc0UWU3SWZwTVBvQVZNSExUcXB3VTcyUFZVdEtaMHFCUnpmb2plM0l0dXo5ZUsvUHd1UFYiLCJtYWMiOiJlMmE5MTQ3MjFkNmFhMjcxZTIwMWY4YjY4ZWQyNmZkNzc0NDU5MTlmMmU3ZWZhMDc0ZGU5ZDNkNTM3Njk1YjcwIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 04:06:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Iml4OU5OM2hNU21UaFd4ZkVSUm5nSXc9PSIsInZhbHVlIjoiU2VvdC9TTFRaZEwzNEJqdEZEckRySzh0bnBBdzlwNGJnV0xGbVJCZ1RpNVNkR0tQdWdDT2tDTFRBa2x3aUl4Tnpma2o2MG5IRzFxTnc0UWU3SWZwTVBvQVZNSExUcXB3VTcyUFZVdEtaMHFCUnpmb2plM0l0dXo5ZUsvUHd1UFYiLCJtYWMiOiJlMmE5MTQ3MjFkNmFhMjcxZTIwMWY4YjY4ZWQyNmZkNzc0NDU5MTlmMmU3ZWZhMDc0ZGU5ZDNkNTM3Njk1YjcwIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 04:06:25 GMT; path=/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-409787318\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1796277012 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3DNVNHGoIowkwurbHuGq4xGScqYVMzbFzVAjwb1e</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01JZS17WNHG9WMD5H0YSR2MDW7&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/management/balances_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796277012\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management/balances", "action_name": "admin.balance.index", "controller_action": "Closure"}, "badge": null}}