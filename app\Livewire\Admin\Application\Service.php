<?php

namespace App\Livewire\Admin\Application;

use App\Enums\ApplicationTabEnum;
use App\Enums\CustomerFlagEnum;
use App\Livewire\Admin\Application\Forms\ServiceForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Services\ApplicationService;
use App\Services\ToastService;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;

#[Layout('components.layouts.application-layout')]
class Service extends BaseAdminPageComponent
{

    public ServiceForm $serviceForm;

    public $applicationId;

    public $application = null;

    public function __construct()
    {
        $this->page = ApplicationTabEnum::SERVICE;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
        $this->dispatch('init-select2');
    }

    public function mount($application_id)
    {
        $this->applicationId = $application_id;

        $this->application = app()->make(ApplicationRepository::class)->find($application_id);

        if (empty($this->application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }

        // prepare data for edit
        $this->prepareDataUpdate();
    }

    private function prepareDataUpdate()
    {
        $this->serviceForm->fill($this->application->toArray());
        $this->serviceForm->customer_flag = $this->application->customer_flag?->value ?? CustomerFlagEnum::SELF;
    }

    public function validateSave()
    {
        $this->serviceForm->validate();

        $this->dispatch('app-service-store', $this->serviceForm);
    }

    #[On('app-service-store')]
    public function store($body)
    {
        $applicationId = $this->applicationId;

        $application = app()->make(ApplicationService::class)->createApplicationService($applicationId, $body);

        if($application) {
            app(ToastService::class)->createSuccess();
            // redirect to tab service
            return redirect()->route('admin.customer.application.identification', ['application_id' => $application->id]);
        }
        app(ToastService::class)->createError();
    }

    public function render()
    {
        return $this->viewLivewireAdmin('application.service', [
            'application' => $this->application,
        ]);
    }
}
