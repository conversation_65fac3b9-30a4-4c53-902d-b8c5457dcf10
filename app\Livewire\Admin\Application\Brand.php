<?php

namespace App\Livewire\Admin\Application;

use App\Enums\ApplicationTabEnum;
use App\Enums\TypeEnum;
use App\Livewire\Admin\Application\Forms\BrandForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Repositories\ShopBrandRepository;
use App\Services\ApplicationService;
use App\Services\CourseService;
use App\Services\ToastService;
use Livewire\Attributes\Layout;

#[Layout('components.layouts.application-layout')]
class Brand extends BaseAdminPageComponent
{

    public BrandForm $saveForm;

    public $customerId;

    public $customer = null;

    public $application = null;

    public $brandShopList = [];

    public $productGeneral = [
        [
            'course_id' => '',
            'quantity' => 0,
            'amount' => '',
        ]
    ];

    public $productOptional = [
        [
            'course_id' => '',
            'quantity' => 0,
            'amount' => '',
        ]
    ];

    public $courseGeneralList = [];

    public $courseOptionalList = [];

    public $totalAmount = 0;

    public function __construct()
    {
        $this->page = ApplicationTabEnum::BRAND;
        parent::__construct();
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle .= $suffixTitle;
        $this->dispatch('init-select2');
    }

    public function mount($customer_id, $application_id = null)
    {
        // check exist customer
        $this->customerId = $customer_id;
        $this->customer = app()->make(CustomerRepository::class)->getBasicInfoCustomer($customer_id);
        $this->application = app()->make(ApplicationRepository::class)->find($application_id);

        if (empty($this->customer) && empty($this->application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }

        // init brand shop list
        $this->brandShopList = app()->make(ShopBrandRepository::class)->getAllByShopAdmin();

        // prepare data for mode edit
        if ($this->application) {
            $this->loadExistingEditData();
        }
    }

    public function validateSave()
    {
        // format form data before submit
        if (collect($this->saveForm?->product_optional)?->isEmpty()) {
            $this->saveForm->product_optional = null;
        }

        $this->saveForm->validate();

        $this->save();
    }

    public function save()
    {
        $body = $this->saveForm->toArray();

        $customerId = $this->customerId;

        if ($this->application) {
            $application = app()->make(ApplicationService::class)->updateApplicationBrand($this->application, $body);
        } else {
            $application = app()->make(ApplicationService::class)->createApplicationBrand($customerId, $body);
        }

        if ($application) {
            app(ToastService::class)->createSuccess();
            // redirect to tab payment
            return redirect()->route('admin.customer.application.payment', ['application_id' => $application->id]);
        }
        app(ToastService::class)->createError();
    }

    public function render()
    {
        // init product data
        $this->courseGeneralList = app()->make(CourseService::class)->getAllCourseByType(TypeEnum::NORMAL, $this->saveForm?->fee_type);
        $this->courseOptionalList = app()->make(CourseService::class)->getAllCourseByType(TypeEnum::OPTIONAL, $this->saveForm?->fee_type);

        return $this->viewLivewireAdmin('application.brand', [
            'productGeneral' => $this->productGeneral,
            'productOptional' => $this->productOptional,
            'courseGeneralList' => $this->courseGeneralList,
            'courseOptionalList' => $this->courseOptionalList,
            'brandShopList' => $this->brandShopList,
            'totalAmount' => $this->totalAmount,
            'application' => $this->application,
        ]);
    }

    /**
     * Load existing product data from application_courses for editing mode
     */
    private function loadExistingEditData()
    {
        // Load basic application data
        $application = $this->application;
        $this->saveForm->shop_brand_id = $application->shop_brand_id;
        $this->saveForm->staff_name = $application->staff_name;
        $this->saveForm->fee_type = $application->fee_type?->value;

        $productGeneral = $application?->applicationCourses?->map(function ($item) {
            if ($item?->course?->type?->value == TypeEnum::NORMAL) {
                return [
                    'application_course_id' => $item->id,
                    'course_id' => $item->course_id,
                    'quantity' => $item->count,
                    'amount' => format_float($item->amount),
                ];
            }
            return null;
        })->filter()->toArray() ?? [];

        $productOptional = $application?->applicationCourses?->map(function ($item) {
            if ($item?->course?->type?->value == TypeEnum::OPTIONAL) {
                return [
                    'application_course_id' => $item->id,
                    'course_id' => $item->course_id,
                    'quantity' => $item->count,
                    'amount' => format_float($item->amount),
                ];
            }
            return null;
        })->filter()->toArray() ?? [];

        $this->productGeneral = !empty($productGeneral) ? $productGeneral : [
            [
                'course_id' => '',
                'quantity' => 0,
                'amount' => '',
            ]
        ];
        $this->productOptional = !empty($productOptional) ? $productOptional : [
            [
                'course_id' => '',
                'quantity' => 0,
                'amount' => '',
            ]
        ];;

        $this->saveForm->product_optional = $productOptional;
        $this->saveForm->product_general = $productGeneral;
    }

    public function addGeneral()
    {
        $this->productGeneral[] = [
            'course_id' => '',
            'quantity' => 0,
            'amount' => '',
        ];
    }

    public function removeGeneral($index)
    {
        unset($this->productGeneral[$index]);
        $this->productGeneral = array_values($this->productGeneral); // reindex
        if (isset($this->saveForm->product_general)) {
            unset($this->saveForm->product_general[$index]);
            $this->saveForm->product_general = array_values($this->saveForm->product_general);
        }
    }

    public function addOptional()
    {
        $this->productOptional[] = [
            'course_id' => '',
            'quantity' => 0,
            'amount' => '',
        ];
    }

    public function removeOptional($index)
    {
        unset($this->productOptional[$index]);
        $this->productOptional = array_values($this->productOptional); // reindex
        if (isset($this->saveForm->product_optional)) {
            unset($this->saveForm->product_optional[$index]);
            $this->saveForm->product_optional = array_values($this->saveForm->product_optional);
        }
    }

    public function incrementQtyGeneral($index)
    {
        $current = intval($this->saveForm->product_general[$index]['quantity'] ?? 0);
        $new = $current + 1;
        $this->productGeneral[$index]['quantity'] = $new;
        $this->saveForm->product_general[$index]['quantity'] = $new;
    }

    public function decrementQtyGeneral($index)
    {
        $current = intval($this->saveForm->product_general[$index]['quantity'] ?? 0);
        $new = $current - 1;
        $this->productGeneral[$index]['quantity'] = $new;
        $this->saveForm->product_general[$index]['quantity'] = $new;
    }

    public function incrementQtyOptional($index)
    {
        $current = intval($this->saveForm->product_optional[$index]['quantity'] ?? 0);
        $new = $current + 1;
        $this->productOptional[$index]['quantity'] = $new;
        $this->saveForm->product_optional[$index]['quantity'] = $new;
    }

    public function decrementQtyOptional($index)
    {
        $current = intval($this->saveForm->product_optional[$index]['quantity'] ?? 0);
        $new = $current - 1;
        $this->productOptional[$index]['quantity'] = $new;
        $this->saveForm->product_optional[$index]['quantity'] = $new;
    }

    public function clearProductSelections()
    {
        $this->saveForm->product_general = [
            [
                'course_id' => '',
                'quantity' => 0,
                'amount' => '',
            ]
        ];

        $this->saveForm->product_optional = [
            [
                'course_id' => '',
                'quantity' => 0,
                'amount' => '',
            ]
        ];

        $this->productGeneral = $this->saveForm->product_general;
        $this->productOptional = $this->saveForm->product_optional;
    }
}
