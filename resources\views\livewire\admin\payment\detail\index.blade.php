@php
    $func = \App\Enums\FunctionEnum::PAYMENT;
@endphp
<div id="contents" class="scroll-validate">
    <div class="contents-container">
        <div class="container-fluid">
            <div class="page-detail-header d-flex justify-content-between">
                <h1 class="page-detail-header-name">
                    <div class="name-group">
                        <div class="name-kana">{{ $loanSchedule->customer?->last_name_kana }}</div>
                        <div class="name-field">{{ $loanSchedule->customer?->last_name }}</div>
                    </div>
                    <div class="name-group">
                        <div class="name-kana">{{ $loanSchedule->customer?->first_name_kana }}</div>
                        <div class="name-field">{{ $loanSchedule->customer?->first_name }}</div>
                    </div>
                    <div class="mb-2">{{ trans2('screens.payment.detail.deposit_money') }}</div>
                </h1>
                <div class="page-detail-header-link">
                    <a href="{{ getRoute('payment.index') }}" class="btn-text-prev small">{{ trans2('screens.payment.detail.back') }}</a>
                </div>
            </div>

            <ul class="page-detail-tabs nav nav-tabs" wire:ignore>
                <li class="nav-item">
                    <a href="{{ getRoute('payment.deposit-list.index', ['payment_id' => $loanSchedule->id]) }}" class="nav-link {{ (request()->routeIs('admin.payment.deposit-list.index')) ? 'active' : '' }}">
                        {{ trans2('screens.contract.detail.deposit_tab') }}
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ getRoute('payment.details', ['id' => $loanSchedule->id]) }}" class="nav-link {{ request()->routeIs('admin.payment.details') || request()->routeIs('admin.payment.details.registration') ? 'active' : '' }}">{{ trans2('screens.payment.detail.detail_tab') }}</a>
                </li>
            </ul>

            {{--  slot content page detail --}}
            @yield('detail-tab')

            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ getRoute('payment.index') }}" class="btn-text-prev small">{{ trans2('screens.payment.detail.back') }}</a>

                <div>
                    <livewire:admin.payment.delete :id="data_get($loanSchedule, 'id')" :isCanDelete="$isCanDelete"/>
                </div>
            </div>
        </div>
    </div>
</div>
