<?php

namespace App\Livewire\Admin\Contract\ApplicationPayment;

use App\Enums\ContractStatusEnum;
use App\Enums\SidebarMenuEnum;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Services\LoanScheduleService;
use App\Services\ApplicationService;
use App\Services\CustomerService;
use App\Services\ToastService;
use Gate;

class Index extends BaseAdminPageComponent
{
    public $contract_id;
    public $isCanDelete;
    public $customer;
    public $loanSchedules = null;
    public $paymentDatas = null;
    public $contract = null;

    public function __construct()
    {
        $this->page = SidebarMenuEnum::CONTRACT;
        parent::__construct();
        $subTitle = trans2('screens.contract.deposit_tab.page_title');
        $suffixTitle = '｜' . '' . trans2('project_name');
        $this->pageTitle = $this->pageTitle . $subTitle . $suffixTitle;
    }

    public function mount($contract_id)
    {
        $this->contract_id = $contract_id;
        $this->contract = app()->make(ApplicationService::class)->getContractByApplication($contract_id);

        if (empty($this->contract)) {
            app(ToastService::class)->error(__('messages.no_data'));
            return $this->redirect(route('admin.contract.index'));
        }

        $this->loanSchedules = app()->make(LoanScheduleService::class)->getListByApplication($contract_id);
        $this->paymentDatas = app()->make(LoanScheduleService::class)->getListForPaymentTableByApplication($contract_id);

        $this->brandIds = collect($this->contract->brand_id);
        $this->shopIds = collect($this->contract->shop_id);
        Gate::authorize('allowed-update', [SidebarMenuEnum::CONTRACT, $this->brandIds, $this->shopIds]);

        $this->isCanDelete = $this->isCanDelete();
    }

    protected function isCanDelete()
    {
        if ($this->contract->contract_status) {
            return in_array($this->contract->contract_status->value, ContractStatusEnum::canDeleteStatus());
        }
        return true;
    }

    public function render()
    {
        return $this->viewLivewireAdmin('contract.application-payment.index', [
            'loanSchedules' => $this->loanSchedules,
            'paymentDatas' => $this->paymentDatas,
        ]);
    }
}
