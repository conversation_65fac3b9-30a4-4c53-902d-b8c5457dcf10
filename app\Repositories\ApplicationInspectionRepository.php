<?php

namespace App\Repositories;

use App\Models\ApplicationInspectionStatus;

class ApplicationInspectionRepository extends CustomRepository
{
    protected $model = ApplicationInspectionStatus::class;

    public function getApplicationInspectionStatus($applicationId)
    {
        return $this->model::where('application_id', $applicationId)
            ->orderBy('ins_date', 'desc')
            ->get();
    }

    public function getApplicationInspectionStatusCount($status)
    {
        $currentUser = getCurrentUser();
        $q = $this->whereIn('status', $status);

        if($currentUser->isAdmin()){
            $q->whereNull('to_shop_id');
        }

        if ($currentUser->isBrand()) {
            $q->whereIn('to_shop_id', app(ShopBrandRepository::class)->getAllByBrandAdmin()->pluck('shop_id'));
        }

        if($currentUser->isStore()){
            $q->whereIn('to_shop_id', $currentUser->listShopIds());
        }

        return $q->count();
    }
}
