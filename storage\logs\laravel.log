[2025-07-17 19:09:04] local.DEBUG: (Time: 02.87) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.56) SQL: select * from `shop_brands` where `shop_brands`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.52) SQL: select * from `shops` where `shops`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.47) SQL: select * from `shops` where `shops`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.53) SQL: select * from `brands` where `brands`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 25.16) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.65) SQL: select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.35) SQL: select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.34) SQL: select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.56) SQL: select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.36) SQL: select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.31) SQL: select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:04] local.DEBUG: (Time: 00.33) SQL: select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.38) SQL: select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.28) SQL: select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.26) SQL: select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.26) SQL: select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.26) SQL: select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.36) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.43) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.38) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.28) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 11.99) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 01.21) SQL: select applications.id, applications.payment_start_month AS target_month,

            COUNT(applications.id) AS total_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.id END) AS new_contracts,

            SUM(CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.total_amount ELSE 0 END) AS new_contract_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 2 THEN applications.id END) AS completed_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 3 THEN applications.id END) AS cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 3 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS cancelled_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 6 THEN applications.id END) AS forced_cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 6 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS forced_contract_cancel_amount,

            COUNT(DISTINCT loan_arrears.application_id) AS uncollected_contracts,
            SUM(IFNULL(loan_arrears.amount, 0)) AS uncollected_amount from `applications` left join (select `loan_arrears`.`application_id`, SUM(amount) as amount from `loan_arrears` where `loan_arrears`.`del_flag` = 0 group by `loan_arrears`.`application_id`) as `loan_arrears` on `loan_arrears`.`application_id` = `applications`.`id` where `applications`.`del_flag` = 0 group by `applications`.`payment_start_month` order by `applications`.`payment_start_month` asc {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 23.91) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.45) SQL: select * from `shops` where `shops`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.49) SQL: select * from `brands` where `brands`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.45) SQL: select * from `shops` where `shops`.`id` in (1, 2) {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.38) SQL: select * from `brands` where `brands`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) {"path":"admin.sql"} 
[2025-07-17 19:09:05] local.DEBUG: (Time: 00.70) SQL: select applications.id, applications.payment_start_month AS target_month,

            COUNT(applications.id) AS total_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.id END) AS new_contracts,

            SUM(CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.total_amount ELSE 0 END) AS new_contract_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 2 THEN applications.id END) AS completed_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 3 THEN applications.id END) AS cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 3 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS cancelled_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 6 THEN applications.id END) AS forced_cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 6 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS forced_contract_cancel_amount,

            COUNT(DISTINCT loan_arrears.application_id) AS uncollected_contracts,
            SUM(IFNULL(loan_arrears.amount, 0)) AS uncollected_amount from `applications` left join (select `loan_arrears`.`application_id`, SUM(amount) as amount from `loan_arrears` where `loan_arrears`.`del_flag` = 0 group by `loan_arrears`.`application_id`) as `loan_arrears` on `loan_arrears`.`application_id` = `applications`.`id` where `payment_start_month` >= '202507' and `payment_start_month` <= '202507' and `applications`.`del_flag` = 0 group by `applications`.`payment_start_month` order by `applications`.`payment_start_month` asc {"path":"admin.sql"} 
[2025-07-17 19:09:11] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746951) or (`reserved_at` <= 1752746861)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:15] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746955) or (`reserved_at` <= 1752746865)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:19] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746959) or (`reserved_at` <= 1752746869)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:23] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746963) or (`reserved_at` <= 1752746873)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:25] local.DEBUG: (Time: 02.99) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:09:25] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (default, 0, ?, 1752746965, 1752746965, {"uuid":"8251849b-4dbe-4317-9002-c43f8bd99fad","displayName":"App\\Jobs\\CalculateDamageFees","job":"Illuminate\\Queue\\CallQueuedHandler@call","maxTries":null,"maxExceptions":null,"failOnTimeout":false,"backoff":null,"timeout":null,"retryUntil":null,"data":{"commandName":"App\\Jobs\\CalculateDamageFees","command":"O:28:\"App\\Jobs\\CalculateDamageFees\":1:{s:11:\"\u0000*\u0000schedule\";O:45:\"Illuminate\\Contracts\\Database\\ModelIdentifier\":5:{s:5:\"class\";s:23:\"App\\Models\\LoanSchedule\";s:2:\"id\";i:1;s:9:\"relations\";a:0:{}s:10:\"connection\";s:5:\"mysql\";s:15:\"collectionClass\";N;}}"}}))
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('insert into `jo...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php(34): Illuminate\Database\Connection->run('insert into `jo...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Processors\MySqlProcessor.php(35): Illuminate\Database\MySqlConnection->insert('insert into `jo...', Array, NULL)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3549): Illuminate\Database\Query\Processors\MySqlProcessor->processInsertGetId(Object(Illuminate\Database\Query\Builder), 'insert into `jo...', Array, NULL)
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(185): Illuminate\Database\Query\Builder->insertGetId(Array)
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(96): Illuminate\Queue\DatabaseQueue->pushToDatabase(NULL, '{"uuid":"825184...')
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Queue.php(342): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}('{"uuid":"825184...', NULL, NULL)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(90): Illuminate\Queue\Queue->enqueueUsing(Object(App\Jobs\CalculateDamageFees), '{"uuid":"825184...', NULL, NULL, Object(Closure))
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Bus\Dispatcher.php(254): Illuminate\Queue\DatabaseQueue->push(Object(App\Jobs\CalculateDamageFees))
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Bus\Dispatcher.php(230): Illuminate\Bus\Dispatcher->pushCommandToQueue(Object(Illuminate\Queue\DatabaseQueue), Object(App\Jobs\CalculateDamageFees))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Bus\Dispatcher.php(77): Illuminate\Bus\Dispatcher->dispatchToQueue(Object(App\Jobs\CalculateDamageFees))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Bus\PendingDispatch.php(193): Illuminate\Bus\Dispatcher->dispatch(Object(App\Jobs\CalculateDamageFees))
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Bus\Dispatchable.php(19): Illuminate\Foundation\Bus\PendingDispatch->__destruct()
#13 C:\xampp\htdocs\ladybird\app\Console\Commands\CalculateDelayDamageFees.php(50): App\Jobs\CalculateDamageFees::dispatch(Object(App\Models\LoanSchedule))
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): App\Console\Commands\CalculateDelayDamageFees->handle()
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#20 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#23 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(App\Console\Commands\CalculateDelayDamageFees), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:27] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746967) or (`reserved_at` <= 1752746877)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:31] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746971) or (`reserved_at` <= 1752746881)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:35] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746975) or (`reserved_at` <= 1752746885)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:39] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746979) or (`reserved_at` <= 1752746889)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:43] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746983) or (`reserved_at` <= 1752746893)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:47] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746987) or (`reserved_at` <= 1752746897)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:51] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746991) or (`reserved_at` <= 1752746901)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:55] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746995) or (`reserved_at` <= 1752746905)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:09:59] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752746999) or (`reserved_at` <= 1752746909)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:03] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747003) or (`reserved_at` <= 1752746913)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:07] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747007) or (`reserved_at` <= 1752746917)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:11] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747011) or (`reserved_at` <= 1752746921)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:15] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747015) or (`reserved_at` <= 1752746925)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:19] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747019) or (`reserved_at` <= 1752746929)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:23] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747023) or (`reserved_at` <= 1752746933)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:27] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747027) or (`reserved_at` <= 1752746937)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:31] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747031) or (`reserved_at` <= 1752746941)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:35] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747035) or (`reserved_at` <= 1752746945)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:39] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747039) or (`reserved_at` <= 1752746949)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:43] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747043) or (`reserved_at` <= 1752746953)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:47] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747047) or (`reserved_at` <= 1752746957)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:51] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747051) or (`reserved_at` <= 1752746961)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:55] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747055) or (`reserved_at` <= 1752746965)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:10:59] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747059) or (`reserved_at` <= 1752746969)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:11:03] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747063) or (`reserved_at` <= 1752746973)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:11:07] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747067) or (`reserved_at` <= 1752746977)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:11:11] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747071) or (`reserved_at` <= 1752746981)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:11:15] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747075) or (`reserved_at` <= 1752746985)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:11:19] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747079) or (`reserved_at` <= 1752746989)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:11:23] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747083) or (`reserved_at` <= 1752746993)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:11:27] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'local-ladybird.jobs' doesn't exist (Connection: mysql, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1752747087) or (`reserved_at` <= 1752746997)) order by `id` asc limit 1 for update)
#0 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php(414): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2913): Illuminate\Database\Connection->select('select * from `...', Array, false)
#3 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2902): Illuminate\Database\Query\Builder->runSelect()
#4 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3456): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#5 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(2901): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\BuildsQueries.php(333): Illuminate\Database\Query\Builder->get(Array)
#7 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(246): Illuminate\Database\Query\Builder->first()
#8 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(224): Illuminate\Queue\DatabaseQueue->getNextAvailableJob('default')
#9 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Concerns\ManagesTransactions.php(30): Illuminate\Queue\DatabaseQueue->Illuminate\Queue\{closure}(Object(Illuminate\Database\MySqlConnection))
#10 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\DatabaseQueue.php(223): Illuminate\Database\Connection->transaction(Object(Closure))
#11 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(349): Illuminate\Queue\DatabaseQueue->pop('default')
#12 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(363): Illuminate\Queue\Worker->Illuminate\Queue\{closure}('default')
#13 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Worker.php(162): Illuminate\Queue\Worker->getNextJob(Object(Illuminate\Queue\DatabaseQueue), 'default')
#14 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(137): Illuminate\Queue\Worker->daemon('database', 'default', Object(Illuminate\Queue\WorkerOptions))
#15 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Queue\Console\WorkCommand.php(120): Illuminate\Queue\Console\WorkCommand->runWorker('database', 'default')
#16 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): Illuminate\Queue\Console\WorkCommand->handle()
#17 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#18 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#19 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#20 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Container\Container.php(662): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#21 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(211): Illuminate\Container\Container->call(Array)
#22 C:\xampp\htdocs\ladybird\vendor\symfony\console\Command\Command.php(326): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#23 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Console\Command.php(180): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#24 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(1096): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#25 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(324): Symfony\Component\Console\Application->doRunCommand(Object(Illuminate\Queue\Console\WorkCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#26 C:\xampp\htdocs\ladybird\vendor\symfony\console\Application.php(175): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#27 C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(201): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#28 C:\xampp\htdocs\ladybird\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#29 {main}","[F]C:\xampp\htdocs\ladybird\vendor\laravel\framework\src\Illuminate\Database\Connection.php",[L]829 {"path":""} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 03.56) SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'local-ladybird' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 00.86) SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'local-ladybird' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 01.48) SQL: select `migration` from `migrations` order by `batch` asc, `migration` asc {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 00.45) SQL: select `migration` from `migrations` order by `batch` asc, `migration` asc {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 00.31) SQL: select max(`batch`) as aggregate from `migrations` {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 29.85) SQL: create table `loan_withdrawals` (`id` bigint unsigned not null auto_increment primary key, `customer_id` int not null comment '顧客ID', `application_id` int not null comment '申込ID', `withdrawal_date` date not null comment '出金日', `amount` decimal(10, 2) not null comment '入金額', `withdrawal_type` char(1) not null comment '出金種別', `del_flag` char(1) not null default '0' comment '削除フラグ', `ins_date` timestamp not null default CURRENT_TIMESTAMP comment '登録日時', `ins_id` int unsigned not null comment '登録者ID', `upd_date` timestamp null comment '更新日時', `upd_id` int unsigned null comment '更新者ID') default character set utf8mb4 collate 'utf8mb4_unicode_ci' {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 24.54) SQL: alter table `loan_withdrawals` add index `loan_withdrawals_customer_id_index`(`customer_id`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 23.25) SQL: alter table `loan_withdrawals` add index `loan_withdrawals_application_id_index`(`application_id`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 21.93) SQL: alter table `loan_withdrawals` add index `loan_withdrawals_del_flag_index`(`del_flag`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 03.94) SQL: insert into `migrations` (`migration`, `batch`) values ('2025_07_08_132624_create_loan_withdrawals_table', 10) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 17.02) SQL: alter table `loan_transaction_logs` add `loan_withdrawal_id` int not null comment 'ローン出金ID' after `loan_arrear_id` {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 23.00) SQL: alter table `loan_transaction_logs` add index `loan_transaction_logs_loan_withdrawal_id_index`(`loan_withdrawal_id`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 03.90) SQL: insert into `migrations` (`migration`, `batch`) values ('2025_07_08_132933_update_loan_transaction_logs_table', 10) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 21.46) SQL: alter table `loan_refunds` add `loan_withdrawal_id` int not null comment 'ローン出金ID' after `loan_payment_id` {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 21.76) SQL: alter table `loan_refunds` add index `loan_refunds_loan_withdrawal_id_index`(`loan_withdrawal_id`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 03.73) SQL: insert into `migrations` (`migration`, `batch`) values ('2025_07_08_134506_update_loan_refund_table', 10) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 13.44) SQL: alter table `loan_overpayments` add `loan_refund_id` int not null comment 'ローン返金ID' after `loan_payment_id` {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 24.80) SQL: alter table `loan_overpayments` add index `loan_overpayments_loan_refund_id_index`(`loan_refund_id`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 03.56) SQL: insert into `migrations` (`migration`, `batch`) values ('2025_07_08_134626_update_loan_overpayment_table', 10) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 17.55) SQL: create table `customer_brands` (`id` int unsigned not null auto_increment primary key comment 'ID', `customer_id` int not null comment '顧客ID', `brand_id` int not null comment 'ブランドID', `del_flag` char(1) not null default '0' comment '削除フラグ', `ins_date` timestamp not null default CURRENT_TIMESTAMP comment '登録日時', `ins_id` int unsigned not null comment '登録者ID', `upd_date` timestamp null comment '更新日時', `upd_id` int unsigned null comment '更新者ID') default character set utf8mb4 collate 'utf8mb4_unicode_ci' {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 20.95) SQL: alter table `customer_brands` add index `customer_brands_customer_id_index`(`customer_id`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 21.05) SQL: alter table `customer_brands` add index `customer_brands_brand_id_index`(`brand_id`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 19.73) SQL: alter table `customer_brands` add index `customer_brands_del_flag_index`(`del_flag`) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 03.34) SQL: insert into `migrations` (`migration`, `batch`) values ('2025_07_09_233312_create_customer_brands_table', 10) {"path":"batch.sql"} 
[2025-07-17 19:11:30] local.DEBUG: (Time: 03.28) SQL: insert into `migrations` (`migration`, `batch`) values ('2025_07_17_191112_create_jobs_table', 10) {"path":"batch.sql"} 
[2025-07-17 19:11:38] local.DEBUG: (Time: 03.56) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:11:40] local.DEBUG: (Time: 00.47) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.85) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 01.32) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.36) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.40) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.46) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.48) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.45) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-05 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.48) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.47) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.41) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:11:41] local.DEBUG: (Time: 00.38) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:13:48] local.DEBUG: (Time: 04.16) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 01.06) SQL: select * from `shop_brands` where `shop_brands`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 01.54) SQL: select * from `shops` where `shops`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.48) SQL: select * from `shops` where `shops`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 01.36) SQL: select * from `brands` where `brands`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 01.51) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 02.44) SQL: select applications.id, applications.payment_start_month AS target_month,

            COUNT(applications.id) AS total_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.id END) AS new_contracts,

            SUM(CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.total_amount ELSE 0 END) AS new_contract_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 2 THEN applications.id END) AS completed_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 3 THEN applications.id END) AS cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 3 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS cancelled_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 6 THEN applications.id END) AS forced_cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 6 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS forced_contract_cancel_amount,

            COUNT(DISTINCT loan_arrears.application_id) AS uncollected_contracts,
            SUM(IFNULL(loan_arrears.amount, 0)) AS uncollected_amount from `applications` left join (select `loan_arrears`.`application_id`, SUM(amount) as amount from `loan_arrears` where `loan_arrears`.`del_flag` = 0 group by `loan_arrears`.`application_id`) as `loan_arrears` on `loan_arrears`.`application_id` = `applications`.`id` where `applications`.`del_flag` = 0 group by `applications`.`payment_start_month` order by `applications`.`payment_start_month` asc {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 01.74) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 02.17) SQL: select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.43) SQL: select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.46) SQL: select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.92) SQL: select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.47) SQL: select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.48) SQL: select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.49) SQL: select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.40) SQL: select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.47) SQL: select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.38) SQL: select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 01.10) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.34) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.36) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:49] local.DEBUG: (Time: 00.52) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:50] local.DEBUG: (Time: 01.61) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {"path":"admin.sql"} 
[2025-07-17 19:13:50] local.DEBUG: (Time: 00.40) SQL: select * from `shops` where `shops`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:50] local.DEBUG: (Time: 00.39) SQL: select * from `brands` where `brands`.`del_flag` = '0' {"path":"admin.sql"} 
[2025-07-17 19:13:50] local.DEBUG: (Time: 00.41) SQL: select * from `shops` where `shops`.`id` in (1, 2) {"path":"admin.sql"} 
[2025-07-17 19:13:50] local.DEBUG: (Time: 00.50) SQL: select * from `brands` where `brands`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10) {"path":"admin.sql"} 
[2025-07-17 19:13:50] local.DEBUG: (Time: 00.73) SQL: select applications.id, applications.payment_start_month AS target_month,

            COUNT(applications.id) AS total_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.id END) AS new_contracts,

            SUM(CASE WHEN applications.contract_status = 1
                AND applications.payment_start_month = applications.payment_start_month
                THEN applications.total_amount ELSE 0 END) AS new_contract_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 2 THEN applications.id END) AS completed_contracts,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 3 THEN applications.id END) AS cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 3 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS cancelled_amount,

            COUNT(DISTINCT CASE WHEN applications.contract_status = 6 THEN applications.id END) AS forced_cancelled_contracts,
            SUM(CASE WHEN applications.contract_status = 6 THEN IFNULL(applications.contract_cancel_amount, 0) ELSE 0 END) AS forced_contract_cancel_amount,

            COUNT(DISTINCT loan_arrears.application_id) AS uncollected_contracts,
            SUM(IFNULL(loan_arrears.amount, 0)) AS uncollected_amount from `applications` left join (select `loan_arrears`.`application_id`, SUM(amount) as amount from `loan_arrears` where `loan_arrears`.`del_flag` = 0 group by `loan_arrears`.`application_id`) as `loan_arrears` on `loan_arrears`.`application_id` = `applications`.`id` where `payment_start_month` >= '202507' and `payment_start_month` <= '202507' and `applications`.`del_flag` = 0 group by `applications`.`payment_start_month` order by `applications`.`payment_start_month` asc {"path":"admin.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 02.98) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.37) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.32) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.35) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.36) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.32) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.31) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.24) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.25) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-05 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.26) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.26) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.22) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:14:27] local.DEBUG: (Time: 00.29) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:18:09] local.DEBUG: (Time: 03.00) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.44) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.65) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.35) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.47) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.40) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.31) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.46) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.39) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-05 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.36) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.44) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.41) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:18:34] local.DEBUG: (Time: 00.31) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:19:35] local.DEBUG: (Time: 02.98) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:20:04] local.DEBUG: (Time: 03.05) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:20:16] local.DEBUG: (Time: 02.90) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:21:04] local.DEBUG: (Time: 02.52) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:44] local.DEBUG: (Time: 02.91) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.43) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.45) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.INFO: Updating loan_arrears  
[2025-07-17 19:22:51] local.INFO: Amount: 1.00  
[2025-07-17 19:22:51] local.INFO: Payment Plan Date: 2025/06/27  
[2025-07-17 19:22:51] local.INFO: Today: 2025-07-26  
[2025-07-17 19:22:51] local.INFO: Delay Days: 21  
[2025-07-17 19:22:51] local.INFO: Year: 2025  
[2025-07-17 19:22:51] local.INFO: Year Days: 365  
[2025-07-17 19:22:51] local.INFO: Penalty: 0  
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.34) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.43) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.28) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.INFO: Updating loan_arrears  
[2025-07-17 19:22:51] local.INFO: Amount: 11.00  
[2025-07-17 19:22:51] local.INFO: Payment Plan Date: 2025/06/26  
[2025-07-17 19:22:51] local.INFO: Today: 2025-07-26  
[2025-07-17 19:22:51] local.INFO: Delay Days: 22  
[2025-07-17 19:22:51] local.INFO: Year: 2025  
[2025-07-17 19:22:51] local.INFO: Year Days: 365  
[2025-07-17 19:22:51] local.INFO: Penalty: 0  
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.25) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.43) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.38) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-05 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.INFO: Updating loan_arrears  
[2025-07-17 19:22:51] local.INFO: Amount: 11.00  
[2025-07-17 19:22:51] local.INFO: Payment Plan Date: 2025/06/05  
[2025-07-17 19:22:51] local.INFO: Today: 2025-07-26  
[2025-07-17 19:22:51] local.INFO: Delay Days: 37  
[2025-07-17 19:22:51] local.INFO: Year: 2025  
[2025-07-17 19:22:51] local.INFO: Year Days: 365  
[2025-07-17 19:22:51] local.INFO: Penalty: 0  
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.26) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.44) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.34) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.INFO: Updating loan_arrears  
[2025-07-17 19:22:51] local.INFO: Amount: 11.00  
[2025-07-17 19:22:51] local.INFO: Payment Plan Date: 2025/05/26  
[2025-07-17 19:22:51] local.INFO: Today: 2025-07-26  
[2025-07-17 19:22:51] local.INFO: Delay Days: 45  
[2025-07-17 19:22:51] local.INFO: Year: 2025  
[2025-07-17 19:22:51] local.INFO: Year Days: 365  
[2025-07-17 19:22:51] local.INFO: Penalty: 0  
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.34) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.53) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.35) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.INFO: Updating loan_arrears  
[2025-07-17 19:22:51] local.INFO: Amount: 1.00  
[2025-07-17 19:22:51] local.INFO: Payment Plan Date: 2025/06/27  
[2025-07-17 19:22:51] local.INFO: Today: 2025-07-26  
[2025-07-17 19:22:51] local.INFO: Delay Days: 21  
[2025-07-17 19:22:51] local.INFO: Year: 2025  
[2025-07-17 19:22:51] local.INFO: Year Days: 365  
[2025-07-17 19:22:51] local.INFO: Penalty: 0  
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.33) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.37) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.34) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.INFO: Updating loan_arrears  
[2025-07-17 19:22:51] local.INFO: Amount: 11.00  
[2025-07-17 19:22:51] local.INFO: Payment Plan Date: 2025/06/26  
[2025-07-17 19:22:51] local.INFO: Today: 2025-07-26  
[2025-07-17 19:22:51] local.INFO: Delay Days: 22  
[2025-07-17 19:22:51] local.INFO: Year: 2025  
[2025-07-17 19:22:51] local.INFO: Year Days: 365  
[2025-07-17 19:22:51] local.INFO: Penalty: 0  
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.34) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.36) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.34) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-05 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.INFO: Updating loan_arrears  
[2025-07-17 19:22:51] local.INFO: Amount: 11.00  
[2025-07-17 19:22:51] local.INFO: Payment Plan Date: 2025/06/05  
[2025-07-17 19:22:51] local.INFO: Today: 2025-07-26  
[2025-07-17 19:22:51] local.INFO: Delay Days: 37  
[2025-07-17 19:22:51] local.INFO: Year: 2025  
[2025-07-17 19:22:51] local.INFO: Year Days: 365  
[2025-07-17 19:22:51] local.INFO: Penalty: 0  
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.63) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.46) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.36) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:22:51] local.INFO: Updating loan_arrears  
[2025-07-17 19:22:51] local.INFO: Amount: 11.00  
[2025-07-17 19:22:51] local.INFO: Payment Plan Date: 2025/05/26  
[2025-07-17 19:22:51] local.INFO: Today: 2025-07-26  
[2025-07-17 19:22:51] local.INFO: Delay Days: 45  
[2025-07-17 19:22:51] local.INFO: Year: 2025  
[2025-07-17 19:22:51] local.INFO: Year Days: 365  
[2025-07-17 19:22:51] local.INFO: Penalty: 0  
[2025-07-17 19:22:51] local.DEBUG: (Time: 00.42) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:32:05] local.DEBUG: (Time: 02.90) SQL: select `customer_id`, `application_id`, `payment_plan_date`, `amount`, `id` from `loan_schedules` where `payment_status` = 2 and `payment_plan_date` < '2025-07-01 00:00:00' and `loan_schedules`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.45) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 1 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.47) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-27 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.INFO: Updating loan_arrears  
[2025-07-17 19:32:12] local.INFO: Amount: 2000.00  
[2025-07-17 19:32:12] local.INFO: Payment Plan Date: 2025/06/27  
[2025-07-17 19:32:12] local.INFO: Today: 2025-07-26  
[2025-07-17 19:32:12] local.INFO: Delay Days: 21  
[2025-07-17 19:32:12] local.INFO: Year: 2025  
[2025-07-17 19:32:12] local.INFO: Year Days: 365  
[2025-07-17 19:32:12] local.INFO: Penalty: 23  
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.37) SQL: select * from `loan_arrears` where `loan_schedule_id` = 1 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.44) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 5 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.37) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.INFO: Updating loan_arrears  
[2025-07-17 19:32:12] local.INFO: Amount: 2000.00  
[2025-07-17 19:32:12] local.INFO: Payment Plan Date: 2025/06/26  
[2025-07-17 19:32:12] local.INFO: Today: 2025-07-26  
[2025-07-17 19:32:12] local.INFO: Delay Days: 22  
[2025-07-17 19:32:12] local.INFO: Year: 2025  
[2025-07-17 19:32:12] local.INFO: Year Days: 365  
[2025-07-17 19:32:12] local.INFO: Penalty: 24  
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.83) SQL: select * from `loan_arrears` where `loan_schedule_id` = 5 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.54) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 6 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.52) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-06-05 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.INFO: Updating loan_arrears  
[2025-07-17 19:32:12] local.INFO: Amount: 2000.00  
[2025-07-17 19:32:12] local.INFO: Payment Plan Date: 2025/06/05  
[2025-07-17 19:32:12] local.INFO: Today: 2025-07-26  
[2025-07-17 19:32:12] local.INFO: Delay Days: 37  
[2025-07-17 19:32:12] local.INFO: Year: 2025  
[2025-07-17 19:32:12] local.INFO: Year Days: 365  
[2025-07-17 19:32:12] local.INFO: Penalty: 40  
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.25) SQL: select * from `loan_arrears` where `loan_schedule_id` = 6 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.46) SQL: select * from `loan_schedules` where `loan_schedules`.`id` = 10 limit 1 {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.34) SQL: select `holiday_date` from `holidays` where `holiday_date` between '2025-05-26 00:00:00' and '2025-07-26 00:00:00' and `holidays`.`del_flag` = '0' {"path":"batch.sql"} 
[2025-07-17 19:32:12] local.INFO: Updating loan_arrears  
[2025-07-17 19:32:12] local.INFO: Amount: 2000.00  
[2025-07-17 19:32:12] local.INFO: Payment Plan Date: 2025/05/26  
[2025-07-17 19:32:12] local.INFO: Today: 2025-07-26  
[2025-07-17 19:32:12] local.INFO: Delay Days: 45  
[2025-07-17 19:32:12] local.INFO: Year: 2025  
[2025-07-17 19:32:12] local.INFO: Year Days: 365  
[2025-07-17 19:32:12] local.INFO: Penalty: 49  
[2025-07-17 19:32:12] local.DEBUG: (Time: 00.28) SQL: select * from `loan_arrears` where `loan_schedule_id` = 10 and `loan_arrears`.`del_flag` = '0' limit 1 {"path":"batch.sql"} 
