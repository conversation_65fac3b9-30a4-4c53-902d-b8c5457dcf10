<?php

namespace App\Traits;

use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Services\ToastService;

trait HandlesApplicationCustomer
{
    public $customerId;
    public $applicationId;
    public $customer = null;
    public $application = null;

    public function initCustomerAndApplication($customer_id, $application_id)
    {
        $this->customerId = $customer_id;
        $this->applicationId = $application_id;

        $this->dispatch('init-select2');

        // Get customer
        $this->customer = app(CustomerRepository::class)->getInfoApplicationCustomer($customer_id);
        if (empty($this->customer)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }

        // Get application
        $this->application = $this->getApplication($application_id);
        if (empty($this->application)) {
            app(ToastService::class)->error(__('messages.no_data'));
            $this->redirect(route('admin.customer.index'));
        }
    }

    private function getApplication($application_id)
    {
        return app()->make(ApplicationRepository::class)->find($application_id);
    }
}
