<?php

namespace App\Models;

use App\Models\Base\CustomModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LoanOverpayment extends CustomModel
{
    use HasFactory;

    /**
     * テーブル名指定
     */
    protected $table = 'loan_overpayments';

    /**
     * タイムスタンプ自動管理無効
     */
    public $timestamps = false;

    /**
     * ホワイトリスト
     */
    protected $fillable = [
        'id',
        'customer_id',
        'application_id',
        'loan_payment_id',
        'loan_refund_id',
        'amount',
        'refunded_flag',
        'payment_date',
    ];

    /**
     * 関連定義
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function loanPayment()
    {
        return $this->belongsTo(LoanPayment::class);
    }
    public function loanRefund()
    {
        return $this->belongsTo(LoanRefund::class);
    }
}
