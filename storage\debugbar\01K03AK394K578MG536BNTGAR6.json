{"__meta": {"id": "01K03AK394K578MG536BNTGAR6", "datetime": "2025-07-14 11:00:31", "utime": **********.781021, "method": "GET", "uri": "/management", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.592222, "end": **********.781041, "duration": 0.18881893157958984, "duration_str": "189ms", "measures": [{"label": "Booting", "start": **********.592222, "relative_start": 0, "end": **********.755091, "relative_end": **********.755091, "duration": 0.*****************, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.755104, "relative_start": 0.*****************, "end": **********.781043, "relative_end": 2.1457672119140625e-06, "duration": 0.025938987731933594, "duration_str": "25.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.763036, "relative_start": 0.*****************, "end": **********.76528, "relative_end": **********.76528, "duration": 0.0022439956665039062, "duration_str": "2.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.778714, "relative_start": 0.****************, "end": **********.779048, "relative_end": **********.779048, "duration": 0.00033402442932128906, "duration_str": "334μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET management", "middleware": "admin, locale, auth:admin", "uses": "Closure() {#502\n  class: \"Livewire\\Volt\\VoltManager\"\n  this: Livewire\\Volt\\VoltManager {#493 …}\n  use: {\n    $componentName: \"App\\Livewire\\Admin\\Dashboard\\Index\"\n  }\n  file: \"C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\VoltManager.php\"\n  line: \"34 to 41\"\n}", "as": "admin.home", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "KsF2SvrpMvyKYq4jjEGhUMQRwD5kBH2GRCXsEOX2", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/management", "action_name": "admin.home", "controller_action": "Closure", "uri": "GET management", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>", "middleware": "admin, locale, auth:admin", "duration": "192ms", "peak_memory": "24MB", "response": "Redirect to http://127.0.0.1:8000/management/login", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-215663921 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-215663921\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1413139697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1413139697\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-692249278 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"844 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjA4U2lRM1UzNUFLZDdJS2w0RnA5OFE9PSIsInZhbHVlIjoiQWR3bDY3VUdUNVhpZmFycGtuTUEzUzBMVzhPNkJ3ZEJHelRHOUQ0eWVkR1dGbEZ4UmZRUkhNalpPb0UzY2NqWkx4ZTNlUmpGNHZRajU2Vm9DdEhTaDdqalJQRFBFTWMrOGdpYzZ0Mkd5c3VocllmYVBhMmJpcVhiMUZrUEJDd0pVYzlLV2o5ajZyOGlFVjc0Zy9ZWHBRPT0iLCJtYWMiOiIzM2Q4NGVmZmVkNmY3OGViYTVlMTdhNDlhNDNmODRmOGM5MmNhMDE5YzhmZWU1NzcwMTdhYmJjOTY4NDlmMTFlIiwidGFnIjoiIn0%3D; ladybird_session=Udb6ATAhTWSFlkEWdVLpLEtjKwtLxhT3N0MOIVhZ; XSRF-TOKEN=eyJpdiI6ImRUZ0xFYTc4bW1lVFdhNTd3bVc4TVE9PSIsInZhbHVlIjoia2tEOWY1NFFBWVRLbkF1dTZKSTdYNjZlTWhRWW52dXdrODZFbE9zRitEaEw2UVFobUFmbWdvZ2dJUVJXaW50SzRDeUZ2UXFWeGs5NXd6SllLc1JEVG1zejlZbU1hZTE3QjZzME1mVU80QWdKdDVXZU5TTDZac1grSzNIQW1SZ0MiLCJtYWMiOiI4YmY5OTFlOGI0OGY1Y2JjYzI4NjlkMGI3MTViNzA1MGNlNWY5NjM0N2IyMjE4N2IyZWIwZmY0MzM1ZDAzNWE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-692249278\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1070403977 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KsF2SvrpMvyKYq4jjEGhUMQRwD5kBH2GRCXsEOX2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070403977\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1903701670 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 02:00:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/management/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJqRzlRTjY3YldlSVBxTVhwNjA0aWc9PSIsInZhbHVlIjoiZE9BUTRpZGNZNVA3YVpRa1hrbTlGeGJvdnE2OERJVWgyM09GaXBUZUc1U2NDQkRNMHNPcEFWVWQzb3g5VFl3enV2MGFySnJvcFRKbWZwNjJPZ3RwdDZpNTFmWWtkRnF6TlhQUFhtVk9qa3FETnNNbUdIL001TlM2NlZoNUFZZ3MiLCJtYWMiOiI4MDlkNDQxYzEzNGRmZmFmZDI4NjY3MWFmMDk5Y2FmY2Y5MjAyNTQ0OGZlMWFjYmQwNTE0NDgxZjFjYTk3MmRmIiwidGFnIjoiIn0%3D; expires=Mon, 14 Jul 2025 04:00:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJqRzlRTjY3YldlSVBxTVhwNjA0aWc9PSIsInZhbHVlIjoiZE9BUTRpZGNZNVA3YVpRa1hrbTlGeGJvdnE2OERJVWgyM09GaXBUZUc1U2NDQkRNMHNPcEFWVWQzb3g5VFl3enV2MGFySnJvcFRKbWZwNjJPZ3RwdDZpNTFmWWtkRnF6TlhQUFhtVk9qa3FETnNNbUdIL001TlM2NlZoNUFZZ3MiLCJtYWMiOiI4MDlkNDQxYzEzNGRmZmFmZDI4NjY3MWFmMDk5Y2FmY2Y5MjAyNTQ0OGZlMWFjYmQwNTE0NDgxZjFjYTk3MmRmIiwidGFnIjoiIn0%3D; expires=Mon, 14-Jul-2025 04:00:31 GMT; path=/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903701670\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-292411105 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KsF2SvrpMvyKYq4jjEGhUMQRwD5kBH2GRCXsEOX2</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292411105\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/management", "action_name": "admin.home", "controller_action": "Closure"}, "badge": "302 Found"}}