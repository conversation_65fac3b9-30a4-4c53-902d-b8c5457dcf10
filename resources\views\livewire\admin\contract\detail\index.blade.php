@php
    $func = \App\Enums\FunctionEnum::CUSTOMER;
@endphp
<div id="contents" class="scroll-validate">
    <div class="contents-container">
        <div class="container-fluid">
            <div class="page-detail-header d-flex justify-content-between">
                <h1 class="page-detail-header-name">
                    <div class="name-group">
                        <div class="name-kana">{{ $contract->customer?->last_name_kana }}</div>
                        <div class="name-field">{{ $contract->customer?->last_name }}</div>
                    </div>
                    <div class="name-group">
                        <div class="name-kana">{{ $contract->customer?->first_name_kana }}</div>
                        <div class="name-field">{{ $contract->customer?->first_name }}</div>
                    </div>
                    <div class="mb-2">{{ trans2('screens.contract.detail.your_contract') }}</div>
                </h1>
                <div class="page-detail-header-link">
                    <a href="{{ getRoute('contract.index') }}" class="btn-text-prev small">{{ trans2('screens.contract.detail.back_to_list') }}</a>
                </div>
            </div>

            <ul class="page-detail-tabs nav nav-tabs" wire:ignore>
                <li class="nav-item">
                    <a href="{{ getRoute('contract.details', ['id' => $contract->id]) }}" class="nav-link {{ (request()->routeIs('admin.contract.details') || request()->routeIs('admin.contract.edit')) ? 'active' : '' }}">{{ trans2('screens.contract.detail.contract_tab') }}</a>
                </li>
                <li class="nav-item" wire:ignore>
                    <a href="{{ getRoute('contract.deposit.index', ['contract_id' => $contract->id]) }}" class="nav-link {{ (request()->routeIs('admin.contract.deposit.index')) ? 'active' : '' }}">
                        {{ trans2('screens.contract.detail.deposit_tab') }}
                    </a>
                </li>
            </ul>

            {{--  slot content page detail --}}
            @yield('detail-tab')

            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ getRoute('contract.index') }}" class="btn-text-prev small">{{ trans2('screens.contract.detail.back_to_list') }}</a>

                <div>
                    <livewire:admin.contract.delete :id="data_get($contract, 'id')" :isCanDelete="$isCanDelete"/>
                </div>
            </div>
        </div>
    </div>
</div>
