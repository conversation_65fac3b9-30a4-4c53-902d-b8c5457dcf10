<?php

namespace App\Models\Presenters;

use Carbon\Carbon;


trait PLoanWithdrawal
{
    public function getPaymentDateAttribute(){
        return $this->attributes['payment_date']
            ? Carbon::parse($this->attributes['payment_date'])->format('Y/m/d')
            : null;
    }

    public function getWithdrawalDateAttribute(){
        return $this->attributes['withdrawal_date']
            ? Carbon::parse($this->attributes['withdrawal_date'])->format('Y/m/d')
            : null;
    }
}
