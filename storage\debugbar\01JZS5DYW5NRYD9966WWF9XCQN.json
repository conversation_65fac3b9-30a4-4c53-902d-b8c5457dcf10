{"__meta": {"id": "01JZS5DYW5NRYD9966WWF9XCQN", "datetime": "2025-07-10 12:17:56", "utime": **********.230071, "method": "GET", "uri": "/management/customers/6/application/brand", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 8, "messages": [{"message": "[12:17:55] LOG.debug: (Time: 14.26) SQL: select * from `administrators` where `id` = 2 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.614642, "xdebug_link": null, "collector": "log"}, {"message": "[12:17:55] LOG.debug: (Time: 00.49) SQL: select `customers`.`id`, `customers`.`first_name`, `customers`.`last_name`, `customers`.`first_name_kana`, `customers`.`last_name_kana`, `customers`.`birthday`, `customers`.`tel1`, `customers`.`tel2`, `customers`.`tel3`, `customers`.`sex`, `customers`.`email`, `customers`.`zip1`, `customers`.`zip2`, `customers`.`pref_id`, `customers`.`address`, `customers`.`city`, `customers`.`building`, `customers`.`contract_count`, `customers`.`black_flag`, `customers`.`ins_date`, `customers`.`upd_date` from `customers` where `id` = '6' and `customers`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.63502, "xdebug_link": null, "collector": "log"}, {"message": "[12:17:55] LOG.debug: (Time: 00.59) SQL: select * from `applications` where `applications`.`customer_id` in (6) and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.640885, "xdebug_link": null, "collector": "log"}, {"message": "[12:17:55] LOG.debug: (Time: 00.49) SQL: select * from `applications` where `applications`.`id` is null and `applications`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.644697, "xdebug_link": null, "collector": "log"}, {"message": "[12:17:55] LOG.debug: (Time: 00.52) SQL: select `shops`.*, `administrator_shops`.`administrator_id` as `pivot_administrator_id`, `administrator_shops`.`shop_id` as `pivot_shop_id` from `shops` inner join `administrator_shops` on `shops`.`id` = `administrator_shops`.`shop_id` and `administrator_shops`.`del_flag` = 0 where `administrator_shops`.`administrator_id` = 2 and `shops`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.654125, "xdebug_link": null, "collector": "log"}, {"message": "[12:17:55] LOG.debug: (Time: 00.40) SQL: select `id`, `name`, `brand_id`, `shop_id`, `credit_flag` from `shop_brands` where 0 = 1 and `del_flag` = 0 and `shop_brands`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.656919, "xdebug_link": null, "collector": "log"}, {"message": "[12:17:55] LOG.debug: (Time: 00.47) SQL: select `courses`.`id`, `courses`.`name_management`, `courses`.`name_application`, `courses`.`type`, `courses`.`unit_price` from `courses` where `type` = '1' and `split_fee_flag` = 0 and `courses`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.661821, "xdebug_link": null, "collector": "log"}, {"message": "[12:17:55] LOG.debug: (Time: 00.52) SQL: select `courses`.`id`, `courses`.`name_management`, `courses`.`name_application`, `courses`.`type`, `courses`.`unit_price` from `courses` where `type` = '2' and `split_fee_flag` = 0 and `courses`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.666271, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.332339, "end": **********.230114, "duration": 0.8977749347686768, "duration_str": "898ms", "measures": [{"label": "Booting", "start": **********.332339, "relative_start": 0, "end": **********.564395, "relative_end": **********.564395, "duration": 0.****************, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.56441, "relative_start": 0.****************, "end": **********.230118, "relative_end": 4.0531158447265625e-06, "duration": 0.***************, "duration_str": "666ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.573014, "relative_start": 0.****************, "end": **********.575851, "relative_end": **********.575851, "duration": 0.002836942672729492, "duration_str": "2.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.227067, "relative_start": 0.****************, "end": **********.227219, "relative_end": **********.227219, "duration": 0.00015211105346679688, "duration_str": "152μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.227821, "relative_start": 0.***************, "end": **********.22786, "relative_end": **********.22786, "duration": 3.886222839355469e-05, "duration_str": "39μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "1x livewire.admin.application.brand", "param_count": null, "params": [], "start": **********.674602, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/application/brand.blade.phplivewire.admin.application.brand", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fapplication%2Fbrand.blade.php&line=1", "ajax": false, "filename": "brand.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.application.brand"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.209279, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.application-layout", "param_count": null, "params": [], "start": **********.210076, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/application-layout.blade.phpcomponents.layouts.application-layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapplication-layout.blade.php&line=1", "ajax": false, "filename": "application-layout.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.application-layout"}, {"name": "1x components.layouts.structures.head", "param_count": null, "params": [], "start": **********.210713, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/head.blade.phpcomponents.layouts.structures.head", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Fhead.blade.php&line=1", "ajax": false, "filename": "head.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.head"}, {"name": "1x volt-livewire::admin.application.tab", "param_count": null, "params": [], "start": **********.217262, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/admin/application/tab.blade.phpvolt-livewire::admin.application.tab", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fapplication%2Ftab.blade.php&line=1", "ajax": false, "filename": "tab.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::admin.application.tab"}, {"name": "1x volt-livewire::common.confirm", "param_count": null, "params": [], "start": **********.219768, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/confirm.blade.phpvolt-livewire::common.confirm", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.confirm"}, {"name": "1x volt-livewire::common.toast-message", "param_count": null, "params": [], "start": **********.222007, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views\\livewire/common/toast-message.blade.phpvolt-livewire::common.toast-message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Ftoast-message.blade.php&line=1", "ajax": false, "filename": "toast-message.blade.php", "line": "?"}, "render_count": 1, "name_original": "volt-livewire::common.toast-message"}, {"name": "1x livewire.common.event-handle", "param_count": null, "params": [], "start": **********.222967, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/common/event-handle.blade.phplivewire.common.event-handle", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fcommon%2Fevent-handle.blade.php&line=1", "ajax": false, "filename": "event-handle.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.common.event-handle"}, {"name": "1x components.layouts.structures.footer_js", "param_count": null, "params": [], "start": **********.223393, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_js.blade.phpcomponents.layouts.structures.footer_js", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_js.blade.php&line=1", "ajax": false, "filename": "footer_js.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_js"}, {"name": "1x components.layouts.structures.footer_autoload", "param_count": null, "params": [], "start": **********.224189, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/components/layouts/structures/footer_autoload.blade.phpcomponents.layouts.structures.footer_autoload", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fstructures%2Ffooter_autoload.blade.php&line=1", "ajax": false, "filename": "footer_autoload.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.structures.footer_autoload"}]}, "route": {"uri": "GET management/customers/{customer_id}/application/brand", "middleware": "admin, locale, auth:admin, check.access.application", "uses": "Closure() {#578\n  class: \"Livewire\\Volt\\VoltManager\"\n  this: Livewire\\Volt\\VoltManager {#493 …}\n  use: {\n    $componentName: \"App\\Livewire\\Admin\\Application\\Brand\"\n  }\n  file: \"C:\\xampp\\htdocs\\ladybird\\vendor\\livewire\\volt\\src\\VoltManager.php\"\n  line: \"34 to 41\"\n}", "as": "admin.customer.application.brand", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management/customers/{customer_id}/application", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>"}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.017740000000000002, "accumulated_duration_str": "17.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 2 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.600549, "duration": 0.01426, "duration_str": "14.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 80.383}, {"sql": "select `customers`.`id`, `customers`.`first_name`, `customers`.`last_name`, `customers`.`first_name_kana`, `customers`.`last_name_kana`, `customers`.`birthday`, `customers`.`tel1`, `customers`.`tel2`, `customers`.`tel3`, `customers`.`sex`, `customers`.`email`, `customers`.`zip1`, `customers`.`zip2`, `customers`.`pref_id`, `customers`.`address`, `customers`.`city`, `customers`.`building`, `customers`.`contract_count`, `customers`.`black_flag`, `customers`.`ins_date`, `customers`.`upd_date` from `customers` where `id` = '\\'6\\'' and `customers`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": ["'6'", "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/CustomerRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CustomerRepository.php", "line": 297}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 66}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.634634, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CustomerRepository.php:297", "source": {"index": 16, "namespace": null, "name": "app/Repositories/CustomerRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CustomerRepository.php", "line": 297}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FCustomerRepository.php&line=297", "ajax": false, "filename": "CustomerRepository.php", "line": "297"}, "connection": "local-ladybird", "explain": null, "start_percent": 80.383, "width_percent": 2.762}, {"sql": "select * from `applications` where `applications`.`customer_id` in (6) and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Repositories/CustomerRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CustomerRepository.php", "line": 297}, {"index": 22, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 66}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.640398, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "CustomerRepository.php:297", "source": {"index": 21, "namespace": null, "name": "app/Repositories/CustomerRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CustomerRepository.php", "line": 297}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FCustomerRepository.php&line=297", "ajax": false, "filename": "CustomerRepository.php", "line": "297"}, "connection": "local-ladybird", "explain": null, "start_percent": 83.145, "width_percent": 3.326}, {"sql": "select * from `applications` where `applications`.`id` is null and `applications`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": ["'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "core/Repositories/BaseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\core\\Repositories\\BaseRepository.php", "line": 37}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 67}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.64432, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:37", "source": {"index": 17, "namespace": null, "name": "core/Repositories/BaseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\core\\Repositories\\BaseRepository.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fcore%2FRepositories%2FBaseRepository.php&line=37", "ajax": false, "filename": "BaseRepository.php", "line": "37"}, "connection": "local-ladybird", "explain": null, "start_percent": 86.471, "width_percent": 2.762}, {"sql": "select `shops`.*, `administrator_shops`.`administrator_id` as `pivot_administrator_id`, `administrator_shops`.`shop_id` as `pivot_shop_id` from `shops` inner join `administrator_shops` on `shops`.`id` = `administrator_shops`.`shop_id` and `administrator_shops`.`del_flag` = 0 where `administrator_shops`.`administrator_id` = 2 and `shops`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [0, 2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ShopBrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopBrandRepository.php", "line": 68}, {"index": 21, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.653716, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ShopBrandRepository.php:68", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ShopBrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopBrandRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopBrandRepository.php&line=68", "ajax": false, "filename": "ShopBrandRepository.php", "line": "68"}, "connection": "local-ladybird", "explain": null, "start_percent": 89.233, "width_percent": 2.931}, {"sql": "select `id`, `name`, `brand_id`, `shop_id`, `credit_flag` from `shop_brands` where 0 = 1 and `del_flag` = 0 and `shop_brands`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [0, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ShopBrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopBrandRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.656623, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ShopBrandRepository.php:75", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ShopBrandRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ShopBrandRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FShopBrandRepository.php&line=75", "ajax": false, "filename": "ShopBrandRepository.php", "line": "75"}, "connection": "local-ladybird", "explain": null, "start_percent": 92.165, "width_percent": 2.255}, {"sql": "select `courses`.`id`, `courses`.`name_management`, `courses`.`name_application`, `courses`.`type`, `courses`.`unit_price` from `courses` where `type` = '\\'1\\'' and `split_fee_flag` = 0 and `courses`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'1'", 0, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 64}, {"index": 16, "namespace": null, "name": "app/Services/CourseService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\CourseService.php", "line": 108}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 118}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.661458, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CourseRepository.php:64", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FCourseRepository.php&line=64", "ajax": false, "filename": "CourseRepository.php", "line": "64"}, "connection": "local-ladybird", "explain": null, "start_percent": 94.419, "width_percent": 2.649}, {"sql": "select `courses`.`id`, `courses`.`name_management`, `courses`.`name_application`, `courses`.`type`, `courses`.`unit_price` from `courses` where `type` = '\\'2\\'' and `split_fee_flag` = 0 and `courses`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": ["'2'", 0, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 64}, {"index": 16, "namespace": null, "name": "app/Services/CourseService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\CourseService.php", "line": 108}, {"index": 17, "namespace": null, "name": "app/Livewire/Admin/Application/Brand.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Application\\Brand.php", "line": 119}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.6658561, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CourseRepository.php:64", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CourseRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\CourseRepository.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FCourseRepository.php&line=64", "ajax": false, "filename": "CourseRepository.php", "line": "64"}, "connection": "local-ladybird", "explain": null, "start_percent": 97.069, "width_percent": 2.931}]}, "models": {"data": {"App\\Models\\Course": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": {"admin.application.brand #qKnwVTklep6603bKvPLr": "array:4 [\n  \"data\" => array:14 [\n    \"saveForm\" => App\\Livewire\\Admin\\Application\\Forms\\BrandForm {#891\n      #component: App\\Livewire\\Admin\\Application\\Brand {#853\n        #__id: \"qKnwVTklep6603bKvPLr\"\n        #__name: \"admin.application.brand\"\n        #listeners: []\n        #attributes: Livewire\\Features\\SupportAttributes\\AttributeCollection {#989\n          #items: array:1 [\n            0 => Livewire\\Attributes\\Layout {#941\n              #component: App\\Livewire\\Admin\\Application\\Brand {#853}\n              #subTarget: null\n              #subName: null\n              #level: Livewire\\Features\\SupportAttributes\\AttributeLevel {#943\n                +name: \"ROOT\"\n              }\n              #levelName: null\n              +name: \"components.layouts.application-layout\"\n              +params: []\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n        #withValidatorCallback: null\n        #rulesFromOutside: []\n        #messagesFromOutside: []\n        #validationAttributesFromOutside: []\n        +guest: false\n        #viewData: []\n        +page: \"brand\"\n        +pageTitle: \"ブランド種類マスタ｜LadyBird\"\n        +redirecting: false\n        +saveForm: App\\Livewire\\Admin\\Application\\Forms\\BrandForm {#891}\n        +customerId: \"6\"\n        +customer: App\\Models\\Customer {#972\n          #connection: \"mysql\"\n          #table: \"customers\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:21 [\n            \"id\" => 6\n            \"first_name\" => \"vv\"\n            \"last_name\" => \"aa\"\n            \"first_name_kana\" => \"フリガナ\"\n            \"last_name_kana\" => \"フリガナ\"\n            \"birthday\" => \"2025-06-04\"\n            \"tel1\" => \"11\"\n            \"tel2\" => \"22\"\n            \"tel3\" => \"33\"\n            \"sex\" => \"1\"\n            \"email\" => null\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"町屋\"\n            \"city\" => \"荒川区\"\n            \"building\" => null\n            \"contract_count\" => null\n            \"black_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-23 13:53:23\"\n            \"upd_date\" => \"2025-06-23 13:53:23\"\n          ]\n          #original: array:21 [\n            \"id\" => 6\n            \"first_name\" => \"vv\"\n            \"last_name\" => \"aa\"\n            \"first_name_kana\" => \"フリガナ\"\n            \"last_name_kana\" => \"フリガナ\"\n            \"birthday\" => \"2025-06-04\"\n            \"tel1\" => \"11\"\n            \"tel2\" => \"22\"\n            \"tel3\" => \"33\"\n            \"sex\" => \"1\"\n            \"email\" => null\n            \"zip1\" => \"1160\"\n            \"zip2\" => \"001\"\n            \"pref_id\" => 13\n            \"address\" => \"町屋\"\n            \"city\" => \"荒川区\"\n            \"building\" => null\n            \"contract_count\" => null\n            \"black_flag\" => \"0\"\n            \"ins_date\" => \"2025-06-23 13:53:23\"\n            \"upd_date\" => \"2025-06-23 13:53:23\"\n          ]\n          #changes: []\n          #casts: array:7 [\n            \"sex\" => \"App\\Enums\\SexEnum\"\n            \"black_flag\" => \"App\\Enums\\BlackFlagEnum\"\n            \"relationship_flag\" => \"App\\Enums\\RelationshipFlagEnum\"\n            \"information_input_flag\" => \"App\\Enums\\InformationInputFlagEnum\"\n            \"bank_flag\" => \"App\\Enums\\BankFlagEnum\"\n            \"bank_account_type\" => \"App\\Enums\\BankAccountTypeEnum\"\n            \"contact_flag\" => \"App\\Enums\\ContactFlagEnum\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"applications\" => Illuminate\\Database\\Eloquent\\Collection {#971\n              #items: []\n              #escapeWhenCastingToString: false\n            }\n          ]\n          #touches: []\n          +timestamps: false\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:103 [\n            0 => \"id\"\n            1 => \"last_name\"\n            2 => \"first_name\"\n            3 => \"last_name_kana\"\n            4 => \"first_name_kana\"\n            5 => \"sex\"\n            6 => \"birthday\"\n            7 => \"email\"\n            8 => \"tel1\"\n            9 => \"tel2\"\n            10 => \"tel3\"\n            11 => \"zip1\"\n            12 => \"zip2\"\n            13 => \"pref_id\"\n            14 => \"city\"\n            15 => \"address\"\n            16 => \"building\"\n            17 => \"pref_kana_id\"\n            18 => \"city_kana\"\n            19 => \"address_kana\"\n            20 => \"building_kana\"\n            21 => \"contract_count\"\n            22 => \"black_flag\"\n            23 => \"emergency_last_name\"\n            24 => \"emergency_first_name\"\n            25 => \"emergency_last_name_kana\"\n            26 => \"emergency_first_name_kana\"\n            27 => \"emergency_tel1\"\n            28 => \"emergency_tel2\"\n            29 => \"emergency_tel3\"\n            30 => \"emergency_zip1\"\n            31 => \"emergency_zip2\"\n            32 => \"emergency_pref_id\"\n            33 => \"emergency_city\"\n            34 => \"emergency_address\"\n            35 => \"emergency_building\"\n            36 => \"relationship_flag\"\n            37 => \"relationship_other\"\n            38 => \"annual_income\"\n            39 => \"company_name\"\n            40 => \"company_name_kana\"\n            41 => \"company_tel1\"\n            42 => \"company_tel2\"\n            43 => \"company_tel3\"\n            44 => \"company_zip1\"\n            45 => \"company_zip2\"\n            46 => \"company_pref_id\"\n            47 => \"company_city\"\n            48 => \"company_address\"\n            49 => \"company_building\"\n            50 => \"information_input_flag\"\n            51 => \"gw_last_name\"\n            52 => \"gw_first_name\"\n            53 => \"gw_last_name_kana\"\n            54 => \"gw_first_name_kana\"\n            55 => \"gw_sex\"\n            56 => \"gw_birthday\"\n            57 => \"gw_tel1\"\n            58 => \"gw_tel2\"\n            59 => \"gw_tel3\"\n            60 => \"gw_zip1\"\n            61 => \"gw_zip2\"\n            62 => \"gw_pref_id\"\n            63 => \"gw_city\"\n            64 => \"gw_address\"\n            65 => \"gw_building\"\n            66 => \"gw_relationship_flag\"\n            67 => \"gw_relationship_other\"\n            68 => \"gw_company_name\"\n            69 => \"gw_company_name_kana\"\n            70 => \"gw_company_tel1\"\n            71 => \"gw_company_tel2\"\n            72 => \"gw_company_tel3\"\n            73 => \"gw_company_zip1\"\n            74 => \"gw_company_zip2\"\n            75 => \"gw_company_pref_id\"\n            76 => \"gw_company_city\"\n            77 => \"gw_company_address\"\n            78 => \"gw_company_building\"\n            79 => \"bank_account_name\"\n            80 => \"bank_account_name_kana\"\n            81 => \"bank_flag\"\n            82 => \"bank_account_mark1\"\n            83 => \"bank_account_mark2\"\n            84 => \"bank_account_mark3\"\n            85 => \"bank_account_number\"\n            86 => \"bank_code\"\n            87 => \"bank_name\"\n            88 => \"branch_code\"\n            89 => \"branch_name\"\n            90 => \"bank_account_type\"\n            91 => \"contact_flag\"\n            92 => \"contact_hope_date1\"\n            93 => \"contact_hope_start_time1\"\n            94 => \"contact_hope_end_time1\"\n            95 => \"contact_hope_date2\"\n            96 => \"contact_hope_start_time2\"\n            97 => \"contact_hope_end_time2\"\n            98 => \"del_flag\"\n            99 => \"ins_id\"\n            100 => \"ins_date\"\n            101 => \"upd_id\"\n            102 => \"upd_date\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #dates: []\n          #forceDeleting: false\n          #_cache: []\n          #oldManyToManyValues: []\n          #shouldTrackRelationship: true\n          #currentRelationTracking: null\n        }\n        +application: null\n        +brandShopList: Illuminate\\Database\\Eloquent\\Collection {#924\n          #items: []\n          #escapeWhenCastingToString: false\n        }\n        +productGeneral: array:1 [\n          0 => array:3 [\n            \"course_id\" => \"\"\n            \"quantity\" => 0\n            \"amount\" => \"\"\n          ]\n        ]\n        +productOptional: array:1 [\n          0 => array:3 [\n            \"course_id\" => \"\"\n            \"quantity\" => 0\n            \"amount\" => \"\"\n          ]\n        ]\n        +courseGeneralList: array:2 [\n          0 => array:5 [\n            \"id\" => 7\n            \"name_management\" => \"aaaa\"\n            \"name_application\" => \"bbbb\"\n            \"type\" => 1\n            \"unit_price\" => 1\n          ]\n          1 => array:5 [\n            \"id\" => 13\n            \"name_management\" => \"sadsadas\"\n            \"name_application\" => \"sadsad1\"\n            \"type\" => 1\n            \"unit_price\" => 1\n          ]\n        ]\n        +courseOptionalList: array:2 [\n          0 => array:5 [\n            \"id\" => 10\n            \"name_management\" => \"sdfs\"\n            \"name_application\" => \"dfsdfsdf\"\n            \"type\" => 2\n            \"unit_price\" => 0\n          ]\n          1 => array:5 [\n            \"id\" => 11\n            \"name_management\" => \"sadasd\"\n            \"name_application\" => \"dasdsa\"\n            \"type\" => 2\n            \"unit_price\" => 0\n          ]\n        ]\n        +totalAmount: 0\n      }\n      #propertyName: \"saveForm\"\n      #withValidatorCallback: null\n      #rulesFromOutside: []\n      #messagesFromOutside: []\n      #validationAttributesFromOutside: []\n      +shop_brand_id: \"\"\n      +staff_name: \"\"\n      +fee_type: 1\n      +product_general: array:1 [\n        0 => array:3 [\n          \"course_id\" => \"\"\n          \"quantity\" => 0\n          \"amount\" => \"\"\n        ]\n      ]\n      +product_optional: array:1 [\n        0 => array:3 [\n          \"course_id\" => \"\"\n          \"quantity\" => 0\n          \"amount\" => \"\"\n        ]\n      ]\n    }\n    \"customerId\" => \"6\"\n    \"customer\" => App\\Models\\Customer {#972}\n    \"application\" => null\n    \"brandShopList\" => Illuminate\\Database\\Eloquent\\Collection {#924}\n    \"productGeneral\" => array:1 [\n      0 => array:3 [\n        \"course_id\" => \"\"\n        \"quantity\" => 0\n        \"amount\" => \"\"\n      ]\n    ]\n    \"productOptional\" => array:1 [\n      0 => array:3 [\n        \"course_id\" => \"\"\n        \"quantity\" => 0\n        \"amount\" => \"\"\n      ]\n    ]\n    \"courseGeneralList\" => array:2 [\n      0 => array:5 [\n        \"id\" => 7\n        \"name_management\" => \"aaaa\"\n        \"name_application\" => \"bbbb\"\n        \"type\" => 1\n        \"unit_price\" => 1\n      ]\n      1 => array:5 [\n        \"id\" => 13\n        \"name_management\" => \"sadsadas\"\n        \"name_application\" => \"sadsad1\"\n        \"type\" => 1\n        \"unit_price\" => 1\n      ]\n    ]\n    \"courseOptionalList\" => array:2 [\n      0 => array:5 [\n        \"id\" => 10\n        \"name_management\" => \"sdfs\"\n        \"name_application\" => \"dfsdfsdf\"\n        \"type\" => 2\n        \"unit_price\" => 0\n      ]\n      1 => array:5 [\n        \"id\" => 11\n        \"name_management\" => \"sadasd\"\n        \"name_application\" => \"dasdsa\"\n        \"type\" => 2\n        \"unit_price\" => 0\n      ]\n    ]\n    \"totalAmount\" => 0\n    \"page\" => \"brand\"\n    \"pageTitle\" => \"ブランド種類マスタ｜LadyBird\"\n    \"redirecting\" => false\n    \"guest\" => false\n  ]\n  \"name\" => \"admin.application.brand\"\n  \"component\" => \"App\\Livewire\\Admin\\Application\\Brand\"\n  \"id\" => \"qKnwVTklep6603bKvPLr\"\n]", "admin.application.tab #QAzcs2nqB91jZLrNdx7H": "array:4 [\n  \"data\" => []\n  \"name\" => \"admin.application.tab\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\08aca3a97df218d337b7bf255fa0063b.php:8$15909\"\n  \"id\" => \"QAzcs2nqB91jZLrNdx7H\"\n]", "common.confirm #ui4BENA5Q1r3FzK9TV8j": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.confirm\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\21a91b4a087079d3e133812909232442.php:8$1590e\"\n  \"id\" => \"ui4BENA5Q1r3FzK9TV8j\"\n]", "common.toast-message #luKLvnrWxpuLtJ8Fd34g": "array:4 [\n  \"data\" => []\n  \"name\" => \"common.toast-message\"\n  \"component\" => \"Livewire\\Volt\\Component@anonymous\\x00C:\\xampp\\htdocs\\ladybird\\storage\\framework\\views\\0264863d287fd82f4cefe2d814020f86.php:8$1590f\"\n  \"id\" => \"luKLvnrWxpuLtJ8Fd34g\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/open?id=01JZS5DAVJ3VWWN636ZDMRMVH0&op=get\"\n]", "_reqCode": "/management/customers/6/application/brand_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management/customers/6/application/brand", "action_name": "admin.customer.application.brand", "controller_action": "Closure", "uri": "GET management/customers/{customer_id}/application/brand", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "management/customers/{customer_id}/application", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Fvolt%2Fsrc%2FVoltManager.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/volt/src/VoltManager.php:34-41</a>", "middleware": "admin, locale, auth:admin, check.access.application", "duration": "900ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-302758141 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-302758141\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1525167465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1525167465\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1730285255 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IjBoZWp3Y2MwaEV6dDlxN214cXZyVlE9PSIsInZhbHVlIjoic3l3bWNEN2xxWnNkalltSGtObXcvTU15bGdmT2RQNnVVWWFObStNVHgvYmlYenNZRGloMWpnN293Y3p5TDBmaSIsIm1hYyI6Ijk5ZGNhNGEzNWVmODQ3NDVhNDFiYmQ1ZmE3YWVhZTU0YTUyZTkwNmRiYTYwYjhlOWU2NDhiMzgxMGE5OWFlZTMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjI3QTl0aFZTdU5Cd3o5VGd5Z24zSEE9PSIsInZhbHVlIjoibTl1VCtXWDVhL0xMaGNMc1JzeGNmWkwxQVg1VmI5K1I4TUJ2ci8xUHBXUjhwMi9YTjZjQk5TcmNjRmpKemtuL0VGOVZyYkU2ZnF6blIzWHZuV1FpdktKQnNiS0JZeldUb3pGQ3pCUWNDaEtTL1JsWk53bytUY2ZSdHY1cE12ekRlS0ppRWw2SDZVc0lsQXVyaWpGMW5BPT0iLCJtYWMiOiIyY2Y5ZTEyZmE5NjQyYmJkODE5NzIyMjFhODM3NDc5NjE2YTVlZDYxMzU1MmNkZmQ5YzJjYTI3OTA3NWE0NTA3IiwidGFnIjoiIn0%3D; ladybird_session=VYL4AtscJdT5gtzU7vz9eiCbIGhcbGn54iXnU9z5; XSRF-TOKEN=eyJpdiI6IjFZREVuZ1RwQ0VmL010OVZhSFF6Qnc9PSIsInZhbHVlIjoiWTFxRGV5NEFQOWxFTmpDMXdWbXB0RXhSL3RKdVF1VVFFVmo5QW55Ri94K2dkYUZXSzJONktUajJHVEoraFRSbGRPa3hDbHRnUlF1M1dtWUY2eHhlTnpLcEt0N0lJQXZNeE9iRC9vV1dmTnlUcURMd01SM2NVclJvRUV5ZndvVE0iLCJtYWMiOiI0MTQ2OWVjODU1YzhkOThiNjllODhjYTQ5NDM1MzVlYzRhZmU1NzdmZGZlZTcxOTk2MGM3MGVlZmVjMjBlNTBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730285255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1858610632 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">2||$2y$10$s0LI87x0oyRCf8LmrLWHVe24uMnHpU1z102HiqCMoYnnol50W9DsC</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858610632\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-919280175 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 03:17:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkZSV1RMSEcxY0Q3RkhUVkZnU1MrbXc9PSIsInZhbHVlIjoiVFF2MW5PYlFBWCs1VDFqbW0weHNsMm4yY2ZtUDJwYStXcHBvM0V1NE9SSDNaOG0wSXYyZFR2SzRqV2RQbG5kaWp4QUNxUnN6RDRwUm9xTjAyT3p4SC80Sldhemx6aUZHTGI2UnBvOSs3SjdrM3R5ZTUybytuR1FhVDNOVElaRngiLCJtYWMiOiIyNjAyZDIxMTI0ODI5YjY1NTlkNDM4MzFkZjMxMTMyMzg4YTJhNmNhNzAyMDYwMmUwYjA3MjRmYTQ5ZTEwNjM2IiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 05:17:56 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkZSV1RMSEcxY0Q3RkhUVkZnU1MrbXc9PSIsInZhbHVlIjoiVFF2MW5PYlFBWCs1VDFqbW0weHNsMm4yY2ZtUDJwYStXcHBvM0V1NE9SSDNaOG0wSXYyZFR2SzRqV2RQbG5kaWp4QUNxUnN6RDRwUm9xTjAyT3p4SC80Sldhemx6aUZHTGI2UnBvOSs3SjdrM3R5ZTUybytuR1FhVDNOVElaRngiLCJtYWMiOiIyNjAyZDIxMTI0ODI5YjY1NTlkNDM4MzFkZjMxMTMyMzg4YTJhNmNhNzAyMDYwMmUwYjA3MjRmYTQ5ZTEwNjM2IiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 05:17:56 GMT; path=/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919280175\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-911011485 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pBKjV01r6NzPvW0JqZut1D8iA3y2e2WTYQQpWDj9</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://127.0.0.1:8000/_debugbar/open?id=01JZS5DAVJ3VWWN636ZDMRMVH0&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/management/customers/6/application/brand_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911011485\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/management/customers/6/application/brand", "action_name": "admin.customer.application.brand", "controller_action": "Closure"}, "badge": null}}