<?php

namespace App\Livewire\Admin\Payment;

use App\Enums\PaymentStatusEnum;
use App\Livewire\Base\BaseDataListComponent;
use Livewire\Attributes\On;
use Livewire\Component;
use App\Services\LoanScheduleService;
use App\Services\CsvService;

class PaymentTable extends BaseDataListComponent
{
    public $monthIndex;
    public $brand_id;
    public $shop_id;
    public $item_type_id;
    public $course_id;
    public $payment_plan_date_from;
    public $payment_plan_date_to;
    public $payment_status;

    #[On('update-advanced-search')]
    public function getAdvancedSearchAttribute($data)
    {
        $this->brand_id = $data['brand_id'] ?? '';
        $this->shop_id = $data['shop_id'] ?? '';
        $this->item_type_id = $data['item_type_id'] ?? '';
        $this->course_id = $data['course_id'] ?? '';
        $this->payment_plan_date_from = $data['payment_plan_date_from'] ?? '';
        $this->payment_plan_date_to = $data['payment_plan_date_to'] ?? '';
        $this->payment_status = $data['payment_status'] ?? '';
    }

    #[On('update-search')]
    public function getSearchAttribute($data)
    {
        $this->monthIndex = $data['monthIndex'] ?? '';
    }

    public function searchDataList($paymentStatus = [], $brandId = null)
    {
        $dataSearch = [
            'month_index' => $this->monthIndex,
            'brand_id' => $this->brand_id,
            'shop_id' => $this->shop_id,
            'item_type_id' => $this->item_type_id,
            'course_id' => $this->course_id,
            'payment_plan_date_from' => $this->payment_plan_date_from,
            'payment_plan_date_to' => $this->payment_plan_date_to,
            'payment_status' => $paymentStatus,
            'focus_brand_id' => $brandId,
        ];

        $this->dispatch('search-data-list', $dataSearch);
        $this->dispatch('scroll-to', id: 'section');
    }

    #[On('export_unpaid_balance_csv')]
    public function downloadUnpaidCSV()
    {
        $dataSearch = [
            'month_index' => $this->monthIndex,
            'brand_id' => $this->brand_id,
            'shop_id' => $this->shop_id,
            'item_type_id' => $this->item_type_id,
            'course_id' => $this->course_id,
            'payment_plan_date_from' => $this->payment_plan_date_from,
            'payment_plan_date_to' => $this->payment_plan_date_to,
        ];

        $dataList = app(LoanScheduleService::class)->getPaymentForCsv($dataSearch, PaymentStatusEnum::NOT_PAID_OVERDUE);

        $headers = getConfig('csv.payments.unpaid_balance.header');
        $filename = getConfig('csv.payments.unpaid_balance.filename') . '.csv';

        return app(CsvService::class)->exportCsv($filename, $headers, $dataList);
    }

    #[On('export_deposit_data_csv')]
    public function downloadDepositDataCSV()
    {
        $dataSearch = [
            'month_index' => $this->monthIndex,
            'brand_id' => $this->brand_id,
            'shop_id' => $this->shop_id,
            'item_type_id' => $this->item_type_id,
            'course_id' => $this->course_id,
            'payment_plan_date_from' => $this->payment_plan_date_from,
            'payment_plan_date_to' => $this->payment_plan_date_to,
        ];

        $dataList = app(LoanScheduleService::class)->getPaymentForCsv($dataSearch, PaymentStatusEnum::WAITING_FOR_PAYMENT);

        $headers = getConfig('csv.payments.deposit_data.header');
        $filename = getConfig('csv.payments.deposit_data.filename') . '.csv';

        return app(CsvService::class)->exportShiftJISCsv($filename, $headers, $dataList);
    }

    public function render()
    {
        $dataSearch = [
            'month_index' => $this->monthIndex,
            'brand_id' => $this->brand_id,
            'shop_id' => $this->shop_id,
            'item_type_id' => $this->item_type_id,
            'course_id' => $this->course_id,
            'payment_plan_date_from' => $this->payment_plan_date_from,
            'payment_plan_date_to' => $this->payment_plan_date_to,
            'payment_status' => $this->payment_status,
        ];

        $dataList = app(LoanScheduleService::class)->getListForPaymentTable($dataSearch);
        $totals = $dataList->reduce(function ($carry, $item) {
            foreach ((array) $item as $key => $value) {
                if (is_numeric($value)) {
                    $carry[$key] = ($carry[$key] ?? 0) + $value;
                }
            }
            return $carry;
        }, []);
        return $this->viewLivewireAdmin('payment.payment-table', [
            'dataList' => $dataList,
            'totals' => $totals,
        ]);
    }
}
