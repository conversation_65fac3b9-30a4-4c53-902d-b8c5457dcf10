<div id="contents">
    <div class="contents-container">
        <div class="container-fluid">
            <h1 class="page-title">{{ trans2('screens.customer.index.title') }}</h1>
            <div id="selected-date"
                data-selected-dates='@json($selectedHoliday)'>
            </div>
            <livewire:admin.customer.search />
            <livewire:admin.customer.advanced-search />
            <livewire:admin.customer.table-data-list />
        </div>
    </div>
</div>
@script
    <script>
        document.addEventListener('livewire:initialized', () => {
            initializeDatepickers();
            initializeMonthpickers();
            initializeBirthDayDatepickers();
            initSelect2Binding();
        });

        function initializeBirthDayDatepickers() {
            document.querySelectorAll('.birthday').forEach(function(el) {
                const currentYear = new Date().getFullYear();
                const minYear = currentYear - 200

                const childDataEl = document.getElementById('selected-date');
                const selectedDatesRaw = childDataEl ? JSON.parse(childDataEl.dataset.selectedDates) : [];
                const selectedDates = selectedDatesRaw.map(item => item.holiday_date);

                $(el).datepicker({
                    dateFormat: 'yy/mm/dd',
                    yearRange: `${minYear}:${currentYear}`,
                    minDate: new Date(minYear, 0, 1),
                    maxDate: new Date(),
                    changeYear: true,
                    showButtonPanel: true,
                    beforeShowDay: function(date) {
                        const d = $.datepicker.formatDate('yy/mm/dd', date);
                        const day = date.getDay();

                        if (selectedDates.includes(d)) {
                            return [true, 'selected-date', ''];
                        }

                        if(day === 5) {
                            return [true, ' ui-datepicker-week-end day-saturday', ''];
                        }

                        if(day === 6) {
                            return [true, ' ui-datepicker-week-end day-sunday', ''];
                        }

                        return [true, '', ''];

                    },
                    beforeShow: function(input, inst) {
                        addCustomNavButtons(inst);
                        setTimeout(function() {
                            customizeButtonPanel(input);
                        }, 1);
                    },
                    onChangeMonthYear: function(year, month, inst) {
                        addCustomNavButtons(inst);
                        setTimeout(function() {
                            customizeButtonPanel(inst.input[0]);
                        }, 1);
                    },
                }).on('change', function() {
                    const value = $(this).val();
                    const model = el.getAttribute('wire:model');
                    if (model) {
                        const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');

                        Livewire.find(componentId).set(model, value);
                        Livewire.find(componentId).call('search');
                    }
                });
            });
        }

        function initializeDatepickers() {
            document.querySelectorAll('.date-default').forEach(function(el) {
                const currentYear = new Date().getFullYear();
                const minYear = currentYear - 200

                const childDataEl = document.getElementById('selected-date');
                const selectedDatesRaw = childDataEl ? JSON.parse(childDataEl.dataset.selectedDates) : [];
                const selectedDates = selectedDatesRaw.map(item => item.holiday_date);

                $(el).datepicker({
                    dateFormat: 'yy/mm/dd',
                    minDate: new Date(minYear, 0, 1),
                    changeYear: true,
                    beforeShowDay: function(date) {
                        const d = $.datepicker.formatDate('yy/mm/dd', date);
                        const day = date.getDay();

                        if (selectedDates.includes(d)) {
                            return [true, 'selected-date', ''];
                        }

                        if(day === 5) {
                            return [true, ' ui-datepicker-week-end day-saturday', ''];
                        }

                        if(day === 6) {
                            return [true, ' ui-datepicker-week-end day-sunday', ''];
                        }

                        return [true, '', ''];

                    },
                    beforeShow: function(input, inst) {
                        addCustomNavButtons(inst);
                    },
                    onChangeMonthYear: function(year, month, inst) {
                        addCustomNavButtons(inst);
                    },
                }).on('change', function() {
                    const value = $(this).val();
                    const model = el.getAttribute('wire:model');
                    if (model) {
                        const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                        Livewire.find(componentId).set(model, value);
                    }
                });
            });
        }

        function initializeMonthpickers() {
            document.querySelectorAll('.monthpicker').forEach(function(el) {
                const currentYear = new Date().getFullYear();
                const minYear = currentYear - 200

                $(el).datepicker({
                        dateFormat: 'yy/mm',
                        yearRange: `${minYear}:${currentYear}`,
                        minDate: new Date(minYear, 0, 1),
                        maxDate: new Date(),
                        changeYear: true,
                        onClose: function(dateText, inst) {
                            $(this).datepicker('setDate', new Date(inst.selectedYear, inst.selectedMonth,
                                1));
                            const value = $(this).val();
                            const model = el.getAttribute('wire:model');
                            if (model) {
                                const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                                if (componentId) {
                                    Livewire.find(componentId).set(model, value, true);
                                }
                            }
                        }
                    })
                    .focus(function() {
                        $('.ui-datepicker-calendar').hide();
                    })
                    .on('change', function() {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');
                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                        }
                    });
            });
        }

        function initSelect2Binding() {

            document.querySelectorAll('select.form-select2').forEach(function (el) {

                if (!$(el).hasClass('select2-hidden-accessible')) {
                    $(el).select2().on('change', function (e) {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');

                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                        }
                    });
                }
            });
        }

        function addCustomNavButtons(inst) {
            setTimeout(function() {
            var buttonPanel = $(inst.dpDiv).find(".ui-datepicker-header");
            if (buttonPanel.find('.custom-nav-button').length === 0) {
                var prevYearBtn = $('<a class="custom-nav-button prev-year" title="Prev Year"><span class="ui-icon">前の年へ</span></a>');
                var nextYearBtn = $('<a class="custom-nav-button next-year" title="Next Year"><span class="ui-icon">次の年へ</span></a>');

                prevYearBtn.click(function(e) {
                $.datepicker._adjustDate(inst.input, -1, 'Y'); // 1年戻る
                e.preventDefault();
                });

                nextYearBtn.click(function(e) {
                $.datepicker._adjustDate(inst.input, +1, 'Y'); // 1年進む
                e.preventDefault();
                });

                prevYearBtn.insertBefore(buttonPanel.find('.ui-datepicker-prev'));
                nextYearBtn.insertAfter(buttonPanel.find('.ui-datepicker-next'));
            }
            }, 1);
        }

        function customizeButtonPanel(input) {
            var buttonPane = $(input).datepicker("widget").find(".ui-datepicker-buttonpane");

            buttonPane.find(".ui-datepicker-close").remove();
            buttonPane.find(".ui-datepicker-current").remove();

            if (buttonPane.find('.ui-datepicker-clear').length === 0) {
                $('<button>', {
                    text: "{{ trans2('button.clear') }}",
                    class: "ui-datepicker-clear ui-state-default ui-priority-secondary ui-corner-all",
                    click: function () {
                        $.datepicker._clearDate(input);
                    }
                }).appendTo(buttonPane);
            }
        }
    </script>
@endscript
