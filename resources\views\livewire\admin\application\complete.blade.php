<div class="">
  <div class="container-min">
    <div class="card application-complete-box mb-3">
      <div class="card-body">
        <h1>{{ trans2('screens.application.complete.title') }}</h1>
        <p>{{ trans2('screens.application.complete.thanks_message') }}</p>
        <p>{{ trans2('screens.application.complete.print_message') }}<br>{{ trans2('screens.application.complete.tablet_message') }}</p>
      </div>
    </div>
    <div class="mb-5">
      <a class="btn application-complete-collapse" data-bs-toggle="collapse" href="#applicationCompleteCollapse" role="button" aria-expanded="false" aria-controls="applicationCompleteCollapse">{{ trans2('screens.application.complete.show_more') }}</a>
    </div>
    <div class="collapse" id="applicationCompleteCollapse">
      <ol class="application-complete-step">
        <li>
          <div class="step">
            <h4>STEP<span class="num">1</h4>
          </div>
          <div class="content">
            <div class="d-flex align-items-center justify-content-between">
              <div class="txt">
                <p>{{ trans2('screens.application.complete.step1_message') }}</p>
              </div>
              <button id="btn-print" class="btn btn-dark w-px-210" target="_blank"
                 data-export-url="{{ getRoute('customer.application.export-pdf', [$applicationId]) }}">
                  <span class="btn-text__print">{{ trans2('screens.application.complete.step1_button') }}</span>
                  <span class="spinner-border spinner-border-sm align-middle ms-1 d-none" id="btn-print-loading" role="status" aria-hidden="true"></span>
              </button>
            </div>
          </div>
        </li>
        <li>
          <div class="step">
            <h4>STEP<span class="num">2</h4>
          </div>
          <div class="content">
            <h5>1. {{ trans2('screens.application.complete.step2_save_and_draf') }}</h5>
            <p>{{ trans2('screens.application.complete.step2_pls_close_page') }}<br>
            {{ trans2('screens.application.complete.step2_continue_review') }}</p>
            <div class="d-flex align-items-center justify-content-between">
              <div class="txt">
                <h5>2. {{ trans2('screens.application.complete.step2_when_sharing') }}</h5>
                <p>{{ trans2('screens.application.complete.step2_when_sharing_message') }}</p>
              </div>
              <button
                class="btn btn-dark w-px-210"
                wire:click="completeRequest"
              >
                @if(!$isLoading)
                    {{ trans2('screens.application.complete.step2_button') }}
                @else
                    <span class="spinner-border spinner-border-sm"></span> Loading...
                @endif
              </button>
            </div>
          </div>
        </li>
      </ol>
    </div>
  </div>

  <div class="modal fade" id="completeRequestModal" tabindex="-1" aria-labelledby="completeRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered">
      <div class="modal-content">
        <button type="button" class="btn-close btn-close-circle position-absolute top-0 start-100 translate-middle" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="modal-body">
          <div class="text-center p-3">
            <h5 class="fs-20 mb-5 mt-4">{{ trans2('screens.application.complete.modal_title') }}</h5>
            <p class="fs-16">{{ trans2('screens.application.complete.modal_message') }}<br>
            {{ trans2('screens.application.complete.modal_message_2') }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@push('scripts')
  <script>
    Livewire.on('showCompleteRequestModal', function () {
        $('#completeRequestModal').modal('show');
    });
  </script>
  <script>
      $(function() {
          $('#btn-print').on('click', function(e) {
              e.preventDefault();
              var $btn = $(this);
              var $text = $btn.find('.btn-text__print');
              var $spinner = $('#btn-print-loading');
              var url = $btn.data('export-url');
              $btn.prop('disabled', true);
              $text.addClass('d-none');
              $spinner.removeClass('d-none');
              $.ajax({
                  url: url,
                  method: 'GET',
                  dataType: 'json',
                  success: function(data) {
                      if (data.url) {
                          window.open(data.url, '_blank');
                      }
                  },
                  error: function() {
                      // log error
                      if (window.notyf) {
                          window.notyf.error("{{ __('messages.export_failed') }}");
                      }
                  },
                  complete: function() {
                      $btn.prop('disabled', false);
                      $text.removeClass('d-none');
                      $spinner.addClass('d-none');
                  }
              });
          });
      });
  </script>
@endpush
