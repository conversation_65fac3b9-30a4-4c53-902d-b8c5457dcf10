<?php

use App\Services\ShopService;
use App\Services\ShopBrandService;
use App\Enums\AuthTypeEnum;
use App\Repositories\BrandRepository;
use App\Repositories\ShopRepository;

?>

<!-- search -->
<div class="contents-container">
    <div class="container-fluid">
        <div class="d-flex align-items-center justify-content-between mb-5 setting-form-control">
            <div class="d-flex">
                <!--[if BLOCK]><![endif]--><?php if(!getCurrentUser()->isStore()): ?>
                    <div class="me-5" wire:ignore>
                        <select name="auth_type" class="form-select2" style="width: 250px" wire:model="authType">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $text): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>"><?php echo e($text); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><?php if(!getCurrentUser()->isStore()): ?>
                    <div style="display: <?php echo e($authType == AuthTypeEnum::STORE ? '' : 'none'); ?> ">
                        <div class="me-5" wire:ignore>
                            <select name="store_id" class="form-select2" style="width: 250px" wire:model="storeId">
                                <option value="">店舗を選択</option>

                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($s->id); ?>"><?php echo e($s->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><?php if(!getCurrentUser()->isStore()): ?>
                    <div style="display: <?php echo e($authType == AuthTypeEnum::BRAND ? '' : 'none'); ?> ">
                        <div class="me-5" wire:ignore>
                            <select name="brand_id" class="form-select2" style="width: 250px" wire:model="brandId" >
                                <option value="">ブランドを選択</option>

                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $b): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($b->id); ?>"><?php echo e($b->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <div class="d-flex align-items-center" wire:ignore>
                    <div class="me-3 text-nowrap"><?php echo e(trans2('screens.dashboard.display_period')); ?></div>
                    <input type="text" name="" value="" class="monthpicker monthpicker-secondary" wire:model="from" readonly >
                    <div class="mx-3">〜</div>
                    <input type="text" name="" value="" class="monthpicker monthpicker-secondary" wire:model="to" readonly>
                </div>
            </div>
            <div class="ms-auto">
                <button type="button" class="btn-csv-download" wire:click.prevent="$dispatch('export_csv')"><?php echo e(trans2('button.CSV_download')); ?></button>
            </div>
        </div>

        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('admin.dashboard.data-list', ['lazy' => true,'authType' => $authType,'from' => $from,'to' => $to,'brandId' => $brandId,'storeId' => $storeId]);

$__html = app('livewire')->mount($__name, $__params, 'lw-1107939981-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
    </div>
</div>
    <?php
        $__scriptKey = '1107939981-0';
        ob_start();
    ?>
    <script>
        document.addEventListener('livewire:initialized', () => {
            initializeMonthpickers();
            initSelect2Binding();
        });

        function initializeMonthpickers() {
            document.querySelectorAll('.monthpicker').forEach(function(el) {
                const currentYear = new Date().getFullYear();
                const currentMonth = (new Date().getMonth() + 1).toString().padStart(2, '0');
                const minYear = currentYear - 200;

                // Set default value if empty
                if (!el.value) {
                    el.value = `${currentYear}年${currentMonth}月`;

                    const model = el.getAttribute('wire:model');
                    if (model) {
                        const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                        
                        setTimeout(() => {
                            Livewire.find(componentId).set(model, el.value);
                        }, 50);
                    }
                }

                $(el).datepicker({
                        dateFormat: 'yy/mm',
                        yearRange: `${minYear}:${currentYear}`,
                        changeYear: true,
                    })
                    .focus(function() {
                        $('.ui-datepicker-calendar').hide();
                        $('#ui-datepicker-div').hide();
                    })
                    .on('change', function() {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');
                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                            Livewire.find(componentId).call('search');
                        }
                    });
            });
        }

        function initSelect2Binding() {

            document.querySelectorAll('select.form-select2').forEach(function(el) {

                if (!$(el).hasClass('select2-hidden-accessible')) {
                    $(el).select2().on('change', function(e) {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');

                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);
                            console.log(1);

                            Livewire.find(componentId).call('search');

                        }
                    });
                }
            });
        }
    </script>
    <?php
        $__output = ob_get_clean();

        \Livewire\store($this)->push('scripts', $__output, $__scriptKey)
    ?><?php /**PATH C:\xampp\htdocs\ladybird\resources\views\livewire/admin/dashboard/data-list-search.blade.php ENDPATH**/ ?>