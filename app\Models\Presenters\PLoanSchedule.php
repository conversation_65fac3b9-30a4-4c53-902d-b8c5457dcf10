<?php

namespace App\Models\Presenters;

use App\Enums\BankFlagEnum;
use App\Enums\PaymentStatusEnum;
use Carbon\Carbon;

trait PLoanSchedule
{
    public function convertToCsvRow()
    {
        if (data_get($this->payment_status, 'value') == PaymentStatusEnum::WAITING_FOR_PAYMENT) {
            return [
                '999999',
                $this->payment_plan_date_only_year_month ?? '',
                $this->bank_code_csv ?? '',
                $this->branch_code_csv ?? '',
                $this->bank_account_type ?? '',
                $this->bank_account_number_csv ?? '',
                mb_convert_kana($this->bank_account_name_kana, 'k') ?? '',
                (int)$this->total_amount ?? '',
                data_get($this->type, 'value') ?? '',
                $this->regist_number ?? '',
            ];
        }

        if (data_get($this->payment_status, 'value') == PaymentStatusEnum::NOT_PAID_OVERDUE) {
            return [
                $this->id ?? '',
                $this->customer_id ?? '',
                $this->application_id ?? '',
                $this->payment_plan_date ?? '',
                data_get($this->payment_type, 'text') ?? '',
                $this->total_amount ?? '',
                $this->balance_difference ?? '',
                $this->regist_number ?? '',
                data_get($this->payment_status, 'text') ?? '',
            ];
        }

        return;
    }

    public function getBalanceDifferenceAttribute()
    {
        return ($this->attributes['amount_paid'] ?? 0) - ($this->attributes['total_amount'] ?? 0);
    }

    public function getPaymentPlanDateOnlyYearMonthAttribute()
    {
        return $this->attributes['payment_plan_date']
            ? Carbon::parse($this->attributes['payment_plan_date'])->format('Ym')
            : null;
    }

    public function getPaymentPlanDateAttribute()
    {
        return $this->attributes['payment_plan_date']
            ? Carbon::parse($this->attributes['payment_plan_date'])->format('Y/m/d')
            : null;
    }

    public function getBankCodeCsvAttribute()
    {
        return ($this->bank_flag == BankFlagEnum::JAPAN)
            ? '9900'
            : $this->bank_code;
    }

    public function getBranchCodeCsvAttribute()
    {
        return ($this->bank_flag == BankFlagEnum::JAPAN)
            ? getMiddleThreeNumber($this->bank_account_mark1)
            : $this->branch_code;
    }

    public function getBankAccountNumberCsvAttribute()
    {
        return ($this->bank_flag == BankFlagEnum::JAPAN)
            ? removeTrailingOneNumber($this->bank_account_number)
            : $this->bank_account_number;
    }
}
