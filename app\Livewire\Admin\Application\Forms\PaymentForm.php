<?php

namespace App\Livewire\Admin\Application\Forms;

use App\Enums\BonusFlagEnum;
use App\Enums\DocSendFlagEnum;
use App\Livewire\Base\BaseAdminForm;
use Illuminate\Validation\Rule;

class PaymentForm extends BaseAdminForm
{
    public $deposit;

    public $payment_count;

    public $payment_start_month;

    public $bonus_flag = BonusFlagEnum::ENABLED;

    public $bonus_payment_amount;

    public $bonus_payment_month1;

    public $bonus_payment_month2;

    public $bonus_payment_count;

    public $doc_send_flag = DocSendFlagEnum::HOME;


    public function rules()
    {
        $isRequiredBonusFlag = $this->bonus_flag ? 'required|' : 'nullable|';

        return [
            'deposit' => 'required|' . $this->decimalRule(),
            'payment_count' => 'required|integer|min:3|max:' . config('constant.MAX_PAYMENT_COUNT_APPLICATION'),
            'payment_start_month' => 'required|date_format:Y年n月',
            'bonus_flag' => ['required', Rule::in(BonusFlagEnum::getValues())],
            'bonus_payment_amount' => $isRequiredBonusFlag . $this->decimalRule(),
            'bonus_payment_month1' => $isRequiredBonusFlag . 'integer|digits_between:1,12',
            'bonus_payment_month2' => 'nullable|integer|digits_between:1,12',
            'bonus_payment_count' => $isRequiredBonusFlag . $this->smallIntegerRule(),
            'doc_send_flag' => ['required', Rule::in(DocSendFlagEnum::getValues())],
        ];
    }

    public function getValidationAttributes()
    {
        return __('models.payment.attributes');
    }

    public function messages()
    {
        return [
            'payment_count.max' => '支払回数には、36より小さな数字を入力してください。',
        ];
    }
}
