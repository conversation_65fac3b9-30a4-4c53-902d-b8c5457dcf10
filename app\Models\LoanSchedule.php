<?php

namespace App\Models;

use App\Enums\LoanScheduleTypeEnum;
use App\Enums\PaymentCompanyFlagEnum;
use App\Enums\PaymentStatusEnum;
use App\Enums\PaymentTypeEnum;
use App\Enums\ResultStatusEnum;
use App\Models\Base\CustomModel;
use App\Models\Concerns\HasNameLang;
use App\Models\Presenters\PBalance;
use App\Models\Presenters\PLoanSchedule;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoanSchedule extends CustomModel
{
    use PLoanSchedule;
    use PBalance;
    use HasNameLang;

    /**
     * The table associated with the model.
     *
     * @var string
     */

    protected $table = 'loan_schedules';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'customer_id',
        'application_id',
        'payment_plan_date',
        'amount',
        'bonus_payment_amount',
        'total_amount',
        'amount_paid',
        'type',
        'payment_type',
        'payment_company_flag',
        'result_status',
        'payment_status',
    ];
    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class, 'application_id', 'id');
    }
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }
    public function loanRefunds()
    {
        return $this->hasMany(LoanRefund::class, 'loan_schedule_id', 'id');
    }
    public function loanArrears()
    {
        return $this->hasMany(LoanArrear::class, 'loan_schedule_id', 'id');
    }
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = trim($value);
    }

    protected $casts = [
        'type' => LoanScheduleTypeEnum::class,
        'payment_type' => PaymentTypeEnum::class,
        'payment_company_flag' => PaymentCompanyFlagEnum::class,
        'result_status' => ResultStatusEnum::class,
        'payment_status' => PaymentStatusEnum::class,
    ];

    public static function getFieldRelationshipMap(): array
    {
        return [
            'customer_id' => 'customer',
            'application' => 'application',
        ];
    }
}
