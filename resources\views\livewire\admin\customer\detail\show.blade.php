@extends('livewire.admin.customer.detail.index')
@section('detail-tab')
    @php
        $func = \App\Enums\FunctionEnum::CUSTOMER;
    @endphp
    <div class="card mb-3">
        <div class="card-body">
            <div class="container-min">
                @can('allowed-update', [\App\Enums\SidebarMenuEnum::CUSTOMER, $brandIds, $shopIds])
                    <div class="mb-4 d-flex justify-content-end">
                        <a href="{{ getRoute('customer.edit', ['id' => $customer->id]) }}" class="btn btn-dark btn-medium">{{ trans2('button.edit') }}</a>
                    </div>
                @endcan
                <table class="table table-details">
                    <tbody>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.ID') }}</th>
                        <td class="text-nowrap">{{ data_get($customer, 'id') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.name') }}</th>
                        <td class="text-break">{{ data_get($customer, 'last_name') }} {{ data_get($customer, 'first_name') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.name_hiragana') }}</th>
                        <td class="text-break">{{ data_get($customer, 'last_name_kana') }}　{{ data_get($customer, 'first_name_kana') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.sex') }}</th>
                        <td class="text-break">{{ data_get($customer, 'sex.text') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.birthday') }}</th>
                        <td class="text-break">
                            @if($customer?->birthday)
                                {{ \Carbon\Carbon::parse($customer?->birthday)->year }}年{{ \Carbon\Carbon::parse($customer?->birthday)->month }}月{{ \Carbon\Carbon::parse($customer?->birthday)->day }}日
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.email') }}</th>
                        <td class="text-break">{{ data_get($customer, 'email') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.tel') }}</th>
                        <td class="text-break">{{ data_get($customer, 'tel1') }}-{{ data_get($customer, 'tel2') }}-{{ data_get($customer, 'tel3') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.postcode') }}</th>
                        <td class="text-break">{{ data_get($customer, 'zip1') }}-{{ data_get($customer, 'zip2') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.prefecture') }}</th>
                        <td class="text-break">{{ data_get($customer?->pref, 'name') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.city') }}</th>
                        <td class="text-break">{{ data_get($customer, 'city') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.address') }}</th>
                        <td class="text-break">{{ data_get($customer, 'address') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.building') }}</th>
                        <td class="text-break">{{ data_get($customer, 'building') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.contract_count') }}</th>
                        <td class="text-break">{{ data_get($customer, 'contract_count') }}</td>
                    </tr>
                    <tr>
                        <th class="text-nowrap">{{ transm('customer.attributes.black') }}</th>
                        <td class="text-break">{{ data_get($customer, 'black_flag.text') }}</td>
                    </tr>
                    </tbody>
                </table>
                <div class="text-end">
                    <span class="small me-4">{{ transm('_default.attributes.ins_date') }}：{{ $customer?->ins_date ? \Carbon\Carbon::parse($customer->ins_date)->format('Y/m/d') : '' }}</span>
                    <span class="small me-4">{{ transm('_default.attributes.upd_date') }}：{{ $customer?->upd_date ? \Carbon\Carbon::parse($customer->upd_date)->format('Y/m/d') : '' }}</span>
                    <button type="button" class="small btn-text fc-primary" data-bs-toggle="modal" data-bs-target="#updateHistoryModal">{{ trans2('button.history') }}</button>
                </div>
            </div>
        </div>
        <livewire:common.tracking-history-modal :function="$func" :id="data_get($customer, 'id')" />
    </div>
@endsection

