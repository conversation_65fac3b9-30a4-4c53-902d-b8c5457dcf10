{"__meta": {"id": "01K0BY0GSF587H9ZE8X464DZES", "datetime": "2025-07-17 19:13:50", "utime": **********.000463, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 20, "messages": [{"message": "[19:13:49] LOG.debug: (Time: 01.74) SQL: select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '0' limit 1 {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.920468, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 02.17) SQL: select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.940125, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.43) SQL: select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.943111, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.945907, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.39) SQL: select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.948449, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.46) SQL: select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.951179, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.953868, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.92) SQL: select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.957302, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.47) SQL: select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.960569, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.48) SQL: select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.963414, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.49) SQL: select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.966237, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.45) SQL: select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.968872, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.40) SQL: select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.97142, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.47) SQL: select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.974623, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.41) SQL: select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.977305, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.38) SQL: select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.97991, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 01.10) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.984182, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.34) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.986694, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.36) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.989081, "xdebug_link": null, "collector": "log"}, {"message": "[19:13:49] LOG.debug: (Time: 00.52) SQL: select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '0' {\n    \"path\": \"admin.sql\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.992351, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.705522, "end": **********.000495, "duration": 0.29497289657592773, "duration_str": "295ms", "measures": [{"label": "Booting", "start": **********.705522, "relative_start": 0, "end": **********.888676, "relative_end": **********.888676, "duration": 0.*****************, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.888686, "relative_start": 0.*****************, "end": **********.000496, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.897823, "relative_start": 0.*****************, "end": **********.89958, "relative_end": **********.89958, "duration": 0.001756906509399414, "duration_str": "1.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.998325, "relative_start": 0.****************, "end": **********.998924, "relative_end": **********.998924, "duration": 0.000598907470703125, "duration_str": "599μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x livewire.admin.dashboard.review-table", "param_count": null, "params": [], "start": **********.996156, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\ladybird\\resources\\views/livewire/admin/dashboard/review-table.blade.phplivewire.admin.dashboard.review-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fdashboard%2Freview-table.blade.php&line=1", "ajax": false, "filename": "review-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.dashboard.review-table"}]}, "route": {"uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "middleware": "web", "as": "livewire.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>"}, "queries": {"count": 20, "nb_statements": 20, "nb_visible_statements": 20, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01284, "accumulated_duration_str": "12.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `administrators` where `id` = 1 and `administrators`.`del_flag` = '\\'0\\'' limit 1", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Http\\Middleware\\Authenticate.php", "line": 21}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9188411, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "local-ladybird", "explain": null, "start_percent": 0, "width_percent": 13.551}, {"sql": "select count(*) as aggregate from `applications` where `status` = 1 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.9381218, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 13.551, "width_percent": 16.9}, {"sql": "select count(*) as aggregate from `applications` where `status` = 2 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.942801, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 30.452, "width_percent": 3.349}, {"sql": "select count(*) as aggregate from `applications` where `status` = 3 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [3, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.9456198, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 33.801, "width_percent": 3.193}, {"sql": "select count(*) as aggregate from `applications` where `status` = 4 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [4, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.94816, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 36.994, "width_percent": 3.037}, {"sql": "select count(*) as aggregate from `applications` where `status` = 5 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.950848, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 40.031, "width_percent": 3.583}, {"sql": "select count(*) as aggregate from `applications` where `status` = 6 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.953526, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 43.614, "width_percent": 3.505}, {"sql": "select count(*) as aggregate from `applications` where `status` = 7 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.956759, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 47.118, "width_percent": 7.165}, {"sql": "select count(*) as aggregate from `applications` where `status` = 8 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [8, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.960272, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 54.283, "width_percent": 3.66}, {"sql": "select count(*) as aggregate from `applications` where `status` = 9 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [9, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.963051, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 57.944, "width_percent": 3.738}, {"sql": "select count(*) as aggregate from `applications` where `status` = 10 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [10, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.965878, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 61.682, "width_percent": 3.816}, {"sql": "select count(*) as aggregate from `applications` where `status` = 11 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.968539, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 65.498, "width_percent": 3.505}, {"sql": "select count(*) as aggregate from `applications` where `status` = 12 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [12, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.971137, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 69.003, "width_percent": 3.115}, {"sql": "select count(*) as aggregate from `applications` where `status` = 13 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.974297, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 72.118, "width_percent": 3.66}, {"sql": "select count(*) as aggregate from `applications` where `status` = 14 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [14, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.9770548, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 75.779, "width_percent": 3.193}, {"sql": "select count(*) as aggregate from `applications` where `status` = 15 and `applications`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 12}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.97963, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ApplicationRepository.php:426", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationRepository.php", "line": 426}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationRepository.php&line=426", "ajax": false, "filename": "ApplicationRepository.php", "line": "426"}, "connection": "local-ladybird", "explain": null, "start_percent": 78.972, "width_percent": 2.96}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (2, 3, 4, 5) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [2, 3, 4, 5, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.983198, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 81.931, "width_percent": 8.567}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (6, 8, 9, 10, 11) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [6, 8, 9, 10, 11, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.98645, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 90.498, "width_percent": 2.648}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (7, 12, 13) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [7, 12, 13, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.988817, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 93.146, "width_percent": 2.804}, {"sql": "select count(*) as aggregate from `application_inspection_status` where `status` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15) and `to_shop_id` is null and `application_inspection_status`.`del_flag` = '\\'0\\''", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, "'0'"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Services\\DashboardService.php", "line": 17}, {"index": 18, "namespace": null, "name": "app/Livewire/Admin/Dashboard/ReviewTable.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Livewire\\Admin\\Dashboard\\ReviewTable.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\ladybird\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.991959, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApplicationInspectionRepository.php:35", "source": {"index": 16, "namespace": null, "name": "app/Repositories/ApplicationInspectionRepository.php", "file": "C:\\xampp\\htdocs\\ladybird\\app\\Repositories\\ApplicationInspectionRepository.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FRepositories%2FApplicationInspectionRepository.php&line=35", "ajax": false, "filename": "ApplicationInspectionRepository.php", "line": "35"}, "connection": "local-ladybird", "explain": null, "start_percent": 95.95, "width_percent": 4.05}]}, "models": {"data": {"App\\Models\\Administrator": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fapp%2FModels%2FAdministrator.php&line=1", "ajax": false, "filename": "Administrator.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.dashboard.review-table #6IypQuGrTdfkSuR7j3lr": "array:4 [\n  \"data\" => array:5 [\n    \"shopId\" => \"\"\n    \"shopBrandId\" => \"\"\n    \"perPage\" => 20\n    \"guest\" => false\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.dashboard.review-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Dashboard\\ReviewTable\"\n  \"id\" => \"6IypQuGrTdfkSuR7j3lr\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_reqCode": "/livewire/update_**********", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/management\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fladybird%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "298ms", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1871454929 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1871454929\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2124129037 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"421 characters\">{&quot;data&quot;:{&quot;shopId&quot;:&quot;&quot;,&quot;shopBrandId&quot;:&quot;&quot;,&quot;perPage&quot;:20,&quot;guest&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;6IypQuGrTdfkSuR7j3lr&quot;,&quot;name&quot;:&quot;admin.dashboard.review-table&quot;,&quot;path&quot;:&quot;management&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;props&quot;:[&quot;shopId&quot;,&quot;shopBrandId&quot;],&quot;errors&quot;:[],&quot;locale&quot;:&quot;ja&quot;},&quot;checksum&quot;:&quot;0303ac8e657a5390d0065bc728788ee6360657fd29fe98697b5887c5fb7ecc4c&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"284 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbeyJzaG9wQnJhbmRJZCI6IiIsInNob3BJZCI6IiJ9LHsicyI6ImFyciJ9XX0sIm1lbW8iOnsiaWQiOiIwV2tKTlo2bDV0UTZqRVZtYzJ6ZiIsIm5hbWUiOiJfX21vdW50UGFyYW1zQ29udGFpbmVyIn0sImNoZWNrc3VtIjoiMTYwYmVmODNjMmE1MDBhYTE3MGM4ZjIzZDJjNDM1OWI4Y2M3YWVhMjAyZGViY2MzMjM1MjNkMTRhNmRkMjBlMSJ9</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124129037\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-388425834 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">924</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1122 characters\">cookie_per_page=eyJpdiI6IkV4b3ZySWVaTjI0czgxRmpycEc3MHc9PSIsInZhbHVlIjoiQUk4ak14MytpVTdFUjBubkplSWZJK25Rc0FWbXV5K2pabU5nU0tuVUNycWx3UXpoZjNqdW9NNWlzcmJ3bmsvOCIsIm1hYyI6IjI5ZjgzNWFiMDIxNTg3MDc3MGU4YzFlNDA3OGVjNDg4N2ExODA5MmU2NDJjMzMyMzdmMWMxZDVkY2RjMDA1ZDMiLCJ0YWciOiIifQ%3D%3D; remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImlqR1hKc3kxU295eW9idG9McWtTeVE9PSIsInZhbHVlIjoiWkQ5SzNBUnA4SlhaUHRZLzk2Rm5PMTVrWm1IYUF0Y2xkanBGSWphOEhkbVZ0QUlTRzJRUG5SczFsNzZMc3VSdHlDSUtIOHdEWmRmbWV4Y2tzQWo2VzlEVVk3SEJIa2tHdjlOcDVMcWw2aXVsQ3pZOHd6R3pBRXA1SklMYzlieUZuTVU0ejVqVkRKU01iUjV5NzdTN1BnPT0iLCJtYWMiOiJmZThlMThjNTZmMjk0ZGY4NWU5NGMyZmYxMWJiMzJkMzhjNGYwMGQ4ZGRjNGU0MTlmYzc0N2RjNDM0ZTZkNmIwIiwidGFnIjoiIn0%3D; ladybird_session=tGFewrUFVXwdbXowWrlw5IwCNUgVWIGWxWStuDZg; XSRF-TOKEN=eyJpdiI6InFGSEd6c2dJM2ZTajlJbWVqa0RDK0E9PSIsInZhbHVlIjoieUVHMDlyaDRRbjAvcnkzZFpSM0FRRW5oczhCNHRxK3Q4dVpYMXdGaDdOY2V5L1gxaHdrb2Ixb09KdVdkeVFpWWYvbEJaK2w3RmMzNlN6WVFab2RzL0xsOWF2YnhYbmpHRG1PRWhnVllXOG1zVm1nYW5LTkNlYTVDT1ZYMS9zU0siLCJtYWMiOiI4ZTBiZmE2ZDZlNGYxZmQ1NDI0NDMyZWEyMDczM2ZhZDMzMmU5OThlNTVjMTBkZDc1YjcyN2I3MDNhNjYzYmJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-388425834\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_per_page</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1||$2y$10$3VGq3zbUWvkQWzIjL1aEouaGMFDiMjy4OBktNE.Ow76IvpQw29yJS</span>\"\n  \"<span class=sf-dump-key>ladybird_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-779074505 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 10:13:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-779074505\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-906528552 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rRBzKuhrz2izE31VlmeYRCtaoTOnHQLNHuPaki12</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_reqCode</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/livewire/update_**********</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906528552\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}