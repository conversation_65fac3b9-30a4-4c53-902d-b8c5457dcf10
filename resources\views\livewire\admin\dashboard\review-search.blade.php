<?php

use function Livewire\Volt\{state};
use function Livewire\Volt\{computed};
use App\Services\ShopService;
use App\Services\ShopBrandService;

state([
    'shopId' => '',
    'shopBrandId' => '',

    'listShopBrands' => app(ShopBrandService::class)->getAllShopBrandsForBrandAdmin() ?? [],
    'listShops' => app(ShopService::class)->getAllShops() ?? [],
])->url(history: true);

$search = function () {
    $this->dispatch('reset-page');
};
?>

<!-- search -->
<div>
    <div class="mb-0 setting-form-control" wire:ignore>
        @if (getCurrentUser()->isAdmin())
            <select name="" class="form-select2" style="width: 250px" wire:model='shopId'>
                <option value="0">{{ trans2('screens.dashboard.admin_base') }}</option>
                @foreach ($this->listShops as $shop)
                    <option value="{{ data_get($shop, 'id') }}">{{ data_get($shop, 'name') }}</option>
                @endforeach
            </select>
        @elseif (getCurrentUser()->isBrand())
            <select name="" class="form-select2" style="width: 250px" wire:model='shopBrandId'>
                <option value="0">{{ trans2('screens.dashboard.admin_base') }}</option>
                @foreach ($this->listShopBrands as $shopBrand)
                    <option value="{{ data_get($shopBrand, 'id') }}">{{ data_get($shopBrand, 'name') }}</option>
                @endforeach
            </select>
        @endif
    </div>

    <livewire:admin.dashboard.review-table lazy :$shopBrandId :$shopId />

</div>
@script
    <script>
        document.addEventListener('livewire:initialized', () => {
            initSelect2Binding();
        });

        function initSelect2Binding() {

            document.querySelectorAll('select.form-select2').forEach(function(el) {

                if (!$(el).hasClass('select2-hidden-accessible')) {
                    $(el).select2().on('change', function(e) {
                        const value = $(this).val();
                        const model = el.getAttribute('wire:model');

                        if (model) {
                            const componentId = el.closest('[wire\\:id]').getAttribute('wire:id');
                            Livewire.find(componentId).set(model, value);

                            Livewire.find(componentId).call('search');

                        }
                    });
                }
            });
        }
    </script>
@endscript
