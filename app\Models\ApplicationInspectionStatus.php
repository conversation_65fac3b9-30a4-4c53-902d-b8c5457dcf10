<?php

namespace App\Models;

use App\Enums\ApplicationInspectionCancelStatusEnum;
use App\Enums\ApplicationInspectionStatusEnum;
use App\Enums\ApplicationInspectionToShopEnum;
use App\Models\Base\CustomModel;

class ApplicationInspectionStatus extends CustomModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */

    protected $table = 'application_inspection_status';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'application_id',
        'to_shop_id',
        'from_shop_id',
        'comment',
        'file_name',
        'file_url',
        'status',
        'cancel_status',
    ];

    public function application()
    {
        return $this->belongsTo(Application::class, 'application_id', 'id');
    }

    public function fromShop()
    {
        return $this->belongsTo(Shop::class, 'from_shop_id', 'id');
    }

    public function toShop()
    {
        return $this->belongsTo(Shop::class, 'to_shop_id', 'id');
    }

    protected $casts = [
        'status' => ApplicationInspectionStatusEnum::class,
        'cancel_status' => ApplicationInspectionCancelStatusEnum::class,
    ];
}
