<div
    x-data="{ messages: [] }"
    x-on:show-modal-errors.window="messages = $event.detail; new bootstrap.Modal($refs.csvMessageModal).show()"
>
    <div class="modal fade" id="csvMessageModal" tabindex="-1" x-ref="csvMessageModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <ul class="mb-0 ps-3 text-danger text-start message-errors">

                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary mx-4" data-bs-dismiss="modal">閉じる</button>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    document.addEventListener('show-modal-errors', function(e) {
        const dataMessages = e?.detail[0] ?? [];

        const messagesEl = document.querySelector('.message-errors');

        let tmpLi = '';
        dataMessages.forEach(value => {
            tmpLi += `<li>${value}</li>`;
            console.log(tmpLi);

        });

        messagesEl.innerHTML = tmpLi;
    })
</script>

