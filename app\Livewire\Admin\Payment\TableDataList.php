<?php

namespace App\Livewire\Admin\Payment;

use App\Enums\PaymentStatusEnum;
use App\Livewire\Base\BaseDataListComponent;
use App\Services\LoanScheduleService;
use App\Services\CustomerService;
use App\Services\CsvService;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;

class TableDataList extends BaseDataListComponent
{
    public $monthIndex;
    public $brand_id;
    public $shop_id;
    public $item_type_id;
    public $course_id;
    public $payment_plan_date_from;
    public $payment_plan_date_to;
    public $payment_status;
    public $focus_brand_id;
    public $isSearch;


    protected $listeners = ['refresh-contract' => '$refresh'];

    #[On('search-data-list')]
    public function searchData($data)
    {
        $this->monthIndex = $data['month_index'] ?? '';
        $this->brand_id = $data['brand_id'] ?? '';
        $this->shop_id = $data['shop_id'] ?? '';
        $this->item_type_id = $data['item_type_id'] ?? '';
        $this->course_id = $data['course_id'] ?? '';
        $this->payment_plan_date_from = $data['payment_plan_date_from'] ?? '';
        $this->payment_plan_date_to = $data['payment_plan_date_to'] ?? '';
        $this->payment_status = $data['payment_status'] ?? '';
        $this->focus_brand_id = $data['focus_brand_id'] ?? '';
        $this->isSearch = true;
    }

    public function render()
    {
        $dataSearch = [
            'month_index' => $this->monthIndex,
            'brand_id' => $this->brand_id,
            'shop_id' => $this->shop_id,
            'item_type_id' => $this->item_type_id,
            'course_id' => $this->course_id,
            'payment_plan_date_from' => $this->payment_plan_date_from,
            'payment_plan_date_to' => $this->payment_plan_date_to,
            'payment_status' => $this->payment_status,
            'focus_brand_id' => $this->focus_brand_id,
        ];
        $dataList = null;
        if ($this->isSearch) {
            $dataList = app(LoanScheduleService::class)->search($dataSearch, $this->perPage);
        }
        return $this->viewLivewireAdmin('payment.table-data-list', [
            'tableData' => $dataList,
        ]);
    }

    public function notifyVisible()
    {
        $this->dispatch('scroll-to', ['id' => 'section']);
    }
}
