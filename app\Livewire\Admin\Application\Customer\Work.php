<?php

namespace App\Livewire\Admin\Application\Customer;

use App\Enums\CustomerTabEnum;
use App\Livewire\Admin\Application\Forms\WorkForm;
use App\Livewire\Base\BaseAdminPageComponent;
use App\Repositories\ApplicationRepository;
use App\Repositories\CustomerRepository;
use App\Services\CustomerService;
use App\Services\PrefService;
use App\Services\ToastService;
use App\Traits\HandlesApplicationCustomer;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;

#[Layout('components.layouts.application-layout')]
class Work extends BaseAdminPageComponent
{
    use HandlesApplicationCustomer;

    public WorkForm $workForm;

    public function mount($customer_id, $application_id)
    {
        $this->page = CustomerTabEnum::WORK;
        $this->pageTitle .= '｜' . trans2('project_name');
        $this->initCustomerAndApplication($customer_id, $application_id);

        $customer = app(CustomerRepository::class)->getInfoApplicationCustomer($customer_id);

        if ($customer) {
            $this->workForm->fillFromCustomer($customer);
        }
    }

    public function validateSave()
    {
        $this->workForm->validate();
        $this->dispatch('app-work-store', $this->workForm);
    }

    #[On('app-work-store')]
    public function store()
    {
        $params = $this->workForm->all();
        
        $customer = app(CustomerService::class)->update($this->customerId, $params);

        if ($customer) {
            app(ToastService::class)->createSuccess();
            // redirect to tab guarantor
            return redirect()->route('admin.customer.application.setup.guarantor', [
                'application_id' => $this->applicationId,
                'customer_id' => $customer->id,
            ]);
        }

        app(ToastService::class)->createError();
    }

    public function render()
    {
        $this->dispatch('init-select2');
        return $this->viewLivewireAdmin('application.customer.work', [
            'listPrefs' => app(PrefService::class)->getAllPrefs(),
        ]);
    }
}
